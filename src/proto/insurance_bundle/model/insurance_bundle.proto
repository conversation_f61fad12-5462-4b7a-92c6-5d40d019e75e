syntax = "proto3";
package model;

import "common/time.proto";
import "insurance_core/insured.proto";
import "insurance_core/form.proto";
import "insurance_bundle/model/policy.proto";
import "insurance_core/insurance_carrier.proto";
import "insurance_core/seller.proto";
import "insurance_core/program_type.proto";

import "google/protobuf/timestamp.proto";

option go_package = "nirvanatech.com/nirvana/insurance-bundle/model";

enum LimitGrouping {
    LimitGrouping_Invalid = 0;
    LimitGrouping_Single = 1;
    LimitGrouping_Combined = 2;
}

enum SortOrder {
    SortOrder_Invalid = 0;
    SortOrder_Asc = 1; // Ascending order
    SortOrder_Desc = 2; // Descending order
}

message Pagination {
    // Cursor for pagination: base64 encoded string "<created_at>_<external_id>"
    // As IB is immutable, created_at with a filter of state != 'Stale'
    // (which is added by default in the list query) will give us the latest bundle,
    // which is why it is used as cursor for pagination. External_id is clubbed with
    // it as two insurance_bundle can have same created_at.
    // If unset, returns the very first page with next_cursor in response.
    optional string cursor = 4;

    // Max number of results to return
    optional int32 page_size = 5;

    // Default sort order is descending by created_at, external_id
    optional model.SortOrder sort_order = 6;
}

// Limit is the maximum amount that an insurer can be called upon to pay for based on the applicable criteria.
// The limit can be of multiple types, but the most common types are Single and Combined.
// The limit model purposefully doesn't define any rule for limit resolution so that the logic of interpreting the
// relationship between limits is decoupled from the model.
// Note: This model can't handle the case wherein a combined limit has different dimension values for different
// sub-coverages. For example, a combined limit of 1M$ on coverages {A, B and C} can have the following dimensions:
// {A, B} have the exposure of Vehicle but {C} has the exposure of Person.
// However, given that we don't anticipate this case because such a limit structure is unheard of, we do not want to
// add complexity to the model to handle this case. If such a case arises in the future, the model can be updated to
// handle this case.
// There are other dimensions to a limit like cadence, scope and exposure. But those are not needed for now and
// can be added later.
message Limit {
    // id of a limit is used to uniquely identify a limit within a policy.
    // For example, BodilyInjuryPerPerson and BodilyInjuryPerAccident are limit ids used to identify two different\
    // limits involving the same sub-coverage BodilyInjury.
    // If limits are varying for the same subCoverageIds on the basis of exposure entities, then ensure that
    // the id is unique for each of those limits.
    // For example, if there are two limits for the same subCoverageIds: Coll and Comp that vary on the basis of
    // exposure entity vehicle, the the id of the limits can be Coll_Vehicle_VIN1, Comp_Vehicle_VIN2, etc.
    // TODO: Remove the id field in the future.
    // Reliance on clients to set the id of the limits is not a good idea because with increasing number of
    // axes along which limits can vary, it can get confusing for clients to set the id of the limits.
    // We should probably follow the same pattern as the deductibles of not having an id field.
    // This is a bigger change and can be done while doing sub-coverage modelling.
    // Why was the ID field added in the first place?
    // Answer: To support the use-case of modelling varying limits for the same subCoverageIds along axes that are
    // not yet supported. For example, the BI limit per accident is different from BI limit per person.
    // Till we don't support this distinction in the limit model(probably by having axes like cadence, scope, etc),
    // the client can send different IDs for the two BI limits. For instance, BILimitPerPerson and BILimitPerAccident.
    string id = 1;
    // displayName is the human readable name of the limit. For example, "Bodily Injury Per Person"
    // or "Bodily Injury Per Accident". This is only to be used for display/presentation purposes.
    string displayName = 2;
    // For Single limit, the subCoverageIds list will have only one element.
    // For example, BodilyInjuryPerPerson has a single sub-coverage: BodilyInjury.
    // For Combined limit, the subCoverageIds list will have multiple elements.
    // For example, CombinedSingleLimit has two sub-coverages: BodilyInjury and PropertyDamage.
    repeated string subCoverageIds = 3;
    double amount = 4;
    LimitGrouping grouping = 5;
    // ──────────────────────────────────────────────────────────────────────────────
    //  Exposure entities identify the real-world items (vehicle, terminal, etc.)
    //  to which this limit applies.  Each entity is uniquely addressed by the
    //  pair (type, id).
    //
    //  Example: The limit for CargoAtScheduledTerminals sub-cov in fleet might vary
    //  by terminal, so the same sub-coverage CargoAtScheduledTerminals could have
    //  different values for terminal A vs. terminal B.
    //
    //  Limits can vary along two independent entity axes for the same set of sub-coverages:
    //      • Entity *type* (vehicle vs. terminal vs. …)
    //      • Entity *id*   (VIN123 vs. VIN456, Terminal-42 vs. Terminal-77, …)
    //
    //  That gives four theoretical patterns for the same set of sub-coverages across limits:
    //
    //    ┌───┬────────────┬─────────┬──────────────────────────────────────────────┐
    //    │ # │ Varies by  │ Varies  │ Support status                               │
    //    │   │ type?      │ by id?  │ (list<ExposureEntity> behaviour)             │
    //    ├───┼────────────┼─────────┼──────────────────────────────────────────────┤
    //    │ 1 │  No        │  No     │ Supported. List can contain one entry.       │
    //    │ 2 │  No        │  Yes    │ Supported. Provide one entry per id of       │
    //    │   │            │         │ the *same* type.                             │
    //    │ 3 │  Yes       │  No     │ The case is invalid by definition (same id   │
    //    │   │            │         │ across types has no meaning)                 │
    //    │ 4 │  Yes       │  Yes    │ Not yet supported – list MUST be empty.      │
    //    │   │            │         │ (Would require up to |types|×|ids| entries.) │
    //    └───┴────────────┴─────────┴──────────────────────────────────────────────┘
    //  This can be summarised as that for the same set of sub-coverages:
    //    - Variation by type is supported.
    //    - If type is not varied, then ids must be varied.
    //
    //  Variations that are “not yet supported” may be enabled by the sub-coverage
    //  remodelling project.
    //
    // ──────────────────────────────────────────────────────────────────────────
    //  Guidance on modelling limits that vary by exposure entities
    //  -----------------------------------------------------------------------
    //  A single `Limit` message represents one monetary amount that applies to
    //  *exactly* the set of `exposureEntities` listed in the message (for the supported cases). If the
    //  same conceptual limit needs different values for different exposure
    //  entities, model this by **creating one `Limit` per amount / entity set
    //  combination**.
    //
    //  In other words: do **not** try to capture multiple differing amounts in
    //  one `Limit`. Instead emit multiple `Limit` messages that share the same
    //  logical `id` & `subCoverageIds`, but differ in their `amount` and
    //  `exposureEntities`.
    //
    //  Example – Per-terminal cargo limit
    //  ---------------------------------
    //  The sub-coverage `CargoAtScheduledTerminals` requires a per-terminal
    //  limit.  Terminal "T-42" is covered up to 1 000 000 USD, whereas terminal
    //  "T-77" is covered only up to   750 000 USD. Represent this as **two**
    //  separate limits:
    //
    //      limits = [
    //        {
    //          id: "CargoAtScheduledTerminals",
    //          subCoverageIds: ["CargoAtScheduledTerminals"],
    //          amount: 1_000_000,
    //          exposureEntities: [
    //            { id: "T-42", type: EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO }
    //          ]
    //        },
    //        {
    //          id: "CargoAtScheduledTerminals",
    //          subCoverageIds: ["CargoAtScheduledTerminals"],
    //          amount: 750_000,
    //          exposureEntities: [
    //            { id: "T-77", type: EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO }
    //          ]
    //        }
    //      ]
    //
    //  Each limit cleanly expresses *one* amount for *one* entity (or group of
    //  entities sharing the same amount). Consumers can then pick the correct
    //  limit by matching the exposure entity at runtime.
    // ──────────────────────────────────────────────────────────────────────────
    repeated ExposureEntity exposureEntities = 6;
}

message InsuranceBundleMetadata {
    // rootBindableSubmissionId is the bindableSubmissionId of the very first submission that created the
    // insurance bundle.
    string rootBindableSubmissionId = 1;
    // rootApplicationId is the applicationId of the very first application that created the insurance bundle.
    string rootApplicationId = 2;
    // endorsementApplicationIDs stores the list of all endorsement applications that have been applied to the insurance
    // bundle.
    repeated string endorsementApplicationIDs = 5;
    // endorsementApplicationIDDelta stores the endorsement application ID that has been applied to the insurance
    // bundle since the last time the insurance bundle was updated. Essentially, this is the endorsement application
    // ID that was applied to the previous version of the insurance bundle to get the current version of the insurance
    // bundle.
    optional string endorsementApplicationIDDelta = 6;
}

message InsuranceBundleSegment {
    common.Interval interval = 1;

    insurance_core.Insured primaryInsured = 2;

    // Policies in the InsuranceBundleSegment. The key is the policyNumber.
    map<string, Policy> policies = 3;

    CoverageCriteria coverageCriteria = 4;
    string id = 5;
}

// Deductible is defined as the amount that will deduct from the loss before paying up a claim.
// Deductible can be of multiple types, but the most common types are Single and Combined.
// The deductible model purposefully doesn't define any rule for deductible resolution so that the logic of interpreting
// the relationship between deductibles can be decoupled from the model.
// The model also allows for the following deductible types to be supported in the future:
// 1. Embedded Deductibles: This is when a combination of individual and combined deductibles are applicable for a
//    sub-coverage. Whichever is met first (individual or combined) triggers coverage for that service. Since the logic
//    of resolution is not defined in the model, the embedded deductible can be supported by adding the interpretation
//    logic in the future/keeping the interpretation logic in a different domain.
// 2. Vanishing Deductibles: This is when the deductible amount reduces over time. The usual procedure is to reduce the
//    deductible amount by a fixed percentage for every policy period the policyholder goes without a claim. Since the
//    deductible changes only after policy expiry, the vanishing requirement need not modelled as a data point in the
//    model. It can be added in later iterations if the requirement arises.
//    However, if the vanishing happens within the policy period, then the use-case can be supported by treating
//    it as an endorsement. The exact implementation of the vanishing deductible can be decided whenever the such a
//   requirement arises.
// 3. Split Deductibles: This is when different deductible amounts are applicable for a sub-coverage for different
//    types of claims. This can be modelled by introducing the dimension that varies the deductible amount and storing
//    each of them as separate deductibles.
message Deductible {
    // For Single deductible, the subCoverageIds list will have only one element.
    // A subCoverageId might theoretically be a part of multiple deductibles. However, in practice, a subCoverageId
    // is usually a part of only one deductible. But the model has been kept flexible to accommodate the theoretical
    // possibility.
    repeated string subCoverageIds = 1;
    double amount = 2;
    // ──────────────────────────────────────────────────────────────────────────────
    //  Exposure entities tell us *which* real-world items (vehicles, terminals, …)
    //  a deductible applies to. The conceptual axes (vary by entity *type* and/or
    //  *id*) are identical to the four patterns documented for `Limit.entities`
    //  above—see that table for the full matrix.
    //
    //  Current support status for deductibles for the same set of sub-coverages:
    //      • Pattern 1  (no variation by type *or* id) – *✔supported* →
    //        only a single exposure entity is supported.
    //      • Pattern 2  (same type, different ids)     – *✔supported* →
    //        Example target use-case: per-vehicle Collision & Comp
    //        deductibles in Business Auto.
    //      • Pattern 3  (different types, same id)     – invalid by definition.
    //      • Pattern 4  (different types, different ids) – *not yet supported* →
    //        list MUST be empty.
    //
    //  The sub-coverage remodelling project may enable the unsupported patterns.
    // ──────────────────────────────────────────────────────────────────────────────

    // ──────────────────────────────────────────────────────────────────────────
    //  Guidance on modelling deductibles that vary by exposure entities
    //  -----------------------------------------------------------------------
    //  A single `Deductible` message represents one monetary amount that applies to
    //  *exactly* the set of `exposureEntities` listed in the message (for the supported cases).
    //  If the same conceptual deductible needs different values for different exposure entities,
    //  model this by **creating one `Deductible` per amount / entity set combination**.
    //
    //  Example – Per-vehicle Collision deductible
    //  -----------------------------------------
    //  Suppose `Collision` requires different deductibles per vehicle:
    //      • Vehicle VIN123 – $1,000
    //      • Vehicle VIN456 – $2,000
    //  Represent this as **two** separate deductible entries:
    //
    //      deductibles = [
    //        {
    //          subCoverageIds: ["Collision"],
    //          amount: 1_000,
    //          exposureEntities: [
    //            { id: "VIN123", type: EXPOSURE_ENTITY_TYPE_VEHICLE }
    //          ]
    //        },
    //        {
    //          subCoverageIds: ["Collision"],
    //          amount: 2_000,
    //          exposureEntities: [
    //            { id: "VIN456", type: EXPOSURE_ENTITY_TYPE_VEHICLE }
    //          ]
    //        }
    //      ]
    //
    //  Each deductible expresses one amount for a subset of entities, enabling consumers to
    //  select the correct deductible at runtime.
    // ──────────────────────────────────────────────────────────────────────────
    repeated ExposureEntity exposureEntities = 3;
    // A deductible type field can be added in the future to support different types of deductibles.
}

// ExposureEntity is used to represent the entity that is exposed to the risk and is covered by one or multiple sub-coverages.
// (id, type) pair is used to uniquely identify an exposure entity.
// Ex: A Vehicle with id "VIN123" and type EXPOSURE_ENTITY_TYPE_VEHICLE
// Users are free to define their own exposure entities as long as they follow the (id, type) pair uniqueness. They are
// also free to select any id format(uuid, numeric, alphanumeric, string etc).
message ExposureEntity {
    string id = 1;
    ExposureEntityType type = 2;
}

enum ExposureEntityType {
    EXPOSURE_ENTITY_TYPE_INVALID = 0;
    EXPOSURE_ENTITY_TYPE_VEHICLE = 1; // id: vin
    // Even though the exposed entity is the cargo, the cargo in this is characterised by the terminal it is
    // being stored at. Therefore, the id of the terminal is used as the id of the cargo.
    EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO = 2; // id: TerminalLocation.Address.Street
    // Even though the exposed entity is the cargo, the cargo in this is characterised by the shipper to which
    // it is being shipped. Therefore, the id of the shipper is used as the id of the cargo.
    EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO = 3; // id: TODO when named shipper limit is implemented
}

// CombinedDeductible holds the list of subCoverageIds for which the deductible is combined.
// For example, if Coll and CargoColl have deductibles of 1k and 2k respectively, and their deductibles are combined,
// then the combined deductible will have subCoverageIds as [Coll, CargoColl]. Claim resolution will work like this
// in such a case:
// Scenario 1: If a customer files for a claim for Coll, then the deductible of 1k will be applied.
// Scenario 2: If a customer files for a claim for CargoColl, then the deductible of 2k will be applied.
// Scenario 3: If a customer files for a claim for both Coll and CargoColl, then the higher value of the two(2k in this
// case) will be applied.
// Note that we're not storing the resolution rules in the insurance bundle. That's left to the clients to interpret.
message CombinedDeductible {
    // For Combined deductible, the subCoverageIds list will have multiple elements.
    repeated string subCoverageIds = 1;
}

// CoverageCriteria holds the criteria related to coverage. These criteria are used to determine the terms and conditions
// under the restrictions of which the coverages should be evaluated.
message CoverageCriteria {
    repeated Limit limits = 1;

    repeated Deductible deductibles = 2;

    repeated CombinedDeductible combinedDeductibles = 3;
}

// InsuranceBundleState is derived using logic that is program specific. For example, the Non-Fleet Admitted program
// has configured the state of the insurance bundle to be the same as the state of their Commercial Auto policy.
// Therefore, the interpretation of the meaning of each state is also program specific.
enum InsuranceBundleState {
    InsuranceBundleState_Invalid = 0;
    InsuranceBundleState_Active = 1;
    InsuranceBundleState_Inactive = 2;
    InsuranceBundleState_Expired = 3;
    InsuranceBundleState_PendingCancellation = 4;
    InsuranceBundleState_Cancelled = 5;
    InsuranceBundleState_Stale = 6;
}

message InsuranceBundle {
    // A unique identifier for the insurance bundle. This id remains constant across insurance bundle versions.
    string externalId = 1;

    // A unique UUID for the insurance bundle which is internal to the system. This would be a uuid and can be stored
    // by the client for future reference.
    string internalId = 2;

    // The version of the insurance bundle. Each time the insurance bundle is updated, the version is incremented.
    // The increment logic is not something to be relied upon and can change over time.
    // Base version of the insurance bundle is 0.
    int64 version = 3;

    // The default insurance carrier for the insurance bundle. This can be overridden at a policy level.
    insurance_core.InsuranceCarrier defaultCarrier = 4;

    // The default seller for the insurance bundle. This can be overridden at a policy level.
    insurance_core.SellerInfo defaultSeller = 5;

    // The default effective duration for the insurance bundle. This can be overridden at a policy level.
    common.Interval defaultEffectiveDuration = 6;

    insurance_core.ProgramType programType = 7;

    InsuranceBundleMetadata metadata = 8;

    insurance_core.FormInfo formInfo = 9;

    // An InsuranceBundle is made up of multiple InsuranceBundleSegments. Each segment represents a period of time
    // for which the insurance bundle is valid. The segments are ordered by the start time of the interval.
    // The segments are non-overlapping and continuous. This means that for a segment with index i, the end time of
    // the segment is the same as the start time of the segment with index i+1.
    repeated InsuranceBundleSegment segments = 10;

    google.protobuf.Timestamp createdAt = 11;
    google.protobuf.Timestamp updatedAt = 12;

    InsuranceBundleState state = 13;

    insurance_core.CarrierAdmittedType carrierAdmittedType = 14;
}

// CondensedInsuranceBundle is a condensed form of the InsuranceBundle. It is used to represent the InsuranceBundle
// in a more compact form. This is useful when the full details of the InsuranceBundle are not needed(for eg in a List
// API).
message CondensedInsuranceBundle {
    string externalID = 1;
    string internalID = 2;
    insurance_core.Insured primaryInsured = 3;
    common.Interval defaultEffectiveDuration = 4;
    InsuranceBundleState state = 5;
    google.protobuf.Timestamp createdAt = 6;
    google.protobuf.Timestamp updatedAt = 7;
}