package client

import (
	"context"
	"strings"
	"time"

	"nirvanatech.com/nirvana/rating/mvr"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"nirvanatech.com/nirvana/common-go/us_states"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	commonib "nirvanatech.com/nirvana/api-server/handlers/common/ib"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	endreviewenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/nonfleet/model"
	intakeoapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

// updateMVRAction updates or creates a new MVR pull action in the endorsement review.
func (i *Impl) updateMVRAction(ctx context.Context, reviewID uuid.UUID) error {
	updateFunc := func(review *endorsementreview.Review) *endorsementreview.Review {
		action := findAction(review.Actions, endreviewenums.EndorsementReviewActionTypePullMVR)
		if action != nil {
			action.LastModifiedAt = time.Now()
			return review
		}

		newAction := endorsementreview.EndorsementReviewAction{
			Action:         endreviewenums.EndorsementReviewActionTypePullMVR,
			LastModifiedAt: time.Now(),
		}
		review.Actions = append(review.Actions, newAction)
		return review
	}

	err := i.Deps.EndorsementReviewWrapper.Update(ctx, reviewID, updateFunc)
	if err != nil {
		return errors.Wrapf(err, "failed to update endorsement review %s", reviewID)
	}
	return nil
}

// getNewDriversAddedInEndReq retrieves the list of newly added drivers from an endorsement request.
func (i *Impl) getNewDriversAddedInEndReq(ctx context.Context, requestID uuid.UUID) ([]application.DriverBasicDetails, error) {
	driverChanges, err := endorsement.GetEndorsementRequestDriverChanges(
		ctx,
		i.Deps.EndorsementRequestManager,
		i.Deps.InsuranceBundleManagerClient,
		requestID,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get driver changes for endorsement request %s", requestID)
	}

	addedDrivers := make([]application.DriverBasicDetails, 0)
	for _, driverChange := range driverChanges {
		if driverChange.ChangeType == intakeoapi.DriverChangeChangeTypeAdded {
			addedDrivers = append(addedDrivers, application.DriverBasicDetails{
				FirstName:     driverChange.Driver.FirstName,
				LastName:      driverChange.Driver.LastName,
				LicenseState:  driverChange.Driver.LicenseState,
				LicenseNumber: driverChange.Driver.LicenseNumber,
				DateOfBirth:   driverChange.Driver.DateOfBirth.Time,
				DateOfHire:    driverChange.Driver.DateOfHire.Time,
			})
		}
	}
	return addedDrivers, nil
}

// fetchViolationsForNewDrivers retrieves violation information for the specified drivers.
func (i *Impl) fetchViolationsForNewDrivers(
	ctx context.Context,
	effectiveDate time.Time,
	usState us_states.USState,
	driversBasicDetails []application.DriverBasicDetails,
) (driver.DriverViolationOverrides, error) {
	var drivers []admitted_app.DriverDetails
	for _, d := range driversBasicDetails {
		drivers = append(drivers, admitted_app.DriverDetails{
			DriverBasicDetails: d,
		})
	}
	mvrViolations, reports, errs := mvr.GetMovingViolationCountsForDrivers(
		ctx,
		drivers,
		effectiveDate,
		enums.ProgramTypeNonFleetAdmitted,
		usState,
		i.Deps.FetcherClientFactory,
	)
	violationOverrides, _, err := driver.GetDriverVioOverridesAndInfo(
		ctx,
		effectiveDate,
		driversBasicDetails,
		enums.ProgramTypeNonFleetAdmitted,
		driver.DriverViolationDetail{
			MVRReports: reports,
			Records:    mvrViolations,
			Errors:     errs,
		},
	)
	return violationOverrides, err
}

// getDriverChangesByCDL creates a map of driver changes from the endorsement request.
func (i *Impl) getDriverChangesByCDL(
	ctx context.Context,
	requestID uuid.UUID,
) (map[string]*endorsementapp.Change, error) {
	endReqObj, err := i.Deps.EndorsementRequestManager.GetByID(ctx, requestID)
	if err != nil {
		return nil, err
	}

	ib, err := commonib.GetInsuranceBundleByInternalID(
		ctx, i.Deps.InsuranceBundleManagerClient, endReqObj.Base.ID.String())
	if err != nil {
		return nil, err
	}

	driverProcessor, err := endorsement.GetProcessor[endorsement.DriverChangeProcessor](ib.ProgramType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, endorsement.ErrGetDriverProcessor.Error())
	}

	driverChangesInDB := driverProcessor.FilterDriverChangesFromEndorsementRequest(endReqObj)
	return driverProcessor.CreateDriverChangeMap(driverChangesInDB), nil
}

// createOverrides generates override records for driver violations.
func (i *Impl) createOverrides(
	reviewID uuid.UUID,
	violationOverrides *driver.DriverViolationOverrides,
	driverChangeMap map[string]*endorsementapp.Change,
) []endorsementreview.Override {
	overrides := make([]endorsementreview.Override, 0)

	for _, dv := range *violationOverrides.DriverViolations {
		for dl, ch := range driverChangeMap {
			if strings.EqualFold(dv.LicenseNumber, dl) {
				override := i.createSingleOverride(reviewID, dv, ch)
				overrides = append(overrides, override)
			}
		}
	}
	return overrides
}

// createSingleOverride creates a single override record for a driver violation.
func (i *Impl) createSingleOverride(
	reviewID uuid.UUID,
	dv admitted_app.DriverViolation,
	ch *endorsementapp.Change,
) endorsementreview.Override {
	data := ch.Data
	driverChangeData := data.GetNonFleetChange().GetDriverChange().Add[0]
	driverChangeData.Violations = &model.NFAdmittedViolationDataV1{
		ViolationPoints: int32(dv.ViolationPoints),
		ClassCounts: map_utils.Transform(
			dv.ClassCounts,
			func(k string, v int64) int32 {
				return int32(v)
			}),
	}

	return endorsementreview.Override{
		ID:               uuid.New(),
		ReviewID:         reviewID,
		OriginalChangeID: pointer_utils.ToPointer(uuid.MustParse(ch.Id)),
		ChangeData:       data,
		IsActive:         true,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}
}
