package client

import (
	"context"
	"sync"
	"time"

	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	"nirvanatech.com/nirvana/openapi-specs/components/common"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/looplab/fsm"

	"nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/application/endorsementapp/endorsement-review/internal"
	statemachine "nirvanatech.com/nirvana/application/endorsementapp/endorsement-review/internal/state-machine"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	endorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	endreviewenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums"
	"nirvanatech.com/nirvana/nonfleet/rating"
)

type Impl struct {
	Deps          internal.Deps
	stateMachines map[uuid.UUID]*statemachine.EndorsementReviewStateMachine
	mu            sync.Mutex
}

func New(deps internal.Deps) *Impl {
	return &Impl{
		Deps:          deps,
		stateMachines: make(map[uuid.UUID]*statemachine.EndorsementReviewStateMachine),
		mu:            sync.Mutex{},
	}
}

// getStateMachine returns the state machine for a given review id. If
// the state machine does not exist, it creates a new one. It maintains a map of
// state machines for all reviews. This is done to ensure that we don't create
// multiple state machines for the same review id.
func (i *Impl) getStateMachine(
	ctx context.Context,
	endorsementReviewID uuid.UUID,
) (*fsm.FSM, error) {
	i.mu.Lock()
	defer i.mu.Unlock()
	if i.stateMachines == nil {
		i.stateMachines = make(map[uuid.UUID]*statemachine.EndorsementReviewStateMachine)
	}

	endRevObj, err := i.Deps.EndorsementReviewWrapper.GetByID(ctx, endorsementReviewID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get endorsement review %s", endorsementReviewID)
	}
	stateMachine, err := statemachine.New(ctx, i.Deps, endRevObj.State)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create state machine for endorsement review %s", endorsementReviewID)
	}

	retval := &statemachine.EndorsementReviewStateMachine{
		EndorsementReviewID: endorsementReviewID,
		StateMachine:        stateMachine,
	}
	i.stateMachines[endorsementReviewID] = retval
	return stateMachine, nil
}

func (i *Impl) Create(ctx context.Context, args endorsementreview.CreateEndorsementReviewArgs) (*uuid.UUID, error) {
	endorsementReviewID := uuid.New()
	now := time.Now()

	endorsementReview := &endorsementreview.Review{
		ID:                      endorsementReviewID,
		RequestID:               args.RequestID,
		UnderwritingAssistantID: args.UnderwritingAssistantID,
		ApprovedAt:              nil,
		CreatedAt:               now,
		UpdatedAt:               now,
		DefaultEffectiveDate:    args.DefaultEffectiveDate,
		PrimaryInsuredName:      args.PrimaryInsuredName,
	}

	if args.WrittenPremium != nil {
		writtenPremium := *args.WrittenPremium
		endorsementReview.WrittenPremium = writtenPremium
	}

	if args.MVRPullDetails != nil && args.MVRPullDetails.Status == common.MVRPullStatusSuccess {
		newAction := endorsementreview.EndorsementReviewAction{
			Action:         endreviewenums.EndorsementReviewActionTypePullMVR,
			LastModifiedAt: args.MVRPullDetails.LatestPullTime,
		}
		endorsementReview.Actions = append(endorsementReview.Actions, newAction)
	}

	err := i.Deps.EndorsementReviewWrapper.Insert(ctx, endorsementReview)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to insert endorsement review: %v", endorsementReview.ID)
	}

	machine, err := i.getStateMachine(ctx, endorsementReviewID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get state machine for endorsement review %v", endorsementReviewID)
	}

	err = machine.Event(ctx, statemachine.EventCreate.String(), statemachine.EventArgs{
		EndorsementReviewID: pointer_utils.ToPointer(endorsementReviewID),
	})
	if err != nil {
		return nil, errors.Wrapf(err, "failed to trigger create event for endorsement review %v", endorsementReviewID)
	}
	return &endorsementReviewID, nil
}

func (i *Impl) GetByID(ctx context.Context, reviewID uuid.UUID) (*endorsementreview.Review, error) {
	return i.Deps.EndorsementReviewWrapper.GetByID(ctx, reviewID)
}

func (i *Impl) UpdatePricingDetails(
	ctx context.Context,
	reviewID uuid.UUID,
	pricingContextIDs []uuid.UUID,
	writtenPremium endorsementapp.EndorsementWrittenPremium,
) error {
	updaterFunc := func(review *endorsementreview.Review) *endorsementreview.Review {
		review.PricingContextIDs = pricingContextIDs
		review.WrittenPremium = writtenPremium
		return review
	}
	return i.Deps.EndorsementReviewWrapper.Update(ctx, reviewID, updaterFunc)
}

func (i *Impl) BeginPriceUpdate(ctx context.Context, reviewID uuid.UUID, pricingType rating.RunType) error {
	machine, err := i.getStateMachine(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement review %v", reviewID)
	}

	endReview, err := i.GetByID(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement review %v", reviewID)
	}

	err = machine.Event(ctx, statemachine.EventRefreshPrice.String(), statemachine.EventArgs{
		EndorsementReviewID:  pointer_utils.ToPointer(reviewID),
		EndorsementRequestID: pointer_utils.ToPointer(endReview.RequestID),
		PricingType:          pointer_utils.ToPointer(pricingType),
	})
	if err != nil {
		return errors.Wrapf(err, "failed to trigger refresh price event for endorsement review %v", reviewID)
	}
	return nil
}

func (i *Impl) EndPriceUpdate(ctx context.Context, reviewID uuid.UUID) error {
	machine, err := i.getStateMachine(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement review %v", reviewID)
	}

	err = machine.Event(ctx, statemachine.EventRefreshComplete.String(), statemachine.EventArgs{
		EndorsementReviewID: pointer_utils.ToPointer(reviewID),
	})
	if err != nil {
		return errors.Wrapf(err, "failed to trigger refresh complete event for endorsement review %v", reviewID)
	}
	return nil
}

// Approve approves the endorsement review as well as the corresponding endorsement request
func (i *Impl) Approve(
	ctx context.Context,
	reviewID uuid.UUID,
	notes *string,
) error {
	machine, err := i.getStateMachine(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement review %s", reviewID)
	}

	endRevObj, err := i.GetByID(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement review %s", reviewID)
	}

	err = machine.Event(ctx, statemachine.EventApprove.String(), statemachine.EventArgs{
		EndorsementReviewID:  pointer_utils.ToPointer(reviewID),
		EndorsementRequestID: pointer_utils.ToPointer(endRevObj.RequestID),
		Notes:                notes,
		ApprovedAt:           pointer_utils.ToPointer(time.Now()),
	})
	if err != nil {
		return errors.Wrapf(err, "failed to approve endorsement review %s", reviewID)
	}

	err = i.Deps.EndorsementRequestManager.Approve(ctx, endRevObj.RequestID, &endRevObj.WrittenPremium)
	if err != nil {
		return errors.Wrapf(err, "failed to approve endorsement request %s", endRevObj.RequestID)
	}
	return nil
}

// Decline declines the endorsement review as well as the corresponding endorsement request
func (i *Impl) Decline(
	ctx context.Context,
	reviewID uuid.UUID,
	notes *string,
) error {
	machine, err := i.getStateMachine(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement review %s", reviewID)
	}

	err = machine.Event(ctx, statemachine.EventDecline.String(), statemachine.EventArgs{
		EndorsementReviewID: pointer_utils.ToPointer(reviewID),
		Notes:               notes,
	})
	if err != nil {
		return errors.Wrapf(err, "failed to reject endorsement review %s", reviewID)
	}

	envRevObj, err := i.GetByID(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement review %s", reviewID)
	}

	err = i.Deps.EndorsementRequestManager.Decline(ctx, envRevObj.RequestID)
	if err != nil {
		return errors.Wrapf(err, "failed to decline endorsement request %s", envRevObj.RequestID)
	}

	return nil
}

// Close closes the endorsement review as well as the corresponding endorsement request
func (i *Impl) Close(ctx context.Context, reviewID uuid.UUID, notes *string) error {
	machine, err := i.getStateMachine(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement review %s", reviewID)
	}

	err = machine.Event(ctx, statemachine.EventClose.String(), statemachine.EventArgs{
		EndorsementReviewID: pointer_utils.ToPointer(reviewID),
		Notes:               notes,
	})
	if err != nil {
		return errors.Wrapf(err, "failed to close endorsement review %s", reviewID)
	}

	envRevObj, err := i.GetByID(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement review %s", reviewID)
	}

	err = i.Deps.EndorsementRequestManager.Close(ctx, envRevObj.RequestID)
	if err != nil {
		return errors.Wrapf(err, "failed to close endorsement request %s", envRevObj.RequestID)
	}

	return nil
}

func (i *Impl) GetAll(ctx context.Context, filters ...endorsementreview.Filter) ([]*endorsementreview.Review, error) {
	return i.Deps.EndorsementReviewWrapper.Get(ctx, filters...)
}

// PullMVR triggers the MVR pull for the given reviewID
// It updates the review with MVR action status, fetches driver changes, pulls MVR data,
// and updates violation overrides accordingly.
// NOTE: Currently, this function is specific to Non-Fleet endorsement reviews only
// TODO: Generalize this function to work with any endorsement review
func (i *Impl) PullMVR(ctx context.Context, reviewID uuid.UUID, usState us_states.USState) error {
	if err := i.updateMVRAction(ctx, reviewID); err != nil {
		return errors.Wrapf(err, "failed to update MVR action for endorsement review %s", reviewID)
	}

	endRevObj, err := i.GetByID(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement review %s", reviewID)
	}

	// We only need to fetch violations for new drivers
	// For drivers already on policy/bundle, their violations are already
	// present in the program data
	newDrivers, err := i.getNewDriversAddedInEndReq(ctx, endRevObj.RequestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get new drivers for endorsement review %s", reviewID)
	}

	// Return if there are no new drivers
	if len(newDrivers) == 0 {
		return nil
	}

	newDriverViolationData, err := i.fetchViolationsForNewDrivers(ctx, endRevObj.DefaultEffectiveDate, usState, newDrivers)
	if err != nil {
		return errors.Wrapf(err, "failed to fetch violations for new drivers for endorsement review %s", reviewID)
	}

	driverChangesByCDL, err := endorsement.GetEndorsementDriverChangesByCDL(ctx, i.Deps.EndorsementRequestManager, i.Deps.InsuranceBundleManagerClient, endRevObj.RequestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get driver changes for endorsement review %s", reviewID)
	}

	// We create overrides by updating the change data of any driver addition change
	// We update the violation points and class counts in the change data
	// This is then persisted as an override
	// This override is used during pricing, where we replace the change data with the override data
	// So, the pricing engine uses the updated violation points and class counts of the newly added drivers
	overrides := i.createOverrides(reviewID, &newDriverViolationData, driverChangesByCDL)

	return i.UpdateOverrides(ctx, reviewID, overrides)
}

func (i *Impl) UpdateOverrides(ctx context.Context, reviewID uuid.UUID, overrides []endorsementreview.Override) error {
	updateFunc := func(review *endorsementreview.Review) *endorsementreview.Review {
		// Deactivate all existing overrides
		// TODO: In future, we may want to get smart about which overrides to deactivate
		for o := range review.Overrides {
			review.Overrides[o].IsActive = false
		}
		review.Overrides = append(review.Overrides, overrides...)

		return review
	}
	return i.Deps.EndorsementReviewWrapper.Update(ctx, reviewID, updateFunc)
}

func (i *Impl) IsMVRPulled(ctx context.Context, reviewID uuid.UUID) bool {
	endRevObj, err := i.GetByID(ctx, reviewID)
	if err != nil {
		return false
	}

	action := findAction(endRevObj.Actions, endreviewenums.EndorsementReviewActionTypePullMVR)
	return action != nil
}

func (i *Impl) SetPanic(ctx context.Context, reviewID uuid.UUID) error {
	machine, err := i.getStateMachine(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement review %v", reviewID)
	}

	endReview, err := i.GetByID(ctx, reviewID)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement review %v", reviewID)
	}

	err = machine.Event(ctx, statemachine.EventPanic.String(), statemachine.EventArgs{
		EndorsementReviewID:  pointer_utils.ToPointer(reviewID),
		EndorsementRequestID: pointer_utils.ToPointer(endReview.RequestID),
	})
	if err != nil {
		return errors.Wrapf(err, "failed to trigger panic event for endorsement review %v", reviewID)
	}
	return nil
}

func findAction(
	actions []endorsementreview.EndorsementReviewAction,
	actionType endreviewenums.EndorsementReviewActionType,
) *endorsementreview.EndorsementReviewAction {
	for i := range actions {
		if actions[i].Action == actionType {
			return &actions[i]
		}
	}
	return nil
}
