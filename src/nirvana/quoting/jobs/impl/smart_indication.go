package impl

import (
	"context"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/insurance_carriers_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/problem"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/quoting/pricing_client_wrapper"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
	"nirvanatech.com/nirvana/rating/utils"
)

// Smart Indication is a component that allows us to run pricing experiments
// on a subset of our users. The goal is to reduce the drop-off post-indication.
// The idea is to run a plain indication and then re-run the indication with
// a discount applied as schedule mods.

// indicationQuartile represents the quartile for a given indication price
// based on our existing data.
// Quartile 0 represents the highest indication price.
type indicationQuartile int

const (
	indicationQuartile0 indicationQuartile = iota
	indicationQuartile1
	indicationQuartile2
	indicationQuartile3
	indicationQuartileUnavailable
)

// generateSmartIndication generates indications using the "Smart Indication"
// experiment wrapper.
func generateSmartIndication(
	ctx context.Context,
	deps *Deps,
	fetcherClient data_fetching.FetcherClient,
	processorClient data_processing.ProcessorClient,
	appID string,
	latestInsurance *data_fetching.InsuranceRecordV1,
	insCarrTree insurance_carriers_utils.InsuranceCarriersTree,
	mi *application.ModelInput,
	config *utils.ModelRunConfig,
	probs *problem.Problems,
	artifactFileKey string,
) (*model_output.ModelOutput, *problem.Problems, *float64, error) {
	// First run of plain indication
	mo, p, err := pricing_client_wrapper.GetPrice(
		ctx,
		fetcherClient,
		processorClient,
		deps.ModelRunner,
		mi,
		config,
		probs,
		artifactFileKey,
		nil,
	)
	log.Info(ctx, "first indication run output", log.Any("mo", mo), log.Any("p", p), log.String("appId", appID))
	if err != nil {
		log.Error(ctx, "failed execute function")
		return nil, nil, nil, errors.Wrap(err, "unable to execute rateml exec func")
	}

	appObj, err := deps.AppWrapper.GetAppById(ctx, appID)
	if err != nil {
		return nil, nil, nil, errors.Wrapf(err, "unable to fetch app obj with id %v", appID)
	}

	var discount *float64
	// Use the locked discount if it exists
	if appObj.ModelPinConfig.RateML.SmartIndicationDiscount != nil {
		discount = appObj.ModelPinConfig.RateML.SmartIndicationDiscount
	} else {
		user, err := GetProducerFromAppObj(ctx, appObj, deps)
		if err != nil {
			return nil, nil, nil, errors.Wrap(err, "unable to get producer from app obj")
		}

		var quartileDiscount float64
		quartileDiscountExperimentEnabled, err := deps.FeatureFlagClient.BoolVariation(
			feature_flag_lib.BuildLookupAttributes(*user),
			feature_flag_lib.FeatureIndicationPricingExperimentQuartileDiscount,
			false,
		)
		if err != nil {
			return nil, nil, nil, errors.Wrap(err, "unable to fetch quartile discount experiment variation from feature flag")
		}
		if quartileDiscountExperimentEnabled {
			quartileDiscount, err = getDiscountPercentBasedOnQuartile(latestInsurance, insCarrTree, mo)
			if err != nil {
				log.Warn(
					ctx,
					"unable to get quartile discount",
					log.Err(err),
				)
			}
		}

		var stateDiscount float64
		stateDiscountExperimentEnabled, err := deps.FeatureFlagClient.BoolVariation(
			feature_flag_lib.BuildLookupAttributes(*user),
			feature_flag_lib.FeatureIndicationPricingExperimentStateDiscount,
			false,
		)
		if err != nil {
			return nil, nil, nil, errors.Wrap(err, "unable to fetch state discount experiment variation from feature flag")
		}
		if stateDiscountExperimentEnabled {
			stateDiscount, err = getDiscountPercentBasedOnState(appObj)
			if err != nil {
				log.Warn(
					ctx,
					"unable to get state discount",
					log.Err(err),
				)
			}
		}

		totalDiscount := quartileDiscount + stateDiscount

		// If total discount is greater than 100%, log a warning and set it to 0
		if totalDiscount > 100 {
			totalDiscount = 0
			log.Warn(
				ctx,
				"skipping smart indication since total discount is greater than 100%",
				log.String("ApplicationID", appID),
				log.Float64("QuartileDiscount", quartileDiscount),
				log.Float64("StateDiscount", stateDiscount),
				log.Float64("TotalDiscount", totalDiscount),
			)
		}

		discount = &totalDiscount

		// Lock the discount
		err = deps.AppWrapper.UpdateApp(ctx, appID, func(app application.Application) (application.Application, error) {
			app.ModelPinConfig.RateML.SmartIndicationDiscount = discount
			return app, nil
		})
		if err != nil {
			return nil, nil, nil, errors.Wrap(err, "unable to lock smart indication discount")
		}
	}

	if discount == nil || *discount == 0 {
		log.Info(ctx, "skipping smart indication since discount is 0", log.String("ApplicationID", appID))
		return mo, p, nil, nil
	}

	originalIndicationPremium := mo.GetTotalPolicyPremium()

	// Re-run indication run with discount applied as schedule mods
	config.Hyperparams.SmartIndicationDiscount = discount

	mo, p, err = pricing_client_wrapper.GetPrice(
		ctx,
		fetcherClient,
		processorClient,
		deps.ModelRunner,
		mi,
		config,
		probs,
		artifactFileKey,
		nil,
	)
	log.Info(ctx, "indication rerun output", log.Any("mo", mo), log.Any("p", p), log.String("appId", appID))
	if err != nil {
		return nil, nil, nil, errors.Wrap(err, "unable to execute rateml exec func after discount is applied")
	}

	discountedIndicationPremium := mo.GetTotalPolicyPremium()
	log.Info(
		ctx,
		"applied discount to indication",
		log.Float64p("discount", discount),
		log.Float64("originalIndicationPremium", originalIndicationPremium),
		log.Float64("discountedIndicationPremium", discountedIndicationPremium),
	)
	return mo, p, discount, err
}

// getDiscountPercentBasedOnQuartile calculates a discount based on a given carrier
// and quartile. The discounts are inputs given by Insurance Product team and
// we do not expect those to change for now. We might want to make these
// configurable during runtime, but will do only if required.
func getDiscountPercentBasedOnQuartile(
	latestInsurance *data_fetching.InsuranceRecordV1,
	insCarrTree insurance_carriers_utils.InsuranceCarriersTree,
	mo *model_output.ModelOutput,
) (float64, error) {
	g, err := getCarrierGrade(latestInsurance, insCarrTree)
	if err != nil {
		return 0, errors.Wrap(err, "unable to get carrier grade")
	}

	if g == insurance_carriers_utils.GradeUnavailable {
		return 0, errors.New("carrier grade unavailable")
	}

	indicationPerPu := mo.GetLiabPolicyPremiumPpu()
	// Get indication quartile
	q, err := getIndicationQuartile(indicationPerPu, g)
	if err != nil {
		return 0, errors.Wrap(err, "unable to get indication quartile")
	}

	switch {
	case g != insurance_carriers_utils.GradeA &&
		g != insurance_carriers_utils.GradeB:
		return 0, errors.New("discounts only apply to A & B graded carriers")
	case q == indicationQuartile0 && g == insurance_carriers_utils.GradeA:
		return 35.0, nil
	case q == indicationQuartile0 && g == insurance_carriers_utils.GradeB:
		return 25.0, nil
	case q == indicationQuartile1 && g == insurance_carriers_utils.GradeA:
		return 15.0, nil
	case q == indicationQuartile1 && g == insurance_carriers_utils.GradeB:
		return 10.0, nil
	default:
		return 0, errors.Newf("unexpected combination of grade %s and quartile %s", g, q)
	}
}

func getDiscountPercentBasedOnState(appObj *application.Application) (float64, error) {
	if appObj.CompanyInfo == nil || appObj.CompanyInfo.USState == nil {
		return 0, errors.New("unable to get us state from app obj")
	}

	eligibleStates := []us_states.USState{
		us_states.PA,
		us_states.MO,
		us_states.KS,
		us_states.SC,
	}

	for _, state := range eligibleStates {
		if appObj.CompanyInfo.USState == state {
			return 15, nil
		}
	}

	return 0, nil
}

// getCarrierGrade gets the grade of a carrier using our InsuranceCarriersTree.
func getCarrierGrade(
	latestInsurance *data_fetching.InsuranceRecordV1,
	insCarrTree insurance_carriers_utils.InsuranceCarriersTree,
) (insurance_carriers_utils.Grade, error) {
	if latestInsurance == nil {
		return insurance_carriers_utils.GradeUnavailable, nil
	}
	node, err := insCarrTree.SearchCarrierByName(latestInsurance.InsuranceCompanyName)
	if err != nil {
		return insurance_carriers_utils.GradeUnavailable, errors.Wrap(err, "carrier not found")
	}
	if (*node).Grade() != insurance_carriers_utils.GradeUnavailable {
		return (*node).Grade(), nil
	}
	parent, err := insCarrTree.ParentWithGrade((*node).Name())
	if err != nil {
		return insurance_carriers_utils.GradeUnavailable, errors.Wrap(err, "unable to find parent with grade")
	}
	return (*parent).Grade(), nil
}

// getIndicationQuartile gets the quartile for a given indication.
// Note: the quartile breaking numbers have been calculated manually for now,
// and therefore, are hardcoded below. This might change if the "Smart
// Indication" experiment works.
func getIndicationQuartile(
	indication float64,
	g insurance_carriers_utils.Grade,
) (indicationQuartile, error) {
	if g == insurance_carriers_utils.GradeA ||
		g == insurance_carriers_utils.GradeB {
		switch {
		case indication >= 16239:
			return indicationQuartile0, nil
		case indication < 16239 && indication >= 9079:
			return indicationQuartile1, nil
		case indication < 9079 && indication >= 6868:
			return indicationQuartile2, nil
		case indication < 6868 && indication >= 4832:
			return indicationQuartile3, nil
		}
	}
	if g == insurance_carriers_utils.GradeC ||
		g == insurance_carriers_utils.GradeD {
		switch {
		case indication >= 18331:
			return indicationQuartile0, nil
		case indication < 18331 && indication >= 9364:
			return indicationQuartile1, nil
		case indication < 9364 && indication >= 8666:
			return indicationQuartile2, nil
		case indication < 8666 && indication >= 4537:
			return indicationQuartile3, nil
		}
	}
	return indicationQuartileUnavailable, errors.New("unexpected grade received")
}
