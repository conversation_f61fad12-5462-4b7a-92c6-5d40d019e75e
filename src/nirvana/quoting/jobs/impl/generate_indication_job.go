package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/PagerDuty/go-pagerduty"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/problem"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/events/quoting_events"
	"nirvanatech.com/nirvana/external_client/salesforce/jobs/enums"
	"nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/external_data_management/clients_management"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/insurance-core/monitoring"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/quoting/ancillary_coverages"
	"nirvanatech.com/nirvana/quoting/jobs"
	"nirvanatech.com/nirvana/quoting/quote_generator"
	"nirvanatech.com/nirvana/quoting/utils"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
	"nirvanatech.com/nirvana/rating/artifacts"
	rating_utils "nirvanatech.com/nirvana/rating/utils"
)

const (
	indicationQuotingStage       = "INDICATION"
	indicationContextLookupDepth = 1
)

func NewGenerateIndicationJob(deps *Deps) (*jtypes.Job[*jobs.IndicationRunMessage], error) {
	runner := NewIndicationTaskRunner(deps)
	return jtypes.NewJob(
		jobs.GenerateIndicationRun,
		job_utils.WrapTaskSetWithOnFailureCallback[*jobs.IndicationRunMessage](
			[]jtypes.TaskCreator[*jobs.IndicationRunMessage]{
				func() jtypes.Task[*jobs.IndicationRunMessage] {
					return NewGenerateIndicationTask(deps, runner)
				},
			},
			func(ctx jtypes.Context, msg *jobs.IndicationRunMessage, err string) error {
				originalError := errors.New(err)
				pdErr := runner.TriggerPd(ctx, msg, originalError)
				if pdErr != nil {
					log.Error(ctx, "Failed to send pagerduty event", log.Err(pdErr))
				}
				log.Error(
					ctx,
					"Failed to generate indication",
					log.String("SubmissionID", msg.SubmissionID.String()),
					log.Err(originalError))
				return nil
			},
		),
		jobs.IndicationRunMessageUnmarshalFn,
	)
}

func NewGenerateIndicationTask(deps *Deps, runner IndicationTaskRunner) *GenerateIndicationTask {
	return &GenerateIndicationTask{deps: deps, runner: runner}
}

type IndicationTaskRunnerImpl struct {
	deps *Deps
}

func NewIndicationTaskRunner(deps *Deps) *IndicationTaskRunnerImpl {
	return &IndicationTaskRunnerImpl{deps: deps}
}

type GenerateIndicationTask struct {
	DefaultQuotingTaskRetryable[*jobs.IndicationRunMessage]
	job_utils.NoopUndoTask[*jobs.IndicationRunMessage]
	deps   *Deps
	runner IndicationTaskRunner
}

var _ jtypes.Task[*jobs.IndicationRunMessage] = (*GenerateIndicationTask)(nil)

func (t *GenerateIndicationTask) ID() string {
	return "ProcessTask"
}

func (t *IndicationTaskRunnerImpl) TriggerPd(ctx jtypes.Context, msg *jobs.IndicationRunMessage, errContext error) error {
	log.Info(ctx, "triggering pagerduty event for generate indication job", log.String("submissionId", msg.SubmissionID.String()))
	sub, err := t.deps.AppWrapper.GetSubmissionById(ctx, msg.SubmissionID.String())
	if err != nil {
		return errors.Wrap(err, "error while getting submission")
	}
	app, err := t.deps.AppWrapper.GetAppById(ctx, sub.ApplicationID)
	if err != nil {
		return errors.Wrap(err, "error while getting application")
	}
	dedupKey := app.ID
	err = t.deps.InsuranceEngPDClient.TriggerAlert(ctx, &monitoring.TriggerPagerDutyAlertRequest{
		Event: pagerduty.V2Event{
			DedupKey: dedupKey,
			Action:   "trigger",
			Client:   "Agents",
			Payload: &pagerduty.V2Payload{
				Summary:  "Failed to Generate Indication for " + sub.CompanyInfo.Name,
				Source:   "Jobber",
				Severity: "error",
				Details: map[string]any{
					"SubmissionId":  sub.ID,
					"ApplicationId": sub.ApplicationID,
					"CompanyName":   sub.CompanyInfo.Name,
					"Error":         errContext.Error(),
				},
			},
		},
		AgencyID:    app.AgencyID,
		ProgramType: policyenums.ProgramTypeFleet,
		Err:         errContext,
	})
	if err != nil {
		return errors.Wrap(err, "failed to send pagerduty event")
	}
	return nil
}

func (t *GenerateIndicationTask) Run(ctx jtypes.Context, msg *jobs.IndicationRunMessage) (err error) {
	defer func(timeStart time.Time) {
		emitMetric(ctx, t.deps.MetricsClient, jobs.GenerateIndicationRun, time.Since(timeStart), err)
	}(time.Now())

	// handle panic and error
	defer func() {
		if r := recover(); r != nil {
			log.Error(ctx, "panic in generate indication task", log.Any("recovered", r))
			err = errors.New("panic in generate indication task")
		}
	}()

	return t.runner.RunGenerateIndication(ctx, msg)
}

func (t *GenerateIndicationTask) Retry(ctx jtypes.Context, msg *jobs.IndicationRunMessage) error {
	return t.Run(ctx, msg)
}

func (t *IndicationTaskRunnerImpl) RunGenerateIndication(jCtx jtypes.Context, msg *jobs.IndicationRunMessage) error {
	subId := msg.SubmissionID.String()
	packageType := msg.IndicationReq.PackageType

	ctx := log.ContextWithFields(
		jCtx,
		log.String("submissionId", subId),
		log.Stringer("packageType", packageType),
	)

	appWrapper := t.deps.AppWrapper
	submissionObject, err := appWrapper.GetSubmissionById(ctx, subId)
	if err != nil {
		log.Error(ctx, "Failed to fetch submission", log.Err(err))
		return errors.Wrap(err, "unable to get sub by id")
	}
	appId := submissionObject.ApplicationID
	ctx = log.ContextWithFields(ctx, log.String("applicationId", appId))
	appStateMachine := t.deps.ASMWrapper.NewAppStateMachine(appId)

	appObj, err := t.deps.AppWrapper.GetAppById(ctx, appId)
	if err != nil {
		log.Plain.Error("Failed to fetch application", log.Err(err))
		return errors.Wrap(err, "unable to get app by id")
	}
	logApplicationEvent(ctx, t.deps, Started, Indication, *appObj, nil, nil)
	log.Info(ctx, "Application problems at the beginning", log.Any("problems", appObj.Problems), log.String("appId", appId))

	var (
		modelOut      *model_output.ModelOutput
		modelProblems *problem.Problems
		discount      *float64
	)

	// Add the ancillary coverages to the submission for model run
	submissionObject, err = addAncillaryCoveragesForIndicationSubmission(
		ctx,
		submissionObject,
		packageType,
	)
	if err != nil {
		return errors.Wrap(err, "failed to persist ancillary coverages for submission")
	}

	modelRunConfig, err := getModelRunConfigForIndication(ctx, submissionObject, packageType)
	if err != nil {
		return errors.Wrap(err, "couldn't get model run config")
	}

	packageName := getPackageName(packageType)

	artifactFileKey := artifacts.GenerateFileKey(
		t.deps.Clock,
		appId,
		subId,
		indicationQuotingStage,
		packageName,
	)

	err = t.writeClientsConfigToContext(ctx, appObj.DataContextID)
	if err != nil {
		return errors.Wrap(err, "failed to write clients config to context")
	}

	fetcherClient, processorClient, closer, err := t.deps.ClientsManager.BuildClientsFromContext(
		ctx,
		appObj.DataContextID,
	)
	if err != nil {
		return errors.Wrap(err, "failed to build data fetcher and data processor clients")
	}
	defer func() { _ = closer() }()

	latestInsurance, err := getLatestInsuranceRecord(ctx, fetcherClient, appObj.CompanyInfo.DOTNumber)
	if err != nil {
		log.Error(ctx, "failed to get latest insurance record", log.Err(err),
			log.String("DotNumber", fmt.Sprint(appObj.CompanyInfo.DOTNumber)))
		return errors.Wrapf(err, "unable to get latest insurance record for dot %v", appObj.CompanyInfo.DOTNumber)
	}

	modelOut, modelProblems, discount, err = generateSmartIndication(
		ctx,
		t.deps,
		fetcherClient,
		processorClient,
		appObj.ID,
		latestInsurance,
		t.deps.InsuranceCarrierTree,
		&submissionObject.ModelInput,
		modelRunConfig,
		appObj.Problems,
		artifactFileKey,
	)
	log.Info(
		ctx,
		"response after smart indication",
		log.Any("modelOut", modelOut),
		log.Any("modelProblems", modelProblems),
		log.String("appId", appId),
	)
	if err != nil {
		log.Plain.Error("Error while running Indication model for submission", log.Err(err))
		// Call asm.SetPanic
		metadata := application.StateMetadata{
			Description: fmt.Sprintf("Error while running Indication model for submission %s"+
				" of application %s. Error = %v.", subId, appId, err),
			IsRatingFailure: true,
		}
		errUpdate := appStateMachine.SetPanic(ctx, &metadata)
		if errUpdate != nil {
			log.Plain.Error("Error while setting panic state for submission", log.Err(err))
			return errors.Wrap(errUpdate, "unable to set panic")
		}
		// rather than making a call to get the app obj with the persisted metadata
		// we assign the metadata in memory for our failed event since we would be
		// returning from this function anyway
		appObj.StateMetadata = metadata
		logApplicationEvent(ctx, t.deps, Failed, Indication, *appObj, nil, nil)
		return errors.Wrap(err, "unable to execute model")
	}

	if modelProblems.NumTags() > 0 {
		err = t.deps.AppWrapper.UpdateApp(ctx, appId,
			func(a application.Application) (application.Application, error) {
				a.Problems.AddBulk(modelProblems)
				return a, nil
			})
		if err != nil {
			logApplicationEvent(ctx, t.deps, Failed, Indication, *appObj, nil, nil)
			return errors.Wrap(err, "unable to update app")
		}

		if modelProblems.HasUnresolvedProblems() {
			// TODO: Do not return error, and handle state machine transitions
			// 		gracefully without indication options.
			logApplicationEvent(ctx, t.deps, Failed, Indication, *appObj, nil, nil)
			return errors.New("problems arose while executing model")
		}
	}

	indicationOption, err := quote_generator.GenerateIndicationOption(ctx, submissionObject, modelOut, packageType)
	if err != nil {
		logApplicationEvent(ctx, t.deps, Failed, Indication, *appObj, nil, nil)
		return errors.Wrap(err, "unable to generate indication option")
	}

	// Insert options for the submission
	if err := appWrapper.InsertIndOption(ctx, *indicationOption); err != nil {
		logApplicationEvent(ctx, t.deps, Failed, Indication, *appObj, nil, nil)
		log.Plain.Error("Failed to insert option for submission", log.String("submissionID", subId),
			log.Err(err))
		return errors.Wrap(err, "unable to insert indication option")
	}
	err = appWrapper.UpdateSub(ctx, subId, func(object application.SubmissionObject) (application.SubmissionObject, error) {
		object.IndicationOptionsIDs = append(object.IndicationOptionsIDs, indicationOption.ID)
		object.AdditionalInsuredInfo = appObj.AdditionalInsuredInfo
		object.PackageType = &packageType
		return object, nil
	})
	if err != nil {
		logApplicationEvent(ctx, t.deps, Failed, Indication, *appObj, nil, nil)
		log.Plain.Error("Failed to update submission with options", log.Err(err))
		return errors.Wrap(err, "unable to update sub")
	}

	// we are selecting the standard indication option as default for these events
	if indicationOption.OptionTag == app_enums.IndicationOptionTagStandard {
		logApplicationEvent(
			ctx,
			t.deps,
			Completed,
			Indication,
			*appObj,
			indicationOption,
			&quoting_events.Hyperparams{
				SmartIndicationDiscountPercentage: discount,
			})

		if err = utils.TriggerUpdateSalesforceOpportunity(ctx, t.deps.Jobber, t.deps.MetricsClient,
			wrapper.SalesforceEventUpdateApplicationArgs{
				ApplicationID:          appId,
				IndicationOptionAmount: indicationOption.TotalPremium,
				NumberOfPowerUnits:     indicationOption.TotalPowerUnits,
				CreatedDate:            time.Now(),
				EventName:              enums.QuotingIndicationGenerationCompleted,
			}); err != nil {
			// just log the error
			log.Error(
				ctx, "failed to update salesforce opportunity",
				log.String("eventName", enums.QuotingIndicationGenerationCompleted.String()),
				log.Err(err),
			)
		}
	}
	return nil
}

func getModelRunConfigForIndication(
	_ context.Context,
	submissionObject *application.SubmissionObject,
	packageType app_enums.IndicationOptionTag,
) (*rating_utils.ModelRunConfig, error) {
	if submissionObject == nil {
		return nil, errors.New("submission was found nil")
	}

	if submissionObject.ModelPinConfig == nil {
		return nil, errors.New("model pin config was found nil")
	}
	rmlConfig := submissionObject.ModelPinConfig.RateML
	applicationConfig := submissionObject.ModelPinConfig.Application

	ratingTierRecordDates, err := rating_utils.NewRecordDatesWithDefaultsV1(
		submissionObject.ModelPinConfig.Data.SentryInputsDumpDate,
		submissionObject.ModelPinConfig.Data.SentryInputsDate,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create new rating record dates")
	}

	return &rating_utils.ModelRunConfig{
		UseDriverMVRs:   false,
		IsIndicationRun: true,
		Hyperparams: rating_utils.ModelRunHyperparameters{
			RatingTierRecordDates: *ratingTierRecordDates,
		},
		RateMLConfig:     rmlConfig,
		RateMLFlags:      rmlConfig.Flags,
		ApplicationFlags: applicationConfig.Flags,
		Bugs:             rmlConfig.Bugs,
		PackageType:      &packageType,
	}, nil
}

// addAncillaryCoveragesForIndicationSubmission adds the ancillary coverages for the submission
// before we generate the indication. This is done since we need to pipe the ancillary coverages
// to the model run. Since we are running parallel jobs over the same submission, we don't want to
// persist the ancillary coverages.
func addAncillaryCoveragesForIndicationSubmission(
	ctx context.Context,
	submission *application.SubmissionObject,
	packageType app_enums.IndicationOptionTag,
) (*application.SubmissionObject, error) {
	// Get the ancillary coverages for the submission
	coverageInfo, err := quote_generator.GenerateAncillaryCoveragesForIndicationSubmission(ctx, *submission, packageType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate ancillary coverages for submission")
	}
	submission.CoverageInfo = coverageInfo

	return submission, nil
}

func persistCommonAncillaryCoverages(
	ctx context.Context,
	deps *Deps,
	submission *application.SubmissionObject,
) error {
	coverageInfo := submission.CoverageInfo
	ancillaryCoverages, err := ancillary_coverages.GetCommonAncillaryCoverages(
		submission.CompanyInfo.USState,
		policyenums.ProgramTypeFleet,
		submission.CoverageInfo.GetPrimaryCoverages(),
		submission.CoverageInfo.EffectiveDate,
		submission.ModelPinConfig.RateML.Version,
		submission.ModelPinConfig.RateML.Provider,
	)
	if err != nil {
		log.Info(ctx, "No ancillary coverages found for state", log.String("state", submission.CompanyInfo.USState.String()))
		// We don't return an error here since we don't want to fail the job if we don't have ancillary coverages
		return nil //nolint:nilerr
	}
	if coverageInfo == nil {
		coverageInfo = new(application.CoverageInfo)
	}
	al := coverageInfo.GetCoverage(app_enums.CoverageAutoLiability)
	if al == nil {
		return errors.Errorf("no auto liability coverage found for sub %s", submission.ID)
	}
	ded := al.Deductible
	submissionCoverages := utils.AddAncillaryCoverages(coverageInfo.Coverages, ancillaryCoverages, ded, nil)

	err = deps.AppWrapper.UpdateSub(
		ctx,
		submission.ID,
		func(object application.SubmissionObject) (application.SubmissionObject, error) {
			object.CoverageInfo.Coverages = submissionCoverages
			return object, nil
		})
	if err != nil {
		return errors.Wrapf(err, "failed to update submission with common ancillary coverages %s", submission.ID)
	}
	return nil
}

func getLatestInsuranceRecord(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	dotNumber int64,
) (*data_fetching.InsuranceRecordV1, error) {
	request := data_fetching.BIPDActiveOrPendingInsuranceRequestV1{DotNumber: dotNumber}
	insurance, err := fetcherClient.GetBIPDActiveOrPendingInsuranceV1(ctx, &request)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get LNI records for DOT %v", dotNumber)
	}
	if len(insurance.GetRecords()) == 0 {
		return nil, nil //nolint:nilnil
	}
	currentInsurance := insurance.GetRecords()[0]

	return currentInsurance, nil
}

func (t *IndicationTaskRunnerImpl) writeClientsConfigToContext(ctx context.Context, contextID uuid.UUID) error {
	commonInterceptorsConfigs := getWritableStoreFirstInterceptorsConfigs(contextID, indicationContextLookupDepth)

	clientsConfig := clients_management.ClientsConfig{
		FetcherConfig: &clients_management.ClientConfig{
			InterceptorsConfigs: commonInterceptorsConfigs,
		},
		ProcessorConfig: &clients_management.ClientConfig{
			InterceptorsConfigs: commonInterceptorsConfigs,
		},
	}

	return t.deps.ClientsManager.WriteClientsConfigToContext(ctx, contextID, &clientsConfig)
}
