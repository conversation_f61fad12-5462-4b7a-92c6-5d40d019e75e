package quote_generator

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/quoting/ancillary_coverages"
	"nirvanatech.com/nirvana/quoting/utils"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
	"nirvanatech.com/nirvana/rating/rtypes"
)

var PrimaryCoverageTypes = map[app_enums.Coverage]bool{
	app_enums.CoverageAutoLiability:      true,
	app_enums.CoverageAutoPhysicalDamage: true,
	app_enums.CoverageMotorTruckCargo:    true,
	app_enums.CoverageGeneralLiability:   true,
}

// shouldOverrideWithAgentLimit determines if we should override an ancillary coverage limit
// with the agent-selected limit based on coverage type and package type
func shouldOverrideWithAgentLimit(
	coverage application.CoverageDetails,
	ancillaryCoverage ancillary_coverages.AncillaryCoverage,
	packageType app_enums.IndicationOptionTag,
) bool {
	// Don't override if coverage has package-based limits - these are intentionally set per package
	if ancillaryCoverage.PackageBasedLimits != nil {
		return false
	}

	// Special case: Enhanced Package Towing only uses agent-selected limit for Complete package
	if coverage.CoverageType == app_enums.CoverageEnhancedPackageTowingLimit {
		return packageType == app_enums.IndicationOptionTagComplete
	}

	// For all other coverages, always override with agent-selected limit
	return true
}

// applyAgentSelectedLimitOverrides applies agent-selected coverage limits to override
// ancillary coverage defaults where appropriate
func applyAgentSelectedLimitOverrides(
	mergedCoverages []application.CoverageDetails,
	agentSelectedCoverages map[app_enums.Coverage]*application.CoverageDetails,
	ancillaryCoverages map[app_enums.Coverage]ancillary_coverages.AncillaryCoverage,
	packageType app_enums.IndicationOptionTag,
) {
	for i, coverage := range mergedCoverages {
		agentSelectedCoverage, hasAgentSelection := agentSelectedCoverages[coverage.CoverageType]
		if !hasAgentSelection {
			continue
		}

		ancillaryCoverage, hasAncillary := ancillaryCoverages[coverage.CoverageType]
		if !hasAncillary {
			// No ancillary coverage rules, so always use agent-selected limit
			mergedCoverages[i].Limit = agentSelectedCoverage.Limit
			continue
		}

		if shouldOverrideWithAgentLimit(coverage, ancillaryCoverage, packageType) {
			mergedCoverages[i].Limit = agentSelectedCoverage.Limit
		}
		// If we shouldn't override, keep the existing limit (from ancillary coverage)
	}
}

// filterCoveragesForPackage filters submission coverages to only include those applicable for the given package type.
// This includes all primary coverages, common coverages, and package-specific coverages.
func filterCoveragesForPackage(
	ctx context.Context,
	submissionCoverages []application.CoverageDetails,
	packageType app_enums.IndicationOptionTag,
) ([]application.CoverageDetails, error) {
	packageCommonCoverages, err := ancillary_coverages.GetCommonCoverages(policy_enums.ProgramTypeFleet)
	if err != nil {
		log.Error(ctx, "Failed to get common coverages",
			log.String("packageType", packageType.String()),
			log.Err(err))
		return nil, errors.Wrapf(err, "failed to get common coverages for package type %s", packageType.String())
	}

	packageSpecificCoverages, err := ancillary_coverages.GetPackageSpecificCoverages(packageType, policy_enums.ProgramTypeFleet)
	if err != nil {
		log.Error(ctx, "Failed to get package specific coverages",
			log.String("packageType", packageType.String()),
			log.Err(err))
		return nil, errors.Wrapf(err, "failed to get package specific coverages for package type %s", packageType.String())
	}

	filteredSubmissionCoverages := make([]application.CoverageDetails, 0)
	for _, coverage := range submissionCoverages {
		if PrimaryCoverageTypes[coverage.CoverageType] {
			filteredSubmissionCoverages = append(filteredSubmissionCoverages, coverage)
			continue
		}

		if slice_utils.Contains(packageCommonCoverages, coverage.CoverageType) {
			filteredSubmissionCoverages = append(filteredSubmissionCoverages, coverage)
		} else if slice_utils.Contains(packageSpecificCoverages, coverage.CoverageType) {
			filteredSubmissionCoverages = append(filteredSubmissionCoverages, coverage)
		}
	}

	return filteredSubmissionCoverages, nil
}

// mergeAgentSelectedAndAncillaryCoverages handles agent-selected coverage limits that should override ancillary coverage defaults.
// Ancillary coverages contain all required coverages for rating with default limits based on state & package type.
// However, agents can select specific coverages with custom limits that should override these defaults.
//
// Flow:
// 1. Filter submission coverages to only include those applicable for the package type
// 2. Store all agent-selected coverages in a map for quick lookup
// 3. Add ancillary coverages to the submission coverages (with default limits)
// 4. Override ancillary coverage limits with agent-selected limits where applicable
//
// Special case: Enhanced Package Towing should only apply its agent-selected limit to Complete package.
// For Standard & Basic packages, it should use the default ancillary coverage limit.
func mergeAgentSelectedAndAncillaryCoverages(
	ctx context.Context,
	submissionCoverages []application.CoverageDetails,
	ancillaryCoverages map[app_enums.Coverage]ancillary_coverages.AncillaryCoverage,
	ded *int32,
	packageType app_enums.IndicationOptionTag,
) ([]application.CoverageDetails, error) {
	// Filter submission coverages to only include those applicable for the package type
	filteredSubmissionCoverages, err := filterCoveragesForPackage(ctx, submissionCoverages, packageType)
	if err != nil {
		return nil, err
	}
	// Store agent-selected coverages for limit override logic
	agentSelectedCoverages := make(map[app_enums.Coverage]*application.CoverageDetails)
	for _, coverage := range filteredSubmissionCoverages {
		agentSelectedCoverages[coverage.CoverageType] = &coverage
	}

	// Add ancillary coverages with default limits
	mergedCoverages := utils.AddAncillaryCoverages(filteredSubmissionCoverages, ancillaryCoverages, ded, &packageType)

	// Override ancillary coverage limits with agent-selected limits where appropriate
	applyAgentSelectedLimitOverrides(mergedCoverages, agentSelectedCoverages, ancillaryCoverages, packageType)

	return mergedCoverages, nil
}

// GenerateAncillaryCoveragesForIndicationSubmission generates ancillary coverages for a submission.
// This uses our auto generate ancillary coverages to find all possible coverages for a state.
func GenerateAncillaryCoveragesForIndicationSubmission(
	ctx context.Context,
	submission application.SubmissionObject,
	packageType app_enums.IndicationOptionTag,
) (*application.CoverageInfo, error) {
	coverageInfo := submission.CoverageInfo

	modelVersion := rtypes.InvalidVersion
	var provider rtypes.RatemlModelProvider
	if submission.ModelPinConfig != nil {
		modelVersion = submission.ModelPinConfig.RateML.Version
		provider = submission.ModelPinConfig.RateML.Provider
	}
	// We get all coverages including package-based ones since they are needed for rating purposes
	ancillaryCoverages, err := ancillary_coverages.GetAllFleetAncillaryCoverages(
		submission.CompanyInfo.USState,
		&packageType,
		coverageInfo.GetPrimaryCoverages(),
		coverageInfo.EffectiveDate,
		false,
		modelVersion,
		provider,
	)
	if err != nil {
		log.Info(ctx, "No ancillary coverages found for state", log.String("state", submission.CompanyInfo.USState.String()))
		return coverageInfo, nil
		// TODO : handle for cases where there's no ancillary coverages for a state.
		// return nil, errors.Errorf("no ancillary coverages found for state %s", submission.CompanyInfo.USState.String())
	}
	if coverageInfo == nil {
		coverageInfo = new(application.CoverageInfo)
	}
	al := coverageInfo.GetCoverage(app_enums.CoverageAutoLiability)
	if al == nil {
		return nil, errors.Errorf("no auto liability coverage found for sub %s", submission.ID)
	}

	// set the coverages in the coverage info to the merged coverages between agent selected and ancillary coverages defaults
	coverageInfo.Coverages, err = mergeAgentSelectedAndAncillaryCoverages(
		ctx,
		coverageInfo.Coverages,
		ancillaryCoverages,
		al.Deductible,
		packageType,
	)
	if err != nil {
		log.Error(ctx, "Failed to merge agent selected and ancillary coverages",
			log.String("packageType", packageType.String()),
			log.Err(err))
		return nil, errors.Wrapf(err, "failed to merge agent selected and ancillary coverages for package type %s", packageType.String())
	}

	return coverageInfo, nil
}

func GenerateIndicationOption(
	ctx context.Context,
	submission *application.SubmissionObject,
	modelOutput *model_output.ModelOutput,
	packageType app_enums.IndicationOptionTag,
) (*application.IndicationOption, error) {
	now := time.Now()
	pkg, err := generateIndicationOption(submission, modelOutput, packageType, now)
	if err != nil {
		log.Error(ctx, "Unable to generate indication option for sub",
			log.String("sub id", submission.ID), log.Any("sub", submission),
			log.String("package type", packageType.String()),
			log.Any("model output", modelOutput), log.Err(err))
		return nil, errors.Wrapf(err, "unable to generate indication option for sub %s", submission.ID)
	}

	return pkg, nil
}

func generateIndicationOption(
	submission *application.SubmissionObject,
	modelOutput *model_output.ModelOutput,
	packageType app_enums.IndicationOptionTag,
	createdAt time.Time,
) (*application.IndicationOption, error) {
	isRecommended := false
	if packageType == app_enums.IndicationOptionTagStandard {
		isRecommended = true
	}
	coverages, err := generatePackageCoverages(submission, modelOutput, false)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to generate package coverages for package type %s and sub %s",
			packageType.String(), submission.ID)
	}
	premiumPerUnit := calculatePremiumPerUnit(
		modelOutput.GetTotalPolicyPremium(),
		modelOutput.GetFleetPowerUnitCount(),
	)
	totalTrailers := calculateNumberOfTrailerUnits(
		int32(len(submission.EquipmentInfo.EquipmentList.Info)),
		int32(modelOutput.GetFleetPowerUnitCount()),
	)
	var surchargeInfo *application.SurchargeInfo
	if statesWithSurchargeInfo[submission.CompanyInfo.USState] {
		surchargeInfo = &application.SurchargeInfo{
			TotalInsuranceSurchargePremium: modelOutput.GetTotalInsuranceSurchargePremium(),
			AutoInsuranceSurchargePremium:  modelOutput.GetAutoInsuranceSurchargePremium(),
			GLInsuranceSurchargePremium:    modelOutput.GetGLInsuranceSurchargePremium(),
			MTCInsuranceSurchargePremium:   modelOutput.GetMTCInsuranceSurchargePremium(),
			AutoLGPTSurchargePremium:       modelOutput.GetAutoLGPTSurchargePremium(),
			GLLGPTSurchargePremium:         modelOutput.GetGLLGPTSurchargePremium(),
			MTCLGPTSurchargePremium:        modelOutput.GetMTCLGPTSurchargePremium(),
			ALPolicySurplusLinesTax:        modelOutput.GetALPolicySurplusLinesTax(),
			ALPolicyStampingFee:            modelOutput.GetALPolicyStampingFee(),
			APDPolicySurplusLinesTax:       modelOutput.GetAPDPolicySurplusLinesTax(),
			APDPolicyStampingFee:           modelOutput.GetAPDPolicyStampingFee(),
			GLPolicySurplusLinesTax:        modelOutput.GetGLPolicySurplusLinesTax(),
			GLPolicyStampingFee:            modelOutput.GetGLPolicyStampingFee(),
			MTCPolicySurplusLinesTax:       modelOutput.GetMTCPolicySurplusLinesTax(),
			MTCPolicyStampingFee:           modelOutput.GetMTCPolicyStampingFee(),
		}
	}

	indOpt := application.NewIndicationOption(application.IndicationOptionInputs{
		ID:                uuid.New().String(),
		SubmissionID:      submission.ID,
		TotalPowerUnits:   int32(modelOutput.GetFleetPowerUnitCount()),
		TotalTrailerUnits: totalTrailers,
		Tiv:               int32(modelOutput.GetTIV()),
		TotalMiles:        int32(submission.CompanyInfo.ProjectedMileage),
		TotalPremium:      int32(modelOutput.GetTotalPolicyPremium()),
		PremiumPerUnit:    premiumPerUnit,
		FlatCharges:       int32(modelOutput.GetFlatPolicyPremium()),
		Coverages:         coverages,
		CreatedAt:         createdAt,
		OptionTag:         packageType,
		IsRecommended:     isRecommended,
		SurchargeInfo:     surchargeInfo,
	})

	totalSurchargePremium := GetTotalSurchargePremiumFromModelOutput(modelOutput)

	if totalSurchargePremium != nil {
		indOpt.TotalSurchargePremium = totalSurchargePremium
	}

	if modelOutput.GetTotalPolicyPremiumUnmodified() != nil {
		indOpt.TotalPolicyPremiumUnmodified = pointer_utils.ToPointer(int32(*modelOutput.GetTotalPolicyPremiumUnmodified()))
	}

	return indOpt, nil
}
