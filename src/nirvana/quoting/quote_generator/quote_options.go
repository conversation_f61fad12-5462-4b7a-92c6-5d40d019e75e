package quote_generator

import (
	"context"
	"math"
	"time"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

func GenerateQuoteOption(
	ctx context.Context,
	submission *application.SubmissionObject,
	modelOutput *model_output.ModelOutput,
	packageType app_enums.IndicationOptionTag,
) (*application.IndicationOption, error) {
	now := time.Now()
	isRecommended := false
	if packageType == app_enums.IndicationOptionTagStandard {
		isRecommended = true
	}
	coverages, err := generatePackageCoverages(submission, modelOutput, true)
	if err != nil {
		log.Error(ctx, "Unable to generate indication option for sub",
			log.String("sub id", submission.ID), log.Any("sub", submission),
			log.String("package type", packageType.String()),
			log.Any("model output", modelOutput), log.Err(err))
		return nil, errors.Wrapf(err, "unable to generate package coverages for package type %s and sub %s",
			packageType.String(), submission.ID)
	}
	var (
		/*
				    The safety discount percentage and premium are derived as follows:
				    - `safetyDiscountPercent` is retrieved directly from `RateMLUnderwritingEntityInputs.SafetyModAllCov`.
				    - `safetyDiscountPremium` is calculated using the formula:
				      `safetyDiscountPremium = totalPolicyPremium * (1.0 / (1.0 - safetyDiscountPercent) - 1.0)`.
					  This is based on the following formulas:
					  - `safetyDiscountPremium = OriginalPremium - FinalPremium`
					  - `FinalPremium = OriginalPremium * (1 - safetyDiscountPercent)`
				    Note: `totalPolicyPremium` already includes the applied safety discount.
			        This logic was originally implemented in RateML but was extracted and moved here
			        during the refactor in [PR #19623](https://github.com/nirvanatech/nirvana/pull/19623).
		*/
		safetyDiscountPercent = submission.RateMLUnderwritingEntityInputs.SafetyModAllCov
		safetyDiscountPremium = modelOutput.GetTotalPolicyPremium() * (1.0/(1.0-safetyDiscountPercent) - 1.0)
	)

	preDiscountPremium := calculatePreDiscountTotalPremium(
		int32(modelOutput.GetTotalPolicyPremium()),
		int32(safetyDiscountPremium),
	)

	totalSurchargePremium := GetTotalSurchargePremiumFromModelOutput(modelOutput)
	subtotalPremium := calculateSubTotalPremium(
		int32(modelOutput.GetTotalPolicyPremium()),
		int32(modelOutput.GetFlatPolicyPremium()),
		totalSurchargePremium,
	)
	premiumPerUnit := calculatePremiumPerUnit(
		modelOutput.GetTotalPolicyPremium(),
		modelOutput.GetFleetPowerUnitCount(),
	)
	totalTrailers := calculateNumberOfTrailerUnits(
		int32(len(submission.EquipmentInfo.EquipmentList.Info)),
		int32(modelOutput.GetFleetPowerUnitCount()),
	)
	var totalPolicyPremiumUnmodified *int32
	if modelOutput.GetTotalPolicyPremiumUnmodified() != nil {
		totalPolicyPremiumUnmodified = pointer_utils.ToPointer(int32(*modelOutput.GetTotalPolicyPremiumUnmodified()))
	}
	var surchargeInfo *application.SurchargeInfo
	if statesWithSurchargeInfo[submission.CompanyInfo.USState] {
		surchargeInfo = &application.SurchargeInfo{
			TotalInsuranceSurchargePremium: modelOutput.GetTotalInsuranceSurchargePremium(),
			AutoInsuranceSurchargePremium:  modelOutput.GetAutoInsuranceSurchargePremium(),
			GLInsuranceSurchargePremium:    modelOutput.GetGLInsuranceSurchargePremium(),
			MTCInsuranceSurchargePremium:   modelOutput.GetMTCInsuranceSurchargePremium(),
			AutoLGPTSurchargePremium:       modelOutput.GetAutoLGPTSurchargePremium(),
			GLLGPTSurchargePremium:         modelOutput.GetGLLGPTSurchargePremium(),
			MTCLGPTSurchargePremium:        modelOutput.GetMTCLGPTSurchargePremium(),
			ALPolicySurplusLinesTax:        modelOutput.GetALPolicySurplusLinesTax(),
			ALPolicyStampingFee:            modelOutput.GetALPolicyStampingFee(),
			APDPolicySurplusLinesTax:       modelOutput.GetAPDPolicySurplusLinesTax(),
			APDPolicyStampingFee:           modelOutput.GetAPDPolicyStampingFee(),
			GLPolicySurplusLinesTax:        modelOutput.GetGLPolicySurplusLinesTax(),
			GLPolicyStampingFee:            modelOutput.GetGLPolicyStampingFee(),
			MTCPolicySurplusLinesTax:       modelOutput.GetMTCPolicySurplusLinesTax(),
			MTCPolicyStampingFee:           modelOutput.GetMTCPolicyStampingFee(),
		}
	}
	// Negotiated Rates
	negoRateApplicable := modelOutput.GetNegotiatedRateFlag()
	ruleFifteenEnabled := modelOutput.GetRule15ExemptionFlag()
	baseLimitPremium := modelOutput.GetRule15ExemptionPremium()
	thresholdPremium := modelOutput.GetRule15ExemptionThreshold()
	negotiatedRates := &application.NegotiatedRates{
		IsNegotiatedRatesApplicable: negoRateApplicable,
		BaseLimitPremium:            pointer_utils.Int64(int64(baseLimitPremium)),
		ThresholdPremium:            pointer_utils.Int64(int64(thresholdPremium)),
		Rules: []application.NegotiatedRatesRule{
			{
				RuleType:     app_enums.NegotiatedRatesExemptionRuleFifteen,
				IsApplicable: ruleFifteenEnabled,
			},
		},
	}

	return application.NewQuoteOption(application.QuoteOptionInputs{
		IndicationOptionInputs: application.IndicationOptionInputs{
			ID:                           uuid.New().String(),
			SubmissionID:                 submission.ID,
			TotalPowerUnits:              int32(modelOutput.GetFleetPowerUnitCount()),
			TotalTrailerUnits:            totalTrailers,
			Tiv:                          int32(modelOutput.GetTIV()),
			TotalMiles:                   int32(submission.CompanyInfo.ProjectedMileage),
			TotalPremium:                 int32(modelOutput.GetTotalPolicyPremium()),
			PremiumPerUnit:               premiumPerUnit,
			FlatCharges:                  int32(modelOutput.GetFlatPolicyPremium()),
			Coverages:                    coverages,
			CreatedAt:                    now,
			OptionTag:                    packageType,
			IsRecommended:                isRecommended,
			TotalSurchargePremium:        totalSurchargePremium,
			TotalPolicyPremiumUnmodified: totalPolicyPremiumUnmodified,
			SurchargeInfo:                surchargeInfo,
		},
		SubtotalPremium:                 subtotalPremium,
		SafetyDiscountPremium:           int32(safetyDiscountPremium),
		RoundedSafetyDiscountPremium:    int32(math.Round(safetyDiscountPremium)),
		SafetyDiscountPercentage:        int32(safetyDiscountPercent * 100),
		RoundedSafetyDiscountPercentage: int32(math.Round(safetyDiscountPercent * 100)),
		RoundedPreDiscountTotalPremium:  preDiscountPremium,
		TotalSurchargePremium:           totalSurchargePremium,
		NegotiatedRates:                 negotiatedRates,
	}), nil
}

// calculateSubTotalPremium calculates the subtotal premium for a quote, subtracting
// the flat charges and surcharges (if present) from the total premium outputted by rateML.
// Note: this could also be outputted by rateML, but we will do once we settle on a definition
// for subtotal premium.
// TODO: make rateML output subtotal premium
func calculateSubTotalPremium(totalPremium, flatCharges int32, totalSurchargePremium *int32) int32 {
	subtotalPremium := totalPremium - flatCharges
	if totalSurchargePremium != nil {
		subtotalPremium -= *totalSurchargePremium
	}
	return subtotalPremium
}

// calculatePremiumPerUnit calculates total premium per power unit, by
// dividing the total premium by the number of power units.
func calculatePremiumPerUnit(totalPremium, numUnits float64) int32 {
	return int32(totalPremium) / int32(numUnits)
}

// calculatePreDiscountTotalPremium returns the pre-discount total premium
// considering the non-rounded values outputted by rateML.
func calculatePreDiscountTotalPremium(totalPremium, safetyDiscountPremium int32) int32 {
	// For the case where a surcharge is being applied (i.e safety discount premium is negative),
	// we want to add the safety discount premium to the total premium. Since the final premium should
	// be more than the pre discount premium.

	// For the case where a discount is being applied (i.e safety discount premium is positive),
	// we want to add the safety discount premium to the total premium, since the final premium should
	// be less than the pre discount premium.

	return totalPremium + safetyDiscountPremium
}
