package quote_generator

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
)

func TestGenerateQuoteOption(t *testing.T) {
	ctx := context.Background()
	submission := &application.SubmissionObject{
		ID: uuid.New().String(),
		ModelInput: application.ModelInput{
			RateMLUnderwritingEntityInputs: application.RateMLUnderwritingEntityInputs{
				SafetyModAllCov: 0.05,
			},
			CompanyInfo: &application.CompanyInfo{
				ProjectedMileage: 1000000,
				USState:          us_states.OH,
			},
			EquipmentInfo: &application.EquipmentInfo{
				EquipmentList: application.EquipmentList{
					Info: []application.EquipmentListRecord{
						{
							VIN: "1",
						},
						{
							VIN: "2",
						},
						{
							VIN: "3",
						},
						{
							VIN: "4",
						},
						{
							VIN: "5",
						},
					},
				},
			},
			CoverageInfo: &application.CoverageInfo{
				Coverages: []application.CoverageDetails{
					{
						CoverageType: app_enums.CoverageAutoLiability,
					},
					{
						CoverageType: app_enums.CoverageAutoPhysicalDamage,
					},
					{
						CoverageType: app_enums.CoverageMotorTruckCargo,
					},
					{
						CoverageType: app_enums.CoverageGeneralLiability,
					},
				},
			},
		},
	}
	modelOutput := model_output.ModelOutput{
		FleetPowerUnitCount:      3,
		Tiv:                      1000000,
		NegotiatedRateFlag:       true,
		Rule15ExemptionFlag:      true,
		Rule15ExemptionPremium:   1000,
		Rule15ExemptionThreshold: 500,
		NewModelOutputStandardPackage: model_output.NewModelOutputStandardPackage{
			TotalStandardPolicyPremium:           10000,
			FlatStandardPolicyPremium:            500,
			TotalStandardPolicyPremiumUnmodified: pointer_utils.Float64(9000),
			TotalStandardSurchargePremium:        pointer_utils.Float64(1000),

			// AL
			NonFlatLiabStandardPolicyPremium:            10000,
			NonFlatLiabStandardPolicyPremiumPpu:         100,
			TraditionalNonFlatLiabStandardPolicyPremium: 1000,
			NegotiatedNonFlatLiabStandardPolicyPremium:  500,

			// APD
			NonFlatPhysStandardPolicyPremium:            20000,
			NonFlatPhysStandardPolicyPremiumPtiv:        200,
			TraditionalNonFlatPhysStandardPolicyPremium: 2000,
			NegotiatedNonFlatPhysStandardPolicyPremium:  1000,
			CoverageFinalModPremiumCompStandard:         pointer_utils.Float64(1000),
			CoverageFinalModPremiumCollStandard:         pointer_utils.Float64(1000),

			// MTC
			MtcStandardPolicyPremium:    30000,
			MtcStandardPolicyPremiumPpu: 300,

			// GL
			GlStandardPolicyPremium: 40000,
		},
		PackageType: app_enums.IndicationOptionTagStandard,
	}

	packageType := app_enums.IndicationOptionTagStandard

	quoteOption, err := GenerateQuoteOption(ctx, submission, &modelOutput, packageType)
	assert.NoError(t, err)
	assert.NotNil(t, quoteOption)
	quoteOption.CreatedAt = time.Time{}

	expectedQuoteOption := &application.IndicationOption{
		ID:           quoteOption.ID,
		SubmissionID: submission.ID,
		Coverages: []application.CoverageDetails{
			{
				CoverageType: app_enums.CoverageAutoLiability,
				Limit:        pointer_utils.Int32(app_logic.DefaultALLimit),
				SymbolsAndDefinitions: pointer_utils.ToPointer([]application.SymbolAndDefinition{
					app_logic.Symbol72,
				}),
				Premium:                pointer_utils.Int32(10000),
				PremiumPerUnit:         pointer_utils.Int32(100),
				PremiumPerHundredMiles: pointer_utils.Float32(1),
				TraditionalPremium:     pointer_utils.Int32(1000),
				NegotiatedPremium:      pointer_utils.Int32(500),
			},
			{
				CoverageType: app_enums.CoverageAutoPhysicalDamage,
				Limit:        pointer_utils.Int32(app_logic.DefaultAPDLimit),
				SymbolsAndDefinitions: pointer_utils.ToPointer([]application.SymbolAndDefinition{
					app_logic.Symbol64,
				}),
				Premium:              pointer_utils.Int32(20000),
				TIVPercentage:        pointer_utils.Float32(20000),
				TraditionalPremium:   pointer_utils.Int32(2000),
				NegotiatedPremium:    pointer_utils.Int32(1000),
				ComprehensivePremium: pointer_utils.ToPointer(1000.0),
				CollisionPremium:     pointer_utils.ToPointer(1000.0),
			},
			{
				CoverageType:           app_enums.CoverageMotorTruckCargo,
				Premium:                pointer_utils.Int32(30000),
				PremiumPerUnit:         pointer_utils.Int32(300),
				PremiumPerHundredMiles: pointer_utils.Float32(3),
			},
			{
				CoverageType: app_enums.CoverageGeneralLiability,
				Limit:        pointer_utils.Int32(app_logic.DefaultGLOccurrenceLimit),
				Premium:      pointer_utils.Int32(40000),
			},
		},
		OptionTag:                       packageType,
		IsRecommended:                   true,
		TotalPremium:                    10000,
		SubtotalPremium:                 8500,
		FlatCharges:                     500,
		SafetyDiscountPremium:           526,
		SafetyDiscountPercentage:        5,
		RoundedSafetyDiscountPremium:    526,
		RoundedSafetyDiscountPercentage: 5,
		RoundedPreDiscountTotalPremium:  10526,
		TotalPowerUnits:                 3,
		TotalTrailerUnits:               2,
		TIV:                             1000000,
		TotalMiles:                      1000000,
		PremiumPerUnit:                  3333,
		TotalSurchargePremium:           pointer_utils.Int32(1000),
		TotalPolicyPremiumUnmodified:    pointer_utils.Int32(9000),
		NegotiatedRates: &application.NegotiatedRates{
			IsNegotiatedRatesApplicable: true,
			IsNegotiatedRatesApplied:    false,
			BaseLimitPremium:            pointer_utils.Int64(1000),
			ThresholdPremium:            pointer_utils.Int64(500),
			Rules: []application.NegotiatedRatesRule{
				{
					RuleType:     app_enums.NegotiatedRatesExemptionRuleFifteen,
					IsApplicable: true,
				},
			},
		},
		CreatedAt: time.Time{},
	}
	assert.Equal(t, expectedQuoteOption, quoteOption)
	assert.Equal(t, submission.ID, quoteOption.SubmissionID)
}
