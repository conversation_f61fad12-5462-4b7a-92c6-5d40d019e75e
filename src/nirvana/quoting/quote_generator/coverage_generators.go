package quote_generator

import (
	"math"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
)

func generateAutoLiabilityCoverage(
	coverageInfo application.CoverageDetails,
	modelOutput *model_output.ModelOutput,
	isQuote bool,
	projectedMileage int,
	usState us_states.USState,
) (*application.CoverageDetails, error) {
	totalPremium := int32(modelOutput.GetLiabPolicyPremium())
	premiumPerPU := int32(modelOutput.GetLiabPolicyPremiumPpu())
	if isQuote {
		totalPremium = int32(modelOutput.GetNonFlatLiabPolicyPremium())
		premiumPerPU = int32(modelOutput.GetNonFlatLiabPolicyPremiumPpu())
	}
	traditionalPremium := int32(modelOutput.GetTraditionalNonFlatLiabPolicyPremium())
	negotiatedPremium := int32(modelOutput.GetNegotiatedNonFlatLiabPolicyPremium())

	var details application.CoverageDetails
	details.CoverageType = coverageInfo.CoverageType
	details.Deductible = coverageInfo.Deductible
	details.Premium = pointer_utils.Int32(totalPremium)
	details.PremiumPerUnit = pointer_utils.Int32(premiumPerPU)
	details.PremiumPerHundredMiles = pointer_utils.ToPointer(
		calculatePremiumPerHundredMiles(
			totalPremium,
			int32(projectedMileage)),
	)
	limit, err := app_logic.GetCoverageLimit(coverageInfo.CoverageType, usState)
	if err != nil {
		return nil, errors.Wrapf(err,
			"unable to get limit for %s coverage and state %s",
			coverageInfo.CoverageType.String(),
			usState,
		)
	}
	details.Limit = limit
	details.SymbolsAndDefinitions = app_logic.GetSymbolsAndDefinitions(details)
	details.TraditionalPremium = pointer_utils.ToPointer(traditionalPremium)
	details.NegotiatedPremium = pointer_utils.ToPointer(negotiatedPremium)
	if modelOutput.GetLiabilityPolicyPremiumUnmodified() != nil {
		details.UnmodifiedPremium = pointer_utils.Int32(int32(*modelOutput.GetLiabilityPolicyPremiumUnmodified()))
	}
	return &details, nil
}

func generatePhysicalDamageCoverage(
	coverageInfo application.CoverageDetails,
	modelOutput *model_output.ModelOutput,
	isQuote bool,
	usState us_states.USState,
) (*application.CoverageDetails, error) {
	totalPremium := int32(modelOutput.GetPhysPolicyPremium())
	tivPercentage := modelOutput.GetPhysPolicyPremiumPtiv()
	if isQuote {
		totalPremium = int32(modelOutput.GetNonFlatPhysPolicyPremium())
		tivPercentage = modelOutput.GetNonFlatPhysPolicyPremiumPtiv()
	}
	traditionalPremium := int32(modelOutput.GetTraditionalNonFlatPhysPolicyPremium())
	negotiatedPremium := int32(modelOutput.GetNegotiatedNonFlatPhysPolicyPremium())
	var details application.CoverageDetails
	details.CoverageType = coverageInfo.CoverageType
	details.Deductible = coverageInfo.Deductible
	details.Premium = pointer_utils.Int32(totalPremium)
	details.TIVPercentage = pointer_utils.Float32(float32(math.Round(tivPercentage*100000.0) / 1000.0))
	limit, err := app_logic.GetCoverageLimit(coverageInfo.CoverageType, usState)
	if err != nil {
		return nil, errors.Wrapf(err,
			"unable to get limit for %s coverage and state %s",
			coverageInfo.CoverageType.String(),
			usState,
		)
	}
	details.Limit = limit
	details.SymbolsAndDefinitions = app_logic.GetSymbolsAndDefinitions(details)
	if coverageInfo.SymbolsAndDefinitions != nil {
		details.SymbolsAndDefinitions = coverageInfo.SymbolsAndDefinitions
	}
	details.TraditionalPremium = pointer_utils.ToPointer(traditionalPremium)
	details.NegotiatedPremium = pointer_utils.ToPointer(negotiatedPremium)
	details.ComprehensivePremium = modelOutput.GetComprehensivePremium()
	details.CollisionPremium = modelOutput.GetCollisionPremium()
	if modelOutput.GetPhysicalPolicyPremiumUnmodified() != nil {
		details.UnmodifiedPremium = pointer_utils.Int32(int32(*modelOutput.GetPhysicalPolicyPremiumUnmodified()))
	}
	return &details, nil
}

func generateMotorTruckCargoCoverage(
	coverageInfo application.CoverageDetails,
	modelOutput *model_output.ModelOutput,
	projectedMileage int,
) (*application.CoverageDetails, error) {
	totalPremium := int32(modelOutput.GetMtcPolicyPremium())
	var details application.CoverageDetails
	cov := app_enums.CoverageMotorTruckCargo
	details.CoverageType = cov
	details.Premium = pointer_utils.Int32(totalPremium)
	details.Deductible = coverageInfo.Deductible
	details.Limit = coverageInfo.Limit
	details.PremiumPerUnit = pointer_utils.ToPointer(int32(modelOutput.GetMtcPolicyPremiumPpu()))
	details.PremiumPerHundredMiles = pointer_utils.ToPointer(
		calculatePremiumPerHundredMiles(
			totalPremium,
			int32(projectedMileage),
		))
	if modelOutput.GetMtcPolicyPremiumUnmodified() != nil {
		details.UnmodifiedPremium = pointer_utils.Int32(int32(*modelOutput.GetMtcPolicyPremiumUnmodified()))
	}
	return &details, nil
}

func generateGeneralLiabilityCoverage(
	coverageInfo application.CoverageDetails,
	modelOutput *model_output.ModelOutput,
	usState us_states.USState,
) (*application.CoverageDetails, error) {
	var details application.CoverageDetails
	details.CoverageType = coverageInfo.CoverageType
	details.Premium = pointer_utils.ToPointer(int32(modelOutput.GetGlPolicyPremium()))
	limit, err := app_logic.GetCoverageLimit(coverageInfo.CoverageType, usState)
	if err != nil {
		return nil, errors.Wrapf(err,
			"unable to get limit for %s coverage and state %s",
			coverageInfo.CoverageType.String(),
			usState,
		)
	}
	details.Limit = limit
	details.Deductible = coverageInfo.Deductible
	if modelOutput.GetGLPolicyPremiumUnmodified() != nil {
		details.UnmodifiedPremium = pointer_utils.Int32(int32(*modelOutput.GetGLPolicyPremiumUnmodified()))
	}
	return &details, nil
}

func calculatePremiumPerHundredMiles(premium, miles int32) float32 {
	return float32(premium) / float32(miles) * 100.0
}

func calculateNumberOfTrailerUnits(numTotalUnits, numPowerUnits int32) int32 {
	return numTotalUnits - numPowerUnits
}
