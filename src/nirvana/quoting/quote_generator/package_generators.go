package quote_generator

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
)

func generatePackageCoverages(
	submission *application.SubmissionObject,
	modelOutput *model_output.ModelOutput,
	isQuote bool,
) ([]application.CoverageDetails, error) {
	var cov *application.CoverageDetails
	var err error
	var coverages []application.CoverageDetails
	for _, coverage := range submission.CoverageInfo.Coverages {
		switch coverage.CoverageType { //nolint:exhaustive
		case app_enums.CoverageAutoPhysicalDamage:
			cov, err = generatePhysicalDamageCoverage(coverage, modelOutput, isQuote, submission.CompanyInfo.USState)
			if err != nil {
				return nil, errors.Wrapf(err, "Failed to generate APD coverages")
			}
		case app_enums.CoverageMotorTruckCargo:
			cov, err = generateMotorTruckCargoCoverage(coverage, modelOutput, submission.CompanyInfo.ProjectedMileage)
			if err != nil {
				return nil, errors.Wrapf(err, "Failed to generate MTC coverages")
			}
		case app_enums.CoverageGeneralLiability:
			cov, err = generateGeneralLiabilityCoverage(coverage, modelOutput, submission.CompanyInfo.USState)
			if err != nil {
				return nil, errors.Wrapf(err, "Failed to generate GL coverages")
			}
		case app_enums.CoverageAutoLiability:
			cov, err = generateAutoLiabilityCoverage(
				coverage,
				modelOutput,
				isQuote,
				submission.CompanyInfo.ProjectedMileage,
				submission.CompanyInfo.USState,
			)
			if err != nil {
				return nil, errors.Wrapf(err, "Failed to generate AL coverages")
			}
		default:
			// TODO: Currently, ancillary coverages are populated directly from the submission,
			// and they lack corresponding premium values in the model output (coupled to primary coverages).
			// Since premiums for ancillary coverages are not available
			// in the current model output, we are omitting premium handling here.
			//
			// Future Work: Once premiums for ancillary coverages are incorporated in the model
			// output, integrate the relevant values here.
			// This will enable premium-based calculations for these sub-coverages as part of
			// generatePackageCoverages and ensure consistency across all coverage types.
			cov = &coverage
		}
		coverages = append(coverages, *cov)
	}
	return coverages, nil
}
