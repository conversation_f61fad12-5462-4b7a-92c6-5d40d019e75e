package quote_generator

import (
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
)

var statesWithSurchargeInfo = map[us_states.USState]bool{
	us_states.KY: true,
	us_states.CA: true,
}

// nolint:unused
func isSurchargeInfoNil(s *model_output.ModelOutput) bool {
	return s.GetTotalInsuranceSurchargePremium() == nil ||
		s.GetAutoInsuranceSurchargePremium() == nil ||
		s.GetGLInsuranceSurchargePremium() == nil ||
		s.GetMTCInsuranceSurchargePremium() == nil ||
		s.GetAutoLGPTSurchargePremium() == nil ||
		s.GetGLLGPTSurchargePremium() == nil ||
		s.GetMTCLGPTSurchargePremium() == nil
}

func GetTotalSurchargePremiumFromModelOutput(m *model_output.ModelOutput) *int32 {
	totalSurchargePremiumKY := m.GetTotalSurchargePremiumKy()
	byPuSurchargePremium := m.GetByPuSurchargePremium()
	totalSurchargePremiumMI := m.GetTotalSurchargePremiumMi()
	totalByCovSurchargePremium := m.GetTotalByCovSurchargePremium()
	totalSurchargePremium := m.GetTotalSurchargePremium()

	if m.GetTotalSurchargePremium() != nil {
		totalSurchargePremium = m.GetTotalSurchargePremium()
	}

	switch {
	case totalSurchargePremiumKY != nil && *totalSurchargePremiumKY > 0:
		return ParseFloat64ToInt32Pointer(*totalSurchargePremiumKY)
	case totalSurchargePremiumMI != nil && *totalSurchargePremiumMI > 0:
		return ParseFloat64ToInt32Pointer(*totalSurchargePremiumMI)
	case byPuSurchargePremium != nil && *byPuSurchargePremium > 0:
		return ParseFloat64ToInt32Pointer(*byPuSurchargePremium)
	case totalByCovSurchargePremium != nil && *totalByCovSurchargePremium > 0:
		return ParseFloat64ToInt32Pointer(*totalByCovSurchargePremium)
	case totalSurchargePremium != nil && *totalSurchargePremium > 0:
		return ParseFloat64ToInt32Pointer(*totalSurchargePremium)
	default:
		return nil
	}
}

func ParseFloat64ToInt32Pointer(v float64) *int32 {
	return pointer_utils.Int32(int32(v))
}
