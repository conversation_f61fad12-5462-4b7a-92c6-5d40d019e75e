load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "quote_generator",
    srcs = [
        "app_pdf_gen.go",
        "coverage_generators.go",
        "gen_quote_pdf_inputs.go",
        "indication_options.go",
        "package_generators.go",
        "pdf_gen_deps.go",
        "quote_options.go",
        "quote_pdf_gen.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/quoting/quote_generator",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/billing/legacy/payment_option",
        "//nirvana/billing/legacy/payment_option/enums",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/math_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/appetite_lite",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/pdfgen",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator",
        "//nirvana/policy_common/forms_generator/forms",
        "//nirvana/quoting/ancillary_coverages",
        "//nirvana/quoting/app_state_machine/app_logic",
        "//nirvana/quoting/utils",
        "//nirvana/rating/adaptors/fleet_adaptor/common",
        "//nirvana/rating/models/models_release",
        "//nirvana/rating/rtypes",
        "//nirvana/telematics/connections",
        "//nirvana/underwriting/common-utils",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_jinzhu_copier//:copier",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_modernc_mathutil//:mathutil",
    ],
)

go_test(
    name = "quote_generator_test",
    srcs = ["quote_options_test.go"],
    embed = [":quote_generator"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/quoting/app_state_machine/app_logic",
        "//nirvana/rating/adaptors/fleet_adaptor/common",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
    ],
)
