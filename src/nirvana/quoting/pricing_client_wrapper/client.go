package pricing_client_wrapper

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/problem"
	"nirvanatech.com/nirvana/common-go/proto"
	slices_util "nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/vin_utils"
	"nirvanatech.com/nirvana/common-go/zip_code_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/fleet/model"
	app_chkr "nirvanatech.com/nirvana/quoting/appetite_checker"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/data_fetching/mvr_fetching"
	"nirvanatech.com/nirvana/rating/data_fetching/rating_tier"
	"nirvanatech.com/nirvana/rating/data_processing/lni_processing"
	"nirvanatech.com/nirvana/rating/data_processing/mvr_processing"
	"nirvanatech.com/nirvana/rating/data_processing/vin_processing"
	"nirvanatech.com/nirvana/rating/data_processing/vin_processing/iso_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/client_helpers/fleet"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/rating/utils"
)

const (
	defaultDriverMVRViolations                 = 0
	attractScoreStaleness                      = 90
	attractScoreFetchTimeout                   = 5 * time.Second
	defaultDriverTenureInYears                 = float64(0)
	defaultRetainedYears                       = 0
	defaultDriverModifiedMVRScoreForIndication = 2
	defaultUnsafeDrivingScore                  = float64(0)
)

// calculateTenureInYears calculates the tenure in years between dateHired and effectiveDate.
// Returns 0 if dateHired is zero or after effectiveDate.
func calculateTenureInYears(dateHired, effectiveDate time.Time) float64 {
	if dateHired.IsZero() || dateHired.After(effectiveDate) {
		return defaultDriverTenureInYears
	}
	return time_utils.Years(effectiveDate.Sub(dateHired))
}

// GetPrice is a wrapper function that performs transformations to
// the inputs received before calling the model runner. Essentially,
// it implements a facade on top of it.
//
// This transformation will be needed to transition to the new API
// in incremental chunks. In other words, we will add transformations
// to this function one-by-one.
//
// This wrapper also helps us avoid having to implement the
// transformations on every caller.
//
// Eventually the ModelRunner component should be eliminated, and
// instead this wrapper will call the new Pricing API (i.e. gRPC call).
func GetPrice(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	processorClient data_processing.ProcessorClient,
	modelRunner *fleet_adaptor.ModelRunner,
	mi *application.ModelInput,
	config *utils.ModelRunConfig,
	oldProbs *problem.Problems,
	artifactFileKey string,
	optionalPlugins []ptypes.PluginID,
) (*model_output.ModelOutput, *problem.Problems, error) {
	if mi == nil {
		return nil, nil, errors.New("model input is nil")
	}

	if config == nil {
		return nil, nil, errors.New("model run config is nil")
	}

	if config.PackageType == nil {
		return nil, nil, errors.New("package type is nil")
	}

	if oldProbs == nil {
		oldProbs = problem.New()
	}

	newProblems := problem.New()

	populateSafetyDiscountFields(mi, config)
	populateLargeLossesFields(mi, config)

	companyExtraPricingInfo, err := getCompanyExtraPricingInfo(
		ctx,
		fetcherClient,
		processorClient,
		mi,
		config,
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get company extra pricing info")
	}

	decodedVehicles, err := getDecodedVehicles(ctx, fetcherClient, processorClient, mi, config, oldProbs)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get decoded vehicles")
	}

	// We do this because the VIN decoding flow doesn't return new
	// problems, it modifies the ones received.
	newProblems.AddBulk(oldProbs)

	rateableDrivers, driverProblems, err := getRateableDrivers(
		ctx,
		fetcherClient,
		processorClient,
		mi,
		config,
		oldProbs,
		decodedVehicles,
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get rateable drivers")
	}

	// We do this because the driver fetching/processing flow
	// returns new problems, it doesn't modify the ones received.
	newProblems.AddBulk(driverProblems)

	err = replaceAppetiteCheckProblems(ctx, newProblems)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unable to replace problems for appetite check tag")
	}

	if newProblems.HasUnresolvedProblems() {
		log.Error(
			ctx,
			"Unresolved problems in model",
			log.Any("problems", newProblems),
		)
		return nil, nil, errors.Newf(
			"Unresolved problems in model. Problems = %+v",
			newProblems,
		)
	}

	mi.ExtraPricingInfo = &application.ExtraPricingInfo{
		PackageType:             *config.PackageType,
		CompanyExtraPricingInfo: companyExtraPricingInfo,
		DecodedVehicles:         decodedVehicles,
		RateableDrivers:         rateableDrivers,
	}

	// Deep copy the full LossRunSummary
	original := make([]application.LossRunSummaryPerCoverage, len(mi.LossInfo.LossRunSummary))
	for i, cov := range mi.LossInfo.LossRunSummary {
		original[i] = application.LossRunSummaryPerCoverage{
			CoverageType: cov.CoverageType,
			Summary:      append([]application.LossRunSummaryRecord(nil), cov.Summary...),
		}
	}

	defer func() {
		// Restore LossRunSummary (coverages deletions, reordering, and mutations)
		mi.LossInfo.LossRunSummary = original
	}()

	err = tagYearlyLossSummaryPeriods(mi)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to tag yearly loss summary periods")
	}

	input, err := transformModelInput(mi)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to transform model input")
	}

	modelKey := getModelKey(config)
	output, err := modelRunner.Run(
		ctx,
		&fleet_adaptor.ModelRunnerRunInputs{
			Input:           input,
			ModelKey:        modelKey,
			ArtifactFileKey: artifactFileKey,
			OptionalPlugins: optionalPlugins,
		},
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to run model")
	}

	return output, newProblems, nil
}

func getCompanyExtraPricingInfo(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	processorClient data_processing.ProcessorClient,
	mi *application.ModelInput,
	config *utils.ModelRunConfig,
) (*application.CompanyExtraPricingInfo, error) {
	zipCode, usState, err := getCompanyAddressInfo(
		ctx,
		fetcherClient,
		mi,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get final company zip code")
	}

	yearsInBusiness := lni_processing.GetFleetYearsInBusinessFromAuthorityHistoryWithDefaultV1(
		ctx,
		processorClient,
		mi.CompanyInfo.DOTNumber,
		mi.CoverageInfo.EffectiveDate,
	)

	ratingTierRecordDates := config.Hyperparams.RatingTierRecordDates
	if !ratingTierRecordDates.IsValid() {
		return nil, errors.Wrap(err, "nil rating tier record dates")
	}

	ratingTierRecord, err := rating_tier.GetRatingTierRecordWithDefaultsV1(
		ctx,
		fetcherClient,
		ratingTierRecordDates,
		mi.CompanyInfo.DOTNumber,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get rating tier record with defaults")
	}

	totalDriversLastYear, driversHiredLastYear := getDriverCounts(mi, config)

	latestObjectiveGrade, err := getLatestObjectiveGrade(ctx, fetcherClient, mi, config)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get latest objective grade")
	}

	nirvanaYearsRetained, err := getNirvanaRetainedYears(ctx, processorClient, mi)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get nirvana retained years")
	}

	modelKey := getModelKey(config)
	isPriorCarrierYearsRetainedRequired, err := fleet.IsPriorCarrierYearsRetainedRequired(modelKey)
	if err != nil {
		return nil, errors.Wrap(err, "unable to check if prior carrier years retained is required")
	}

	var priorCarrierYearsRetained *int64
	if isPriorCarrierYearsRetainedRequired {
		priorCarrierYearsRetained = getPriorCarrierYearsRetained(ctx, processorClient, mi)
	}

	unsafeDrivingScore := ratingTierRecord.UnsafeDrivingScore
	if unsafeDrivingScore == nil {
		unsafeDrivingScore = pointer_utils.Float64(defaultUnsafeDrivingScore)
	}

	return &application.CompanyExtraPricingInfo{
		USState:                    usState,
		ZipCode:                    zipCode,
		YearsInBusiness:            int64(yearsInBusiness),
		AverageMiles:               ratingTierRecord.GetAverageMiles(),
		AverageCombinedGrossWeight: ratingTierRecord.GetAverageCombinedGrossWeight(),
		CrashFrequency:             ratingTierRecord.GetCrashFrequency(),
		VehicleInspectionRatio:     ratingTierRecord.GetVehicleInspectionRatio(),
		MaintenanceViolationsRatio: ratingTierRecord.GetMaintenanceViolationsRatio(),
		UnsafeViolationRatio:       ratingTierRecord.GetUnsafeViolationRatio(),
		UnsafeDrivingScore:         unsafeDrivingScore,
		InspectionIndicator:        ratingTierRecord.GetInspectionIndicator(),
		LargeMachineryIndicator:    ratingTierRecord.GetLargeMachineryIndicator(),
		PowerUnits:                 ratingTierRecord.GetPowerUnits(),
		DriversHiredLastYear:       driversHiredLastYear,
		TotalDriversLastYear:       totalDriversLastYear,
		ObjectiveGrade:             latestObjectiveGrade,
		NirvanaYearsRetained:       pointer_utils.ToPointer(nirvanaYearsRetained),
		PriorCarrierYearsRetained:  priorCarrierYearsRetained,
	}, nil
}

func getCompanyAddressInfo(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	mi *application.ModelInput,
) (string, string, error) {
	census, err := fetcherClient.GetFMCSACensusInfoV1(
		ctx,
		&data_fetching.FMCSACensusInfoRequestV1{DotNumber: mi.CompanyInfo.DOTNumber},
	)
	if err != nil {
		return "", "", errors.Wrapf(
			err,
			"failed to get fmcsa census for DOTNumber: %d",
			mi.CompanyInfo.DOTNumber,
		)
	}

	usState := census.GetPhysicalAddressState()

	var zipCode string
	if mi.UnderwriterInput != nil && mi.UnderwriterInput.RatingAddress != nil {
		zipCode, err = zip_code_utils.ParseFromZipToZip5(mi.UnderwriterInput.RatingAddress.ZipCodeString)
		if err != nil {
			return "", "", errors.Wrap(err, "unable to parse zip to zip 5")
		}
	} else {
		zipCode, err = extractZip5FromCensusData(census)
		if err != nil {
			return "", "", errors.Wrap(err, "unable to extract zip 5 from census data")
		}
	}

	return zipCode, usState, nil
}

func extractZip5FromCensusData(census *data_fetching.FMCSACensusInfoV1) (string, error) {
	if census == nil {
		return "", errors.New("DOT census details empty. Can't extract ZIP")
	}
	zipCode := census.GetPhysicalAddressZipCode()
	if zipCode == "" {
		return "", errors.New("Zip code empty in DOT census.")
	}
	zip5, err := zip_code_utils.ParseFromZipToZip5(zipCode)
	if err != nil {
		return "", errors.Wrapf(err, "Failed to extract zip from %s", zipCode)
	}
	return zip5, nil
}

func getLatestObjectiveGrade(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	mi *application.ModelInput,
	config *utils.ModelRunConfig,
) (*string, error) {
	modelKey := getModelKey(config)
	isCompanyObjectiveGradeRequired, err := fleet.IsCompanyObjectiveGradeRequired(modelKey)
	if err != nil {
		return nil, err
	}

	if !isCompanyObjectiveGradeRequired {
		return nil, nil //nolint:nilnil
	}

	reval := "X"

	dotNumber := mi.CompanyInfo.DOTNumber
	objectiveGrade, err := fetcherClient.GetLatestObjectiveGradeV1(
		ctx,
		&data_fetching.LatestObjectiveGradeRequestV1{
			DotNumber: dotNumber,
		},
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to get latest objective grade V1",
			log.Int64("DOTNumber", dotNumber),
			log.Err(err),
		)
	} else {
		score := objectiveGrade.GetScore()
		if score != data_fetching.ObjectiveGradeScoreV1_Undefined {
			reval = score.String()
		}
	}

	return &reval, nil
}

func getDecodedVehicles(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	processorClient data_processing.ProcessorClient,
	mi *application.ModelInput,
	config *utils.ModelRunConfig,
	probs *problem.Problems,
) ([]*application.DecodedVehicle, error) {
	vins := mi.EquipmentInfo.VINsList()
	vinData, err := vin_processing.FetchVinDataWithDefaultsV1(
		ctx,
		vins,
		probs,
		fetcherClient,
		processorClient,
		config.ApplicationFlags.PrefillAndAutoReviewVinProblems,
	)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch vin data")
	}

	equipmentList := mi.EquipmentInfo.EquipmentList.Info
	decodedVehicles := make([]*application.DecodedVehicle, 0)
	for _, eq := range equipmentList {
		vin := vin_utils.TransformVIN(eq.VIN)

		vinInfo, ok := vinData.VinDetailsMap[vin]
		if !ok {
			return nil, errors.Newf("Couldn't find VIN in decoder result map for vin %s", vin)
		}

		if vinInfo.ShouldSkip {
			log.Info(
				ctx,
				"skipping vehicle instead of creating it",
				log.Any("vin", vin),
				log.Any("vinInfo", vinInfo),
				log.Int32("statedValue", *eq.StatedValue),
			)
			continue
		}

		if eq.StatedValue == nil {
			return nil, errors.Newf("Stated value is nil for vin %s", vin)
		}

		yearMade, err := strconv.ParseInt(vinInfo.ModelYear, 10, 64)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"unable to parse int for model year %s for vin %s",
				vinInfo.ModelYear,
				vin,
			)
		}

		if vinInfo.IsoVehicleType == nil {
			return nil, errors.Newf(
				"missing iso veh type for vin %s with vin info %+v",
				vin,
				vinInfo,
			)
		}

		if vinInfo.IsoWeightGroup == nil {
			return nil, errors.Newf(
				"missing iso weight group for vin %s with vin info %+v",
				vin,
				vinInfo,
			)
		}

		// Read vehicle zone values from fixes, if present
		var vehicleStartZone *string
		var vehicleEndZone *string
		prob := probs.Get(vin_processing.VinProblemTag, vin)
		if prob != nil {
			vinDecodeProb, ok := prob.(*vin_processing.VinDecodeProblem)
			if ok {
				if vinDecodeProb.Fixes.VehicleStartZone != nil {
					vehicleStartZone = vinDecodeProb.Fixes.VehicleStartZone
				}
				if vinDecodeProb.Fixes.VehicleEndZone != nil {
					vehicleEndZone = vinDecodeProb.Fixes.VehicleEndZone
				}
			}
		}

		decodedVehicles = append(decodedVehicles, &application.DecodedVehicle{
			VIN:            vin,
			StatedValue:    float64(*eq.StatedValue),
			YearMade:       yearMade,
			IsoVehicleType: *vinInfo.IsoVehicleType,
			IsoWeightGroup: *vinInfo.IsoWeightGroup,
			StartZone:      vehicleStartZone,
			EndZone:        vehicleEndZone,
		})
	}

	return decodedVehicles, nil
}

func getDriverCounts(
	mi *application.ModelInput,
	config *utils.ModelRunConfig,
) (int64, int64) {
	driverCountTotalLastYear, driverCountNewHiredLastYear := int64(10), int64(4)

	if mi.DriversInfo != nil && (config.UseDriverMVRs || shouldFetchAttractScores(mi)) {
		driverCountTotalLastYear, driverCountNewHiredLastYear = 0, 0
		for _, d := range mi.DriversInfo.Drivers {
			driverCountTotalLastYear++
			if d.DateHired.After(mi.CoverageInfo.EffectiveDate.AddDate(-1, 0, 0)) {
				driverCountNewHiredLastYear++
			}
		}
	}

	return driverCountTotalLastYear, driverCountNewHiredLastYear
}

func getRateableDrivers(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	processorClient data_processing.ProcessorClient,
	mi *application.ModelInput,
	config *utils.ModelRunConfig,
	oldProbs *problem.Problems,
	decodedVehicles []*application.DecodedVehicle,
) ([]*application.RateableDriver, *problem.Problems, error) {
	if mi.DriversInfo == nil {
		return createFakeRateableDriversWithDefaults(decodedVehicles), nil, nil
	}

	drivers := mi.DriversInfo.Drivers

	violationCounts := make(map[string]float64)

	var newProbs *problem.Problems
	var err error

	if config.UseDriverMVRs {
		violationCounts, newProbs, err = mvr_processing.GetFleetMovingViolationCountForDriversV1(
			mi,
			oldProbs,
			config.RateMLConfig.Flags.IsUncountedMVCEnabled,
			fetcherClient,
			processorClient,
		)
		if err != nil {
			return nil, nil, errors.Wrap(err, "unable to get moving violation counts")
		}
	}

	modelKey := getModelKey(config)
	isDriverAttractScoreRequired, err := fleet.IsDriverAttractScoreRequired(modelKey)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unable to check if driver attract score is required")
	}

	attractScores := make(map[string]float64)
	if isDriverAttractScoreRequired && shouldFetchAttractScores(mi) {
		attractScores, err = fetchDriverAttractScores(ctx, fetcherClient, drivers)
		if err != nil {
			return nil, nil, errors.Wrap(err, "unable to fetch driver attract scores")
		}
	}

	isModifiedMVRScoreRequired, err := fleet.IsModifiedMVRScoreRequired(modelKey)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unable to check if modified MVR score is required")
	}

	modifiedMVRScores := make(map[string]int64)
	if isModifiedMVRScoreRequired && config.UseDriverMVRs {
		modifiedMVRScores, err = mvr_processing.GetFleetModifiedMVRScoreForDriversV1(
			ctx,
			mi,
			fetcherClient,
			processorClient,
		)
		if err != nil {
			return nil, nil, errors.Wrap(err, "unable to get modified MVR scores")
		}
	}

	rateableDrivers := make([]*application.RateableDriver, 0)
	for _, d := range drivers {
		driverId := mvr_fetching.DriverId(d.UsState, d.DriverLicenseNumber)

		movingViolationCount := float64(defaultDriverMVRViolations)
		if value, ok := violationCounts[driverId]; ok {
			movingViolationCount = value
		}

		var attractScore *float64
		if score, ok := attractScores[driverId]; ok {
			attractScore = pointer_utils.ToPointer(score)
		}

		// Calculate tenure in years from date hired to effective date
		tenureInYears := calculateTenureInYears(d.DateHired, mi.CoverageInfo.EffectiveDate)

		modifiedMVRScore, ok := modifiedMVRScores[driverId]
		if !ok {
			log.Warn(ctx, "missing modified MVR score for driver", log.String("driverId", driverId))
			// If MVRs are not pulled, we use the default score for indication for the driver
			if !config.UseDriverMVRs {
				modifiedMVRScore = defaultDriverModifiedMVRScoreForIndication
			}
		}

		rateableDrivers = append(rateableDrivers, &application.RateableDriver{
			Id:                   driverId,
			MovingViolationCount: movingViolationCount,
			AttractScore:         attractScore,
			TenureInYears:        pointer_utils.ToPointer(tenureInYears),
			ModifiedMvrScore:     pointer_utils.ToPointer(int64(modifiedMVRScore)),
		})
	}

	return rateableDrivers, newProbs, nil
}

// createFakeDriversWithDefaults creates one fake driver per vehicle. This
// should only be used for indication, but we are not validating that.
func createFakeRateableDriversWithDefaults(
	decodedVehicles []*application.DecodedVehicle,
) []*application.RateableDriver {
	drivers := make([]*application.RateableDriver, len(decodedVehicles))
	for i := range drivers {
		drivers[i] = &application.RateableDriver{
			Id:                   fmt.Sprintf("driver-%d", i),
			MovingViolationCount: defaultDriverMVRViolations,
			TenureInYears:        pointer_utils.ToPointer(defaultDriverTenureInYears),
			ModifiedMvrScore:     pointer_utils.ToPointer(int64(defaultDriverModifiedMVRScoreForIndication)),
		}
	}
	return drivers
}

func shouldFetchAttractScores(mi *application.ModelInput) bool {
	return mi.UnderwriterInput != nil &&
		mi.UnderwriterInput.FetchAttractScore != nil &&
		*mi.UnderwriterInput.FetchAttractScore
}

func fetchDriverAttractScores(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	drivers []application.DriverListRecord,
) (map[string]float64, error) {
	var reqs []*data_fetching.MVRAttractScoreRequestV1
	for _, driver := range drivers {
		if !driver.FirstName.Valid || driver.FirstName.String == "" {
			continue
		}
		if !driver.LastName.Valid || driver.LastName.String == "" {
			continue
		}

		req := &data_fetching.MVRAttractScoreRequestV1{
			UsState:   driver.UsState,
			DlNumber:  driver.DriverLicenseNumber,
			FirstName: driver.FirstName.String,
			LastName:  driver.LastName.String,
			Staleness: attractScoreStaleness,
		}
		if driver.DateOfBirth.Valid {
			req.Dob = timestamppb.New(driver.DateOfBirth.Time)
		}

		reqs = append(reqs, req)
	}

	attractScoresResults, err := mvr_fetching.GetMVRAttractScoresV1(
		ctx,
		fetcherClient,
		reqs,
		pointer_utils.ToPointer(attractScoreFetchTimeout),
	)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get attract scores")
	}

	attractScores := make(map[string]float64)
	for _, driver := range drivers {
		foundElement := slices_util.Find(attractScoresResults, func(score *mvr_fetching.AttractScoreResult) bool {
			return score != nil && score.DLNumber == driver.DriverLicenseNumber && score.AttractScore != nil
		})
		if foundElement == nil {
			continue
		}

		driverId := mvr_fetching.DriverId(driver.UsState, driver.DriverLicenseNumber)
		attractScores[driverId] = float64(*((*foundElement).AttractScore))
	}

	return attractScores, nil
}

func getModelKey(config *utils.ModelRunConfig) rtypes.ModelKey {
	rmlConfig := config.RateMLConfig
	return rtypes.NewModelKey(
		rmlConfig.Provider,
		rmlConfig.USState,
		rmlConfig.Version,
	)
}

func replaceAppetiteCheckProblems(ctx context.Context, newProblems *problem.Problems) error {
	return newProblems.ReplaceForTag(
		app_chkr.AppetiteCheckProblemTag,
		func(problems *problem.Problems) (*problem.Problems, error) {
			newProbs := problem.New()
			flags := problems.AsArray()
			for idx := range flags {
				r, ok := flags[idx].(*app_chkr.AppetiteCheckProblem)
				if !ok {
					return nil, errors.Newf("unexpected problem not casting. Problem = %+v", flags[idx])
				}
				r.IsResolved = true
				if err := newProbs.Add(r); err != nil {
					return nil, errors.Wrapf(err, "unable to add problem. Problem = %+v", flags[idx])
				}
				log.Info(ctx, "auto-resolving appetite check problem", log.String("ruleId", r.Id()))
			}
			return newProbs, nil
		},
	)
}

// tagYearlyLossSummaryPeriods assigns LossRunSummaryPeriod tags based on
// date order (newest -> oldest).
func tagYearlyLossSummaryPeriods(mi *application.ModelInput) error {
	if mi == nil {
		return errors.New("model input is nil")
	}

	if mi.LossInfo == nil {
		return errors.New("loss info is nil")
	}

	lossRunSummaries := mi.LossInfo.LossRunSummary

	lossSummariesDates, err := extractSortedLossSummaryDates(lossRunSummaries)
	if err != nil {
		return errors.Wrap(err, "error extracting loss summary dates")
	}

	for idx, dates := range lossSummariesDates {
		tag, ok := lossRunSummaryPeriodLabelMap[idx]
		if !ok {
			return errors.Newf(
				"unexpected index for loss run summary period: %d",
				idx,
			)
		}
		for _, recordPerCoverage := range lossRunSummaries {
			err = setPeriodTag(recordPerCoverage.Summary, dates, tag)
			if err != nil {
				return errors.Wrapf(
					err,
					"error setting period tag for coverage %s",
					recordPerCoverage.CoverageType,
				)
			}
		}
	}

	return nil
}

// extractSortedPolicyPeriods sorts each coverage's loss summaries by start date (descending),
// then uses the first coverage to derive the canonical policy period ranges and
// validates that every other coverage has exactly the same dates in the same order.
// It returns an error if any coverage has missing, extra, or mismatched period dates.
// See https://github.com/nirvanatech/nirvana/pull/18982#discussion_r1847384778 for context.
func extractSortedLossSummaryDates(lossRunSummaries []application.LossRunSummaryPerCoverage) ([]periodRange, error) {
	if len(lossRunSummaries) == 0 {
		return nil, errors.New("no loss run summaries provided")
	}

	for _, sumPerCoverage := range lossRunSummaries {
		sort.Sort(application.ByStartDateDesc(sumPerCoverage.Summary))
	}

	firstSummary := lossRunSummaries[0].Summary
	lossSummariesDates := make([]periodRange, len(firstSummary))
	for i, rec := range firstSummary {
		lossSummariesDates[i] = periodRange{
			startDate: rec.PolicyPeriodStartDate,
			endDate:   rec.PolicyPeriodEndDate,
		}
	}

	// 3) Validate all other coverages match lossSummariesDates exactly
	for _, cov := range lossRunSummaries[1:] {
		if len(cov.Summary) != len(lossSummariesDates) {
			return nil, errors.Newf(
				"coverage %s has %d periods; expected %d",
				cov.CoverageType, len(cov.Summary), len(lossSummariesDates),
			)
		}
		for i, rec := range cov.Summary {
			expected := lossSummariesDates[i]
			if !rec.PolicyPeriodStartDate.Equal(expected.startDate) ||
				!rec.PolicyPeriodEndDate.Equal(expected.endDate) {
				return nil, errors.Newf(
					"coverage %s, period %d mismatch: got %s–%s, expected %s–%s",
					cov.CoverageType, i,
					rec.PolicyPeriodStartDate, rec.PolicyPeriodEndDate,
					expected.startDate, expected.endDate,
				)
			}
		}
	}

	return lossSummariesDates, nil
}

// getNirvanaRetainedYears calculates the retained years using only the processor client
// by providing a fetcher request instead of pre-fetched data.
func getNirvanaRetainedYears(
	ctx context.Context,
	processorClient data_processing.ProcessorClient,
	mi *application.ModelInput,
) (int64, error) {
	// Process the policies to get retained years using internal fetching
	retainedYearsResponse, err := processorClient.GetRetainedYearsV1(
		ctx,
		&data_processing.RetainedYearsRequestV1{
			FetcherSpec: &data_processing.RetainedYearsRequestV1_FetcherRequest{
				FetcherRequest: &data_fetching.GetNirvanaPoliciesRequestV1{
					DotNumber:        mi.CompanyInfo.DOTNumber,
					EffectiveDate:    timestamppb.New(mi.CoverageInfo.EffectiveDate),
					Version:          0, // Default version
					MainCoverageType: data_fetching.MainCoverageTypeV1_MainCoverageTypeV1_AutoLiability,
				},
			},
			EffectiveDate: timestamppb.New(mi.CoverageInfo.EffectiveDate),
		},
	)
	if err != nil {
		// Log the error but don't fail the entire request - return default retained years
		log.Error(
			ctx,
			"failed to get nirvana retained years",
			log.Int64("DOTNumber", mi.CompanyInfo.DOTNumber),
			log.Err(err),
		)
		return defaultRetainedYears, nil
	}

	return int64(retainedYearsResponse.Years), nil
}

// getPriorCarrierYearsRetained calculates the years retained with the most recent prior carrier
// (excluding current/target carriers) based on BIPD insurance history.
func getPriorCarrierYearsRetained(
	ctx context.Context,
	processorClient data_processing.ProcessorClient,
	mi *application.ModelInput,
) *int64 {
	// Create request for prior carrier years retained calculation
	request := &data_processing.PriorCarrierYearsRetainedRequestV1{
		FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetcherRequests_{
			FetcherRequests: &data_processing.PriorCarrierYearsRetainedRequestV1_FetcherRequests{
				InsuranceHistoryRequest: &data_fetching.BIPDInsuranceHistoryRequestV1{
					DotNumber: mi.CompanyInfo.DOTNumber,
				},
				ActiveOrPendingInsuranceRequest: &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{
					DotNumber:                   mi.CompanyInfo.DOTNumber,
					ShouldIncludeExcessCoverage: false,
				},
			},
		},
		EffectiveDate: timestamppb.New(mi.CoverageInfo.EffectiveDate),
	}

	response, err := processorClient.GetPriorCarrierYearsRetainedV1(ctx, request)
	if err != nil {
		// Log the error but don't fail the entire request - return default value of 0
		log.Error(
			ctx,
			"failed to get prior carrier years retained",
			log.Int64("DOTNumber", mi.CompanyInfo.DOTNumber),
			log.Time("effectiveDate", mi.CoverageInfo.EffectiveDate),
			log.Err(err),
		)
		return pointer_utils.ToPointer(int64(0))
	}

	return pointer_utils.ToPointer(int64(response.Years))
}

func setPeriodTag(
	summaries []application.LossRunSummaryRecord,
	dates periodRange,
	tag enums.LossRunSummaryPeriod,
) error {
	for i, rec := range summaries {
		if rec.PolicyPeriodStartDate.Equal(dates.startDate) &&
			rec.PolicyPeriodEndDate.Equal(dates.endDate) {
			if rec.PeriodTag != enums.LossRunSummaryPeriodInvalid {
				return errors.Newf(
					"period tag already set for record %s: %s - %s",
					rec.PeriodTag,
					rec.PolicyPeriodStartDate.Format(time.RFC3339),
					rec.PolicyPeriodEndDate.Format(time.RFC3339),
				)
			}
			summaries[i].PeriodTag = tag
			return nil
		}
	}

	return errors.Newf(
		"unable to find matching period for tag %s: %s - %s",
		tag,
		dates.startDate.Format(time.RFC3339),
		dates.endDate.Format(time.RFC3339),
	)
}

type periodRange struct {
	startDate time.Time
	endDate   time.Time
}

var lossRunSummaryPeriodLabelMap = map[int]enums.LossRunSummaryPeriod{
	0: enums.LossRunSummaryPeriodCurrent,
	1: enums.LossRunSummaryPeriodFirstPriorYear,
	2: enums.LossRunSummaryPeriodSecondPriorYear,
	3: enums.LossRunSummaryPeriodThirdPriorYear,
	4: enums.LossRunSummaryPeriodFourthPriorYear,
	5: enums.LossRunSummaryPeriodFifthPriorYear,
}

// TODO: continue populating more fields in the policy chunk spec object (and nested objects).
func transformModelInput(mi *application.ModelInput) (*creator_functions.Input, error) {
	policyStartDate := mi.CoverageInfo.EffectiveDate
	policyEndDate := policyStartDate.AddDate(1, 0, 0)
	policyOriginalDates := &proto.Interval{
		Start: timestamppb.New(policyStartDate),
		End:   timestamppb.New(policyEndDate),
	}

	bundleChunkSpec := &ptypes.BundleSpec_ChunkSpec{
		Data: &ptypes.BundleSpec_ChunkSpec_FleetBundleChunkSpecData{
			FleetBundleChunkSpecData: &ptypes.Fleet_BundleChunkSpecData{},
		},
	}

	scs, lss, dss, err := transformSubCoverages(mi)
	if err != nil {
		return nil, err
	}

	commoditiesInfo, err := newCommoditiesInfo(mi)
	if err != nil {
		return nil, err
	}

	company, err := newCompany(mi)
	if err != nil {
		return nil, err
	}

	vehicles, err := newVehicles(mi)
	if err != nil {
		return nil, err
	}

	policyChunkSpec := &ptypes.PolicySpec_ChunkSpec{
		BlanketThirdPartyWithWaiverOfSubrogation:             nil,
		BlanketRegularAdditionalInsured:                      nil,
		BlanketPrimaryAndNonContributoryAdditionalInsured:    nil,
		SpecifiedThirdPartiesWithWOS:                         nil,
		SpecifiedRegularAdditionalInsureds:                   nil,
		SpecifiedPrimaryAndNonContributoryAdditionalInsureds: nil,
		SubCoverages:    scs,
		LimitSpecs:      lss,
		DeductibleSpecs: dss,
		Data: &ptypes.PolicySpec_ChunkSpec_FleetPolicyChunkSpecData{
			FleetPolicyChunkSpecData: &ptypes.Fleet_PolicyChunkSpecData{
				CommoditiesInfo: commoditiesInfo,
				Company:         company,
				Vehicles:        vehicles,
			},
		},
	}

	return &creator_functions.Input{
		Input: common.Input{
			BundleChunkSpec:     bundleChunkSpec,
			PolicyChunkSpec:     policyChunkSpec,
			PolicyOriginalDates: policyOriginalDates,

			// Note that currently in Fleet there is only one call to Pricing, for the
			// entire bundle. It's not one call per policy, because the API hasn't been
			// migrated. Thus, this field is not really being used.
			PolicyNumber: "tentative-policy-number",
		},
		ModelInput: mi,
	}, nil
}

func transformSubCoverages(
	mi *application.ModelInput,
) ([]ptypes.SubCoverageType, []*ptypes.LimitSpec, []*ptypes.DeductibleSpec, error) {
	scs := make([]ptypes.SubCoverageType, 0)
	lss := make([]*ptypes.LimitSpec, 0)
	dss := make([]*ptypes.DeductibleSpec, 0)

	for _, cov := range mi.CoverageInfo.Coverages {
		if cov.CoverageType == enums.CoverageMotorTruckCargo {
			if cov.Limit == nil {
				return nil, nil, nil, errors.Newf("MTC limit is nil")
			}
			if cov.Deductible == nil {
				return nil, nil, nil, errors.Newf("MTC deductible is nil")
			}

			scs = append(scs, ptypes.SubCoverageType_SubCoverageType_Cargo)
			lss = append(lss, &ptypes.LimitSpec{
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Cargo,
					},
				},
				Amount: float64(*cov.Limit),
			})
			dss = append(dss, &ptypes.DeductibleSpec{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Cargo,
					},
				},
				Amount: float64(*cov.Deductible),
			})
		}
	}

	return scs, lss, dss, nil
}

func newCommoditiesInfo(mi *application.ModelInput) (*ptypes.Fleet_CommoditiesInfo, error) {
	records := make([]*ptypes.Fleet_CommodityRecord, 0)

	if mi == nil {
		return nil, errors.New("model input is nil")
	}

	equipmentInfo := mi.EquipmentInfo
	if equipmentInfo == nil {
		return nil, errors.New("equipment info is nil")
	}

	primaryCategory := mi.EquipmentInfo.PrimaryCategory
	if primaryCategory == nil {
		return nil, errors.New("primary commodity category is nil")
	}

	primaryCommodityCategory, err := enums.TransformApplicationCommodityCategoryToProto(*primaryCategory)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to transform primary commodity category %s", *primaryCategory)
	}

	var otherCommoditiesPercentage int64

	commodityDistribution := equipmentInfo.CommodityDistribution
	if commodityDistribution != nil {
		for _, commodityRecord := range commodityDistribution.Commodities {
			commodityCategory, err := enums.TransformApplicationCommodityCategoryToProto(commodityRecord.Category)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"unable to transform commodity category %s",
					commodityRecord.Category,
				)
			}

			var commodityName *model.CommodityName
			commodityType := commodityRecord.Commodity.Type
			if commodityType != nil {
				commodityNameVal, err := enums.TransformApplicationCommodityHauledToProto(*commodityType)
				if err != nil {
					return nil, errors.Wrapf(
						err,
						"unable to transform commodity type %s",
						*commodityType,
					)
				}
				commodityName = pointer_utils.ToPointer(commodityNameVal)
			}

			records = append(records, &ptypes.Fleet_CommodityRecord{
				CommodityCategory:         commodityCategory,
				PercentageOfHauls:         int64(commodityRecord.PercentageOfHauls),
				AverageDollarValuePerHaul: commodityRecord.AvgDollarValueHauled,
				CommodityName:             commodityName,
			})
		}

		additionalCommodities := commodityDistribution.AdditionalCommodities
		if additionalCommodities != nil {
			otherCommoditiesPercentage = int64(additionalCommodities.PercentageOfHauls)
		}
	}

	return &ptypes.Fleet_CommoditiesInfo{
		PrimaryCommodityCategory:   primaryCommodityCategory,
		Records:                    records,
		OtherCommoditiesPercentage: otherCommoditiesPercentage,
	}, nil
}

func newCompany(mi *application.ModelInput) (*ptypes.Fleet_Company, error) {
	if mi == nil {
		return nil, errors.New("model input is nil")
	}

	companyInfo := mi.CompanyInfo
	if companyInfo == nil {
		return nil, errors.New("company info is nil")
	}

	extraPricingInfo := mi.ExtraPricingInfo
	if extraPricingInfo == nil {
		return nil, errors.New("extra pricing info is nil")
	}

	extraCompanyInfo := extraPricingInfo.CompanyExtraPricingInfo
	if extraCompanyInfo == nil {
		return nil, errors.New("extra company info is nil")
	}

	equipmentInfo := mi.EquipmentInfo
	if equipmentInfo == nil {
		return nil, errors.New("equipment info is nil")
	}

	radiusOfOperationRecords, err := transformRadiusOfOperation(companyInfo.RadiusOfOperation)
	if err != nil {
		return nil, err
	}

	if equipmentInfo.PrimaryOperatingClass == nil {
		return nil, errors.New("primary operating class is nil")
	}

	primaryOperationClass, err := enums.TransformApplicationOperatingClassToProto(*equipmentInfo.PrimaryOperatingClass)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"unable to transform primary operating class %s",
			*equipmentInfo.PrimaryOperatingClass,
		)
	}

	taxRecords, err := transformTaxRecords(companyInfo.PremiumTaxRecords)
	if err != nil {
		return nil, err
	}

	operationClassRecords, err := transformOperationClassRecords(equipmentInfo.OperatingClassDistribution)
	if err != nil {
		return nil, err
	}

	terminals := make([]*ptypes.Fleet_Terminal, 0)
	if hasCargoAtTerminalsCoverage(mi) {
		terminals, err = transformTerminals(companyInfo.TerminalLocations)
		if err != nil {
			return nil, err
		}
	}

	return &ptypes.Fleet_Company{
		DotNumber:                         strconv.FormatInt(companyInfo.DOTNumber, 10),
		AverageMiles:                      extraCompanyInfo.AverageMiles,
		AverageCombinedGrossVehicleWeight: extraCompanyInfo.AverageCombinedGrossWeight,
		CrashFrequency:                    extraCompanyInfo.CrashFrequency,
		VehicleInspectionRatio:            extraCompanyInfo.VehicleInspectionRatio,
		MaintenanceViolationsRatio:        extraCompanyInfo.MaintenanceViolationsRatio,
		UnsafeViolationRatio:              extraCompanyInfo.UnsafeViolationRatio,
		UnsafeDrivingScore:                extraCompanyInfo.UnsafeDrivingScore,
		YearsInBusiness:                   extraCompanyInfo.YearsInBusiness,
		InspectionIndicator:               extraCompanyInfo.InspectionIndicator,
		LargeMachineryIndicator:           extraCompanyInfo.LargeMachineryIndicator,
		PowerUnitCount:                    extraCompanyInfo.PowerUnits,
		UsState:                           extraCompanyInfo.USState,
		TotalDriversLastYear:              extraCompanyInfo.TotalDriversLastYear,
		DriversHiredLastYear:              extraCompanyInfo.DriversHiredLastYear,
		ObjectiveGrade:                    extraCompanyInfo.ObjectiveGrade,
		NirvanaYearsRetained:              extraCompanyInfo.NirvanaYearsRetained,
		PriorCarrierYearsRetained:         extraCompanyInfo.PriorCarrierYearsRetained,
		RadiusOfOperationRecords:          radiusOfOperationRecords,
		PrimaryOperationClass:             primaryOperationClass,
		TaxRecords:                        taxRecords,
		OperationClassRecords:             operationClassRecords,
		Terminals:                         terminals,
	}, nil
}

func hasCargoAtTerminalsCoverage(mi *application.ModelInput) bool {
	if mi == nil || mi.CoverageInfo == nil {
		return false
	}
	for _, coverage := range mi.CoverageInfo.Coverages {
		if coverage.CoverageType == enums.CoverageCargoAtScheduledTerminals {
			return true
		}
	}
	return false
}

func transformOperationClassRecords(records []application.OperatingClassDistributionRecord) ([]*ptypes.Fleet_OperationClassRecord, error) {
	retval := make([]*ptypes.Fleet_OperationClassRecord, 0)
	for _, record := range records {
		operationClass, err := enums.TransformApplicationOperatingClassToProto(record.Class)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"unable to transform operating class %s",
				record.Class,
			)
		}
		retval = append(retval, &ptypes.Fleet_OperationClassRecord{
			OperationClass: operationClass,
			Percentage:     record.PercentageOfFleet,
		})
	}
	return retval, nil
}

func transformRadiusOfOperation(records []*application.MileageRadiusRecord) (
	[]*ptypes.Fleet_RadiusOfOperationRecord,
	error,
) {
	retval := make([]*ptypes.Fleet_RadiusOfOperationRecord, 0)
	for _, record := range records {
		radius, err := enums.TransformApplicationMileageRadiusBucketToProto(record.RadiusBucket)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"unable to transform radius bucket %s",
				record.RadiusBucket,
			)
		}
		retval = append(retval, &ptypes.Fleet_RadiusOfOperationRecord{
			RadiusOfOperationRange: radius,
			Percentage:             record.PercentageOfFleet,
		})
	}

	return retval, nil
}

func transformTaxRecords(
	records []application.PremiumTaxRecord,
) ([]*ptypes.Fleet_TaxRecord, error) {
	retval := make([]*ptypes.Fleet_TaxRecord, 0)
	for _, record := range records {
		jurisdictionType := record.JurisdictionType
		jurisdictionName := record.JurisdictionName

		taxCode := record.TaxCode

		for _, lobDetails := range record.LineOfBusinessDetails {
			taxValue := lobDetails.TaxValue

			var policyName ptypes.PolicyName

			covType := lobDetails.CoverageType

			//nolint:exhaustive
			switch covType {
			case enums.CoverageAutoLiability:
				policyName = ptypes.PolicyName_PolicyName_MOTOR_CARRIER
			case enums.CoverageGeneralLiability:
				policyName = ptypes.PolicyName_PolicyName_GENERAL_LIABILITY
			case enums.CoverageMotorTruckCargo:
				policyName = ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO
			default:
				return nil, errors.Newf("unexpected coverage type: %s", covType)
			}

			retval = append(retval, &ptypes.Fleet_TaxRecord{
				JurisdictionType: jurisdictionType,
				JurisdictionName: jurisdictionName,
				TaxCode:          taxCode,
				TaxValue:         taxValue,
				PolicyName:       policyName,
			})
		}
	}

	return retval, nil
}

func newVehicles(mi *application.ModelInput) ([]*ptypes.Fleet_Vehicle, error) {
	if mi == nil {
		return nil, errors.New("model input is nil")
	}

	extraPricingInfo := mi.ExtraPricingInfo
	if extraPricingInfo == nil {
		return nil, errors.New("extra pricing info is nil")
	}

	vehicles := make([]*ptypes.Fleet_Vehicle, 0)
	for _, vehicle := range extraPricingInfo.DecodedVehicles {
		vehicleType, err := iso_utils.TransformApplicationVehicleTypeToProto(vehicle.IsoVehicleType)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"unable to transform vehicle type %s",
				vehicle.IsoVehicleType,
			)
		}
		vehicles = append(vehicles, &ptypes.Fleet_Vehicle{
			Type: vehicleType,
		})
	}

	return vehicles, nil
}

func transformTerminals(records *[]application.TerminalLocation) ([]*ptypes.Fleet_Terminal, error) {
	if records == nil {
		return nil, nil //nolint:nilnil
	}

	terminals := make([]*ptypes.Fleet_Terminal, 0)
	for _, record := range *records {
		if record.CargoTerminalSchedule != nil {
			limitSpec := &ptypes.LimitSpec{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_CargoAtScheduledTerminals,
					},
				},
				Amount:  float64(*record.CargoTerminalSchedule.Limit),
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
			}

			protoEnums, err := record.CargoTerminalSchedule.TransformEnumsToProto()
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"unable to transform CargoTerminalSchedule enums for terminal ID %s",
					record.CargoTerminalSchedule.ID.String(),
				)
			}

			terminal := &ptypes.Fleet_Terminal{
				Id:                           record.CargoTerminalSchedule.ID.String(),
				LimitSpecs:                   []*ptypes.LimitSpec{limitSpec},
				ConstructionClass:            protoEnums.ConstructionClass,
				PublicProtectionClass:        protoEnums.PublicProtectionClass,
				PrivateTheftProtectionSystem: protoEnums.PrivateTheftProtection,
				PrivateFireProtectionSystem:  protoEnums.PrivateFireProtection,
			}

			terminals = append(terminals, terminal)
		}
	}

	return terminals, nil
}
