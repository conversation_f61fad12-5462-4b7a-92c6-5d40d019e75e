load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "pricing_client_wrapper",
    srcs = [
        "client.go",
        "large_loss_proxy.go",
        "safety_discount_helpers.go",
    ],
    importpath = "nirvanatech.com/nirvana/quoting/pricing_client_wrapper",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/vin_utils",
        "//nirvana/common-go/zip_code_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/fleet/model",
        "//nirvana/quoting/app_state_machine/app_logic",
        "//nirvana/quoting/appetite_checker",
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/adaptors/fleet_adaptor",
        "//nirvana/rating/adaptors/fleet_adaptor/common",
        "//nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions",
        "//nirvana/rating/data_fetching/mvr_fetching",
        "//nirvana/rating/data_fetching/rating_tier",
        "//nirvana/rating/data_processing/lni_processing",
        "//nirvana/rating/data_processing/mvr_processing",
        "//nirvana/rating/data_processing/vin_processing",
        "//nirvana/rating/data_processing/vin_processing/iso_utils",
        "//nirvana/rating/pricing/api/client_helpers/fleet",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/rtypes",
        "//nirvana/rating/utils",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
