import { useImpler } from '@impler/react';
import { CircularProgress } from '@material-ui/core';
import { lightTheme } from '@nirvana/ui-kit';
import { useEffect, useRef } from 'react';
import * as Sentry from '@sentry/react';
import Button from '../file-upload';

const INIT_FAILURE_TIMEOUT = 30 * 1000; // 30 seconds

type FileUploadMetadata = {
  fileId: string;
  filename: string;
};

export interface ImplerUploaderProps {
  templateId: string;
  extra?: Record<string, any>;
  title: string;
  useFullNames?: boolean;
  onChange: (
    result: Record<string, any>[],
    metadata?: FileUploadMetadata,
  ) => void;
  onMetaChange?: (metadata?: FileUploadMetadata) => void;
  isDisabled?: boolean;
  schema: any;
  onImplerStateChange?: (state: boolean) => void;
  onImplerWidgetOpen?: () => void;
  onImplerWidgetClose?: () => void;
  onImplerUploadSubmit?: () => void;
}

const Impler = ({
  templateId,
  extra,
  title,
  schema,
  onChange,
  onMetaChange,
  isDisabled,
  onImplerStateChange,
  onImplerWidgetOpen,
  onImplerWidgetClose,
  onImplerUploadSubmit,
}: ImplerUploaderProps) => {
  const fileMetadataRef = useRef<FileUploadMetadata | undefined>(undefined);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const { closeWidget, showWidget, isImplerInitiated } = useImpler({
    templateId,
    projectId: import.meta.env.VITE_IMPLER_PROJECT_ID,
    accessToken: import.meta.env.VITE_IMPLER_ACCESS_TOKEN,
    primaryColor: lightTheme.palette.primary.main,
    extra,
    title,
    texts: {
      COMMON: {
        UPLOAD_AGAIN: 'Restart',
      },
      FILE_DROP_AREA: {
        DROP_FILE: 'Drag and drop your file here',
        FILE_SELECTED: 'File uploaded',
      },
      PHASE1: {
        SELECT_SHEET_NAME: 'Select a sheet',
        SELECT_SHEET_NAME_PLACEHOLDER: 'Select sheet',
        SEE_MAPPING: 'Proceed',
      },
      SELECT_HEADER: {
        INFO: 'Select Header Row from the table. Rows above the header will not be imported. Click on the row to change selection.',
      },
      'PHASE1-2': {
        RECOMMENDED_LIMIT: 'Recommended up to {records} records',
      },
      PHASE2: {
        REVIEW_DATA: 'Proceed',
        IN_SCHEMA_TITLE: 'Required fields',
        IN_SHEET_TITLE: 'Select a field from the sheet',
      },
      PHASE3: {
        RE_REVIEW_DATA: 'Proceed',
        COMPLETE: 'Complete',
        ALL_RECORDS_VALID_TITLE: 'Validation successful!',
        ALL_RECORDS_VALID_DETAILS:
          'Importing {total} rows. Are you sure you want to proceed?',
      },
    },
    config: {
      hideCheckBox: true,
      hideFindAndReplaceButton: true,
      hideSrNo: true,
      hideDeleteButton: true,
    },
    onUploadComplete: (data) => {
      const metadata = {
        fileId: data._id,
        filename: data.originalFileName,
      };
      fileMetadataRef.current = metadata;

      if (onMetaChange) {
        onMetaChange(metadata);
      }
    },
    onDataImported: (data) => {
      if (onImplerUploadSubmit) {
        onImplerUploadSubmit();
      }

      onChange(data, fileMetadataRef.current);
      closeWidget();
    },
    onWidgetClose: () => {
      if (onImplerWidgetClose) {
        onImplerWidgetClose();
      }
    },
  });

  useEffect(() => {
    // Start a timeout when the component mounts
    timeoutRef.current = setTimeout(() => {
      if (!isImplerInitiated) {
        Sentry.captureException(
          new Error(
            'Impler initialization failed: isImplerInitiated not set to true within 30 seconds.',
          ),
        );
      }
    }, INIT_FAILURE_TIMEOUT);

    return () => {
      // Clear the timeout when the component unmounts or when the effect re-runs
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isImplerInitiated]);

  useEffect(() => {
    if (onImplerStateChange) {
      onImplerStateChange(isImplerInitiated);
    }
  }, [isImplerInitiated, onImplerStateChange]);

  return (
    <div>
      <Button
        variant="contained"
        type="button"
        disabled={!isImplerInitiated || isDisabled}
        onClick={() => {
          if (onImplerWidgetOpen) {
            onImplerWidgetOpen();
          }

          showWidget({
            schema,
            colorScheme: 'light',
          });
        }}
        startIcon={
          !isImplerInitiated && (
            <CircularProgress className="text-white" size={18} />
          )
        }
      >
        {isImplerInitiated ? 'Proceed' : 'Initializing...'}
      </Button>
    </div>
  );
};

export default Impler;
