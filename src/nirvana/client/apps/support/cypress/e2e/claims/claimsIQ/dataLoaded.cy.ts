import { RoutesHelper } from '@nirvana/core/testUtils';
import { claimById, claims } from '../../../support/handlers/claims';
import {
  determinationCachedHandler,
  determinationHandler,
  forcedFailureHandler,
  resetDeterminationHandlerState,
  upsertCoverageFeedbackHandler,
} from '../../../support/handlers/determinationHandler';

const routesHelper = new RoutesHelper();
const baseUrl = routesHelper.getBaseUrl();
const clickClaim = (id?: string) => {
  cy.wait('@claims');
  cy.get('table>tbody>tr[data-testid="skeleton-row"]').should('have.length', 0);

  if (id) {
    cy.get(`table>tbody>tr [data-testid="claim-${id}"]`).click();
  } else {
    cy.get('table>tbody>tr').first().click();
  }
};

Cypress.automation('remote:debugger:protocol', {
  command: 'Browser.grantPermissions',
  params: {
    permissions: ['clipboardReadWrite', 'clipboardSanitizedWrite'],
    origin: window.location.origin,
  },
});

describe('ClaimsIQ - Data Loaded', () => {
  beforeEach(() => {
    resetDeterminationHandlerState();
    cy.login();
    routesHelper.overrideGraphqlResponse('claims', claims).as('claims');
    routesHelper
      .overrideGraphqlResponse('ClaimById', claimById)
      .as('claimById');

    routesHelper
      .overrideClaimsLLMResponse(
        '/**/coverage/determination/cached',
        determinationCachedHandler,
      )
      .as('determinationCached');

    routesHelper
      .overrideClaimsLLMResponse(
        '/**/coverage/feedback',
        upsertCoverageFeedbackHandler,
        'POST',
      )
      .as('upsertCoverageFeedback');

    cy.visit(`${baseUrl}/claims`);

    clickClaim();
    cy.get('[data-testid="run-claim-iq-button"]').click();
    cy.wait('@determinationCached');
    cy.get('[data-testid="determination-container"]').should('be.visible');
  });

  it('Keeps showing the data when the user goes to another tab', () => {
    cy.get('[data-testid="policy"]').click();
    cy.get('[data-testid="claimiq"]').click();
    cy.get('[data-testid="determination-container"]').should('be.visible');
  });

  it('allows to refresh the data', () => {
    routesHelper
      .overrideClaimsLLMResponse(
        '/**/coverage/determination',
        determinationHandler,
      )
      .as('determination');
    cy.get('[data-testid="refresh-button"]').click();
    cy.wait('@determination');
    cy.get('[data-testid="claimsiq-loading-container"]').should('be.visible');
  });

  it('shows an error but keeps showing the old data when the request fails', () => {
    routesHelper
      .overrideClaimsLLMResponse(
        '/**/coverage/determination',
        forcedFailureHandler,
      )
      .as('determination');
    cy.get('[data-testid="refresh-button"]').click();

    cy.wait('@determination');
    cy.get(':contains("Error updating coverage notes")').should('be.visible');
    cy.get('[data-testid="determination-container"]').should('be.visible');
  });

  it('allows copying coverage notes to clipboard', () => {
    cy.get('[data-testid="copy-button"]').click();

    cy.window()
      .its('navigator.clipboard')
      .then((clip) => clip.readText())
      .should(
        'eq',
        '### Coverage\nCoverage is 50%\n\n\n### Coverage 2\nCoverage is 100%\n\n',
      );
  });

  describe('Accepting a note', () => {
    it('Allows to Accept a note', () => {
      cy.get('[data-testid="accept-note-button"]').first().click();
      cy.wait('@upsertCoverageFeedback');
      cy.wait('@determinationCached');
      cy.get('[data-cy-status="Accept"]').should('be.visible');
    });

    it('Disables button during API call', () => {
      cy.get('[data-testid="accept-note-button"]').first().click();
      cy.get('[data-testid="accept-note-button"]')
        .first()
        .should('be.disabled');
      cy.wait('@upsertCoverageFeedback');
      cy.get('[data-testid="accept-note-button"]')
        .first()
        .should('not.be.disabled');
    });

    it('Allows to unAccept a note', () => {
      cy.get('[data-testid="accept-note-button"]').first().click();
      cy.wait('@upsertCoverageFeedback');
      cy.get('[data-cy-status="Accept"]').should('be.visible');
      cy.get('[data-testid="accept-note-button"]').first().click();
      cy.wait('@upsertCoverageFeedback');
      cy.get('[data-cy-status="Accept"]').should('not.exist');
    });
  });

  describe('Archiving a note', () => {
    it('Allows to archive a note', () => {
      // Find the archive button using the new data-testid (first one)
      cy.get('[data-testid="archive-note-button"]').first().click();
      cy.wait('@upsertCoverageFeedback');
      cy.wait('@determinationCached');

      // Verify the archived note is visible in the Archived Notes section
      cy.get('[data-testid="archived-notes-title"]').should('be.visible');
      // Click on the Archived Notes accordion to open it
      cy.get('[data-testid="archived-notes-title"]').click();
      // The archived note should be visible in the Archived Notes section
      cy.get('[data-testid="archived-notes-container"]').should(
        'contain',
        'Coverage is 50%',
      );
    });

    it('Allows to unarchive a note', () => {
      // First archive the note
      cy.get('[data-testid="archive-note-button"]').first().click();
      cy.wait('@upsertCoverageFeedback');
      cy.wait('@determinationCached');

      // Verify the note appears in Archived Notes section
      cy.get('[data-testid="archived-notes-title"]').should('be.visible');
      cy.get('[data-testid="archived-notes-title"]').click();

      // Then unarchive it by clicking the archive button again
      cy.get('[data-testid="archive-note-button"][data-cy-archived="true"]')
        .first()
        .click();
      cy.wait('@upsertCoverageFeedback');
      cy.wait('@determinationCached');

      // Verify that there are no archived notes
      cy.get('[data-testid="archived-notes-title"]').should('not.exist');
    });

    it('Disables archive button during API call', () => {
      cy.get('[data-testid="archive-note-button"]').first().click();
      cy.get('[data-testid="archive-note-button"]')
        .first()
        .should('be.disabled');
    });
  });
});
