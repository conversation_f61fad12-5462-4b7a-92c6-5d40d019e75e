import { Citation, CoverageNote } from '@nirvana/api/claims_agent';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Show,
} from '@nirvana/ui';
import { useClaimsContext } from '../../hooks/useClaims';
import { DeterminationItem } from './DeterminationItem';
import { Copy } from './Copy';
import { isArchived } from './utils/isArchived';

type DeterminationProps = {
  verificationData: CoverageNote[] | undefined;
  externalClaimId: string;
};

const mapDocumentToCitation = (coverage_notes: CoverageNote[]) => {
  let lastIndex = 1;
  return coverage_notes.reduce(
    (acc, coverage_note) => {
      const { original_content, note_id } = coverage_note;
      const { citation } = original_content;
      const { document } = citation || {};
      const key = document?.document_id || note_id;
      if (!acc[key]) {
        acc[key] = lastIndex;
        lastIndex++;
      }
      return acc;
    },
    {} as Record<string, number>,
  );
};

export default function Determination({
  externalClaimId,
  verificationData,
}: DeterminationProps) {
  const { setIsDocumentViewerOpen, setIsClaimListOpen, setDocumentData } =
    useClaimsContext();

  function openDocumentViewer(citation: Citation) {
    setIsDocumentViewerOpen(true);
    setIsClaimListOpen(false);

    setDocumentData({
      fileName: citation.filename,
      documentSummary: citation.excerpt,
      pageNumber: citation.pages?.[0] || 1,
      documentID: citation.document?.document_id,
    });
  }
  const documentToCitation = mapDocumentToCitation(verificationData || []);

  const archivedNotes = verificationData?.filter((item) => isArchived(item));

  return (
    <div className="overflow-y-auto mt-4" data-testid="determination-container">
      <div className="flex justify-between text-tw-primary font-semibold py-4">
        <h2 className="flex items-center">
          Coverage Notes <Copy verificationData={verificationData || []} />
        </h2>
        <span>Actions</span>
      </div>
      <div className="space-y-6">
        {verificationData
          ?.filter((item) => !isArchived(item))
          .map((item) => (
            <DeterminationItem
              key={item.note_id}
              item={item}
              externalClaimId={externalClaimId}
              openDocumentViewer={openDocumentViewer}
              documentToCitation={documentToCitation}
            />
          ))}
      </div>
      <div className="border-t border-tw-border-secondary my-4" />
      <Show when={(archivedNotes || []).length > 0}>
        <Accordion type="single" collapsible>
          <AccordionItem value="item-1">
            <AccordionTrigger className="text-tw-gray-600">
              <h2 data-testid="archived-notes-title">Archived Notes</h2>
            </AccordionTrigger>
            <AccordionContent>
              <div
                className="space-y-6 text-text-disabled"
                data-testid="archived-notes-container"
              >
                {archivedNotes?.map((item) => (
                  <DeterminationItem
                    key={item.note_id}
                    item={item}
                    externalClaimId={externalClaimId}
                    openDocumentViewer={openDocumentViewer}
                    documentToCitation={documentToCitation}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </Show>
    </div>
  );
}
