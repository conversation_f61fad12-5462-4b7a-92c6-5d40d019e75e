import {
  CoverageNote,
  NoteFeedback,
  VerificationItemStatusEnum,
} from '@nirvana/api/claims_agent';
import { Button, Show, Tooltip } from '@nirvana/ui';
import { queryClient } from 'Layout';
import {
  fetchVerificationData,
  upsertCoverageFeedback,
} from 'pages/claims/queries';
import React, { useRef } from 'react';
import { AiOutlineLoading } from 'react-icons/ai';
import {
  HiOutlineCheckCircle,
  HiOutlinePencil,
  HiCheckCircle,
} from 'react-icons/hi';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useClaimsContext } from 'pages/claims/hooks/useClaims';
import { LuArchive, LuArchiveRestore } from 'react-icons/lu';
import { isArchived } from './utils/isArchived';

enum CurrentProcessing {
  Accept = 'accept',
  Reject = 'reject',
  Undefined = 'undefined',
}

const getUpsertCoverageFeedbackPayload = (
  noteId: string,
  status: VerificationItemStatusEnum,
  name: string,
  summary: string,
): NoteFeedback => {
  return {
    note_id: noteId,
    modified_content: {
      status,
      name,
      summary,
    },
  };
};

const Icon = ({
  isLoading,
  EmptyIcon,
  FilledIcon,
  isFilled,
}: {
  isLoading: boolean;
  EmptyIcon: React.ElementType;
  FilledIcon: React.ReactNode;
  isFilled: boolean;
}) => {
  if (isLoading) {
    return <AiOutlineLoading className="animate-spin" />;
  }

  return isFilled ? FilledIcon : <EmptyIcon />;
};

export const Actions = ({
  noteId,
  externalClaimId,
}: {
  noteId: string;
  externalClaimId: string;
}) => {
  const { isUpdatingNote } = useClaimsContext();
  const action = useRef<CurrentProcessing>(CurrentProcessing.Undefined);
  const { mutate: updateNote, isLoading } = useMutation({
    mutationFn: upsertCoverageFeedback,
    onSuccess: () => {
      isUpdatingNote.current = noteId;

      queryClient.invalidateQueries(
        ['verificationData', externalClaimId],
        undefined,
        {},
      );
    },
  });

  const { isRefetching, data } = useQuery(
    ['verificationData', externalClaimId],
    () => fetchVerificationData(externalClaimId, false),
    {
      enabled: false,
      onSuccess: () => {
        isUpdatingNote.current = false;
      },
    },
  );
  const { original_content, modified_content } =
    data?.coverage_notes?.find((note) => note.note_id === noteId) || {};
  const { name, summary, status } = modified_content ||
    original_content || {
      name: '',
      summary: '',
      status: VerificationItemStatusEnum.Undefined,
    };

  const toggleAcceptNote = () => {
    const newStatus =
      status === VerificationItemStatusEnum.Accept
        ? VerificationItemStatusEnum.Undefined
        : VerificationItemStatusEnum.Accept;

    action.current = CurrentProcessing.Accept;
    updateNote(
      getUpsertCoverageFeedbackPayload(noteId, newStatus, name, summary),
    );
  };

  const toggleDiscardNote = () => {
    const newStatus =
      status === VerificationItemStatusEnum.Reject
        ? VerificationItemStatusEnum.Undefined
        : VerificationItemStatusEnum.Reject;
    action.current = CurrentProcessing.Reject;
    updateNote(
      getUpsertCoverageFeedbackPayload(noteId, newStatus, name, summary),
    );
  };

  const isProcessing =
    isLoading || (isRefetching && isUpdatingNote.current === noteId);

  const isNoteArchived = isArchived(
    data?.coverage_notes?.find((note) => note.note_id === noteId) ||
      ({} as CoverageNote),
  );

  return (
    <div className="ml-8">
      <div className="flex items-center gap-2">
        <Show when={!isNoteArchived}>
          <Tooltip>
            <Tooltip.Trigger asChild>
              <Button
                variant="text"
                size="icon"
                data-testid="accept-note-button"
                data-cy-status={status}
                onClick={toggleAcceptNote}
                disabled={isProcessing}
                startIcon={
                  <Icon
                    isLoading={
                      isProcessing &&
                      action.current === CurrentProcessing.Accept
                    }
                    EmptyIcon={HiOutlineCheckCircle}
                    FilledIcon={<HiCheckCircle className="text-green-600" />}
                    isFilled={status === VerificationItemStatusEnum.Accept}
                  />
                }
              />
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>
                {status === VerificationItemStatusEnum.Accept
                  ? 'Undo Accept'
                  : 'Accept'}
              </p>
            </Tooltip.Content>
          </Tooltip>

          <Tooltip>
            <Tooltip.Trigger asChild>
              <div className="flex items-center cursor-not-allowed">
                <div className="border-r border-tw-border-secondary h-4" />
                <Button
                  disabled
                  variant="text"
                  size="icon"
                  startIcon={<HiOutlinePencil />}
                />
                <div className="border-r border-tw-border-secondary h-4" />
              </div>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>Coming soon</p>
            </Tooltip.Content>
          </Tooltip>
        </Show>
        <Tooltip>
          <Tooltip.Trigger asChild>
            <Button
              disabled={isProcessing}
              variant="text"
              size="icon"
              data-testid="archive-note-button"
              data-cy-archived={isNoteArchived.toString()}
              onClick={toggleDiscardNote}
              startIcon={
                <Icon
                  isLoading={
                    isProcessing && action.current === CurrentProcessing.Reject
                  }
                  EmptyIcon={LuArchive}
                  FilledIcon={<LuArchiveRestore className="text-text-hint" />}
                  isFilled={status === VerificationItemStatusEnum.Reject}
                />
              }
            />
          </Tooltip.Trigger>
          <Tooltip.Content>
            {isNoteArchived ? 'Unarchive' : 'Archive'}
          </Tooltip.Content>
        </Tooltip>
      </div>
    </div>
  );
};
