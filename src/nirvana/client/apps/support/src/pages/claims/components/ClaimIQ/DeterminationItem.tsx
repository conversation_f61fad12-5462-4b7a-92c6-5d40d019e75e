import { HiExclamation, HiXCircle } from 'react-icons/hi';

import { HoverCard, Show, Tag, Tooltip } from '@nirvana/ui';
import { Citation, CoverageNote } from '@nirvana/api/claims_agent';
import clsx from 'clsx';
import { Actions } from './Actions';
import { isArchived } from './utils/isArchived';

const Icon = ({
  assessment_score,
}: {
  assessment_score: number | null | undefined;
}) => {
  const PARTIAL_SCORE = 0.5;
  const FAILED_SCORE = 0.0;

  switch (assessment_score) {
    case PARTIAL_SCORE:
    case null:
      return (
        <Tooltip>
          <Tooltip.Trigger asChild>
            <span className="cursor-help">
              <HiExclamation className="mt-0.5 text-lg text-warning-main shrink-0" />
            </span>
          </Tooltip.Trigger>
          <Tooltip.Content>
            <p>Needs Review</p>
          </Tooltip.Content>
        </Tooltip>
      );
    case FAILED_SCORE:
      return (
        <Tooltip>
          <Tooltip.Trigger asChild>
            <span className="cursor-help">
              <HiXCircle className="mt-0.5 text-lg text-error-main shrink-0" />
            </span>
          </Tooltip.Trigger>
          <Tooltip.Content>
            <p>Coverage Issue</p>
          </Tooltip.Content>
        </Tooltip>
      );
    default:
      return null;
  }
};

export const DeterminationItem = ({
  openDocumentViewer,
  documentToCitation,
  externalClaimId,
  item,
}: {
  item: CoverageNote;
  externalClaimId: string;
  openDocumentViewer: (citation: Citation) => void;
  documentToCitation: Record<string, number>;
}) => {
  const { original_content, note_id } = item;
  return (
    <div
      className={clsx('flex mt-1 border-t border-tw-border-secondary pt-4', {
        'text-tw-gray-600': isArchived(item),
      })}
    >
      <div className="flex-1 text-sm">
        <h3 className="font-semibold flex items-center">
          <span className="mr-2">{original_content.name}</span>
          <Icon assessment_score={original_content.assessment_score} />
        </h3>
        <p className="mt-1 font-medium">
          {original_content.summary}{' '}
          <Show when={original_content.citation}>
            {(citation) => (
              <HoverCard openDelay={100}>
                <HoverCard.Trigger>
                  <button
                    type="button"
                    className={clsx({
                      'cursor-default': !citation.document,
                    })}
                    onClick={
                      citation.document
                        ? () => openDocumentViewer(citation)
                        : undefined
                    }
                  >
                    <Tag
                      color={citation.document ? 'teal' : 'gray'}
                      className="text-xs font-semibold"
                    >
                      {
                        documentToCitation[
                          citation?.document?.document_id || note_id
                        ]
                      }
                    </Tag>
                  </button>
                </HoverCard.Trigger>
                <HoverCard.Content>
                  <div className="max-w-xs">
                    <div className="mb-2 text-xs font-medium">
                      {citation.filename}
                    </div>
                    <div className="mt-1 text-xs text-gray-600">
                      "{citation.excerpt}"
                    </div>
                  </div>
                </HoverCard.Content>
              </HoverCard>
            )}
          </Show>
        </p>
      </div>
      <Actions externalClaimId={externalClaimId} noteId={note_id} />
    </div>
  );
};
