import { formatDateAgo } from '@nirvana/core/utils';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
} from '@nirvana/ui';
import {
  differenceInSeconds,
  formatDuration,
  intervalToDuration,
} from 'date-fns';
import { HiOutlineSparkles } from 'react-icons/hi';
import { useEffect, useState } from 'react';
import { steps } from './constants';
import { TimelineItem } from './TimeLineItem';
import CheckStill from './assets/CheckStill.svg?react';

const REFRESH_INTERVAL = 15000;

type HeaderProps = {
  refetch: () => void;
  lastRefreshedAt?: string;
  startTime?: Date;
  finishTime?: Date;
};

const THRESHOLD_FOR_DURATION = 8;

export default function Header({
  refetch,
  lastRefreshedAt,
  startTime,
  finishTime,
}: HeaderProps) {
  const differenceAsSeconds =
    finishTime && startTime
      ? Math.abs(differenceInSeconds(finishTime, startTime))
      : 0;

  const duration =
    startTime && finishTime
      ? formatDuration(
          intervalToDuration({
            start: startTime,
            end: finishTime,
          }),
        )
      : '-';

  const [lastRefreshedAtText, setLastRefreshedAtText] = useState(
    lastRefreshedAt ? formatDateAgo(lastRefreshedAt) : '-',
  );

  useEffect(() => {
    const interval = setInterval(() => {
      if (lastRefreshedAt) {
        setLastRefreshedAtText(formatDateAgo(lastRefreshedAt));
      }
    }, REFRESH_INTERVAL);
    return () => clearInterval(interval);
  }, [lastRefreshedAt]);

  return (
    <header>
      <section className="flex items-center gap-2 mb-6">
        <HiOutlineSparkles className="text-xl text-teal-600" />
        <p className="text-xs text-text-hint">
          Updated {lastRefreshedAtText}.
          <Button
            onClick={refetch}
            data-testid="refresh-button"
            variant="link"
            className="ml-2"
          >
            Refresh now
          </Button>
          <span> (takes ~90 secs)</span>
        </p>
      </section>
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger>
            <span className="text-tw-secondary">
              Thought for{' '}
              {differenceAsSeconds > THRESHOLD_FOR_DURATION
                ? duration
                : 'a few seconds'}
            </span>
          </AccordionTrigger>
          <AccordionContent>
            <div className="px-4">
              <ol>
                {steps.slice(0, -1).map((step) => (
                  <TimelineItem key={step.title} summary={step.description} />
                ))}
              </ol>
              <div className="flex items-center gap-2 text-tw-gray-700 -ml-[18px]">
                <CheckStill />
                <p>Done</p>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </header>
  );
}
