import {
  Checkbox,
  Form,
  Input,
  Select,
  RadioGroup,
  Label,
  InputNumber,
} from '@nirvana/ui';
import React from 'react';
import { useFormContext } from 'react-hook-form';

const DEFAULT_COLUMNS = 2;

export interface FormFieldOption {
  name?: string; // Name of the option, used for identification
  value: string | number; // Value of the option
  label: string; // Display label for the option
}

export interface FormField {
  id: string; // Unique identifier for the field
  name: string;
  label: string;
  type: string;
  placeholder?: string; // Placeholder text for input fields
  options?: FormFieldOption[];
  fullWidth?: boolean; // If true, span across both columns
  hidden?: boolean; // If true, don't render this field
  defaultValue?: string | number; // Default value for the field
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  disabled?: boolean | ((formValues: Record<string, any>) => boolean); // If true, field is disabled
  decimalScale?: number; // For number inputs, controls decimal places
  thousandSeparator?: boolean; // For number inputs, controls decimal places and thousand separator
  startAdornment?: React.ReactNode; // Start adornment for input fields
  endAdornment?: React.ReactNode; // End adornment for input fields
  allowNegative?: boolean; // Allow negative numbers in numeric input
  // Function to transform the value before rendering
  valueTransform?: (value: string | number | boolean) => string | boolean;
  // Function to transform the value
  onChangeTransform?: (value: string | boolean | undefined) => any; // eslint-disable-line @typescript-eslint/no-explicit-any
}

interface FormBuilderProps {
  fields: FormField[];
  columns?: number;
}

const FormBuilder = ({
  fields = [],
  columns = DEFAULT_COLUMNS,
}: FormBuilderProps) => {
  const form = useFormContext();

  // Filter out hidden fields
  const visibleFields = fields.filter((field) => !field.hidden);

  // Group fields into rows based on columns prop
  const rows = [];
  for (let i = 0; i < visibleFields.length; i += columns) {
    rows.push(visibleFields.slice(i, i + columns));
  }

  const renderField = (record: FormField) => {
    // Depending on field type, render appropriate input component
    switch (record.type) {
      case 'text':
      case 'email':
      case 'date': // TODO: Create a separate DatePicker component
        return (
          <Form.Field
            control={form.control}
            name={record.name}
            render={({ field }) => (
              <Form.Item>
                <Form.Label>{record.label}</Form.Label>
                <Form.Control>
                  <Input
                    {...field}
                    type={record.type}
                    placeholder={record.placeholder}
                    className="w-full"
                    onChange={(e) => {
                      field.onChange(e.target.value);
                    }}
                  />
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />
        );
      case 'number':
        return (
          <Form.Field
            control={form.control}
            name={record.name}
            render={({ field }) => (
              <Form.Item>
                <Form.Label>{record.label}</Form.Label>
                <Form.Control>
                  <InputNumber
                    placeholder={record.placeholder}
                    className="w-full"
                    decimalScale={record.decimalScale || 0}
                    thousandSeparator={record.thousandSeparator || false}
                    prefix={record.startAdornment}
                    min={record.allowNegative ? undefined : 0}
                    onValueChange={(v: string) => {
                      return field.onChange(v ? +v : undefined);
                    }}
                    value={field.value}
                  />
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />
        );
      case 'dropdown':
        return (
          <Form.Field
            control={form.control}
            name={record.name}
            render={({ field }) => (
              <Form.Item>
                <Form.Label>{record.label}</Form.Label>
                <Form.Control>
                  <Select
                    onValueChange={(v) => {
                      return field.onChange(
                        record.onChangeTransform
                          ? record.onChangeTransform(v)
                          : v,
                      );
                    }}
                    disabled={
                      typeof record.disabled === 'function'
                        ? record.disabled(form.getValues())
                        : record.disabled
                    }
                    value={
                      record.valueTransform
                        ? record.valueTransform(field.value)
                        : field.value
                    }
                    allowClear
                  >
                    <Select.Trigger className="w-full">
                      <Select.Value
                        placeholder={
                          record.placeholder ?? `Select ${record.label}`
                        }
                      />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Group>
                        <Select.Label>Select {record.label}</Select.Label>
                        {record.options?.map((option) => (
                          <Select.Item
                            key={option.value}
                            value={`${option.value}`}
                          >
                            {option.label}
                          </Select.Item>
                        ))}
                      </Select.Group>
                    </Select.Content>
                  </Select>
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />
        );
      case 'checkbox':
        return (
          <Form.Field
            control={form.control}
            name={record.name}
            render={({ field }) => (
              <Form.Item>
                <Form.Label>&nbsp;</Form.Label>
                <div className="flex items-center space-x-2 space-y-0 h-9">
                  <Form.Control>
                    <Checkbox
                      onCheckedChange={(v) => {
                        return field.onChange(
                          record.onChangeTransform
                            ? record.onChangeTransform(v)
                            : v,
                        );
                      }}
                      checked={
                        record.valueTransform
                          ? record.valueTransform(field.value)
                          : field.value
                      }
                    />
                  </Form.Control>
                  <div className="leading-none">
                    <Form.Label>{record.label}</Form.Label>
                  </div>
                </div>
              </Form.Item>
            )}
          />
        );
      case 'checkbox-group':
        return (
          <Form.Item>
            <Form.Label>{record.label}</Form.Label>
            <div className="flex flex-col gap-2">
              {record.options?.map((option) => (
                <Form.Field
                  key={option.name}
                  control={form.control}
                  name={option.name || `${record.name}.${option.value}`}
                  render={({ field }) => (
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={!!field.value}
                        onCheckedChange={field.onChange}
                        id={`${record.name}-${option.value}`}
                      />
                      <Label htmlFor={`${record.name}-${option.value}`}>
                        {option.label}
                      </Label>
                    </div>
                  )}
                />
              ))}
            </div>
            <Form.Message />
          </Form.Item>
        );
      case 'radio-group':
        return (
          <Form.Field
            control={form.control}
            name={record.name}
            render={({ field }) => (
              <Form.Item>
                <Form.Label>{record.label}</Form.Label>
                <Form.Control>
                  <RadioGroup
                    value={String(Number(field.value))}
                    onValueChange={(v) => field.onChange(Boolean(Number(v)))}
                    className="flex gap-4"
                  >
                    {record.options?.map((option) => (
                      <div
                        key={option.value}
                        className="flex items-center space-x-2"
                      >
                        <RadioGroup.Item
                          value={`${option.value}`}
                          id={`${record.name}-${option.value}`}
                        />
                        <Label htmlFor={`${record.name}-${option.value}`}>
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </Form.Control>
                <Form.Message />
              </Form.Item>
            )}
          />
        );
      case 'placeholder':
        return null;
      case 'hidden':
        return (
          <Form.Field
            control={form.control}
            name={record.name}
            render={({ field }) => (
              <input
                type="hidden"
                {...field}
                value={record.defaultValue ?? field.value ?? ''}
              />
            )}
          />
        );
      // Add more field types as needed
      default:
        return <span>Unsupported field type: {record.type}</span>;
    }
  };

  return (
    <div className="flex-grow space-y-4">
      {rows.map((row, rowIndex) => (
        <div
          key={rowIndex}
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))` }}
        >
          {row.map((field) => (
            <div key={field.id} className="col-span-1 max-w-[450px]">
              {renderField(field)}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default FormBuilder;
