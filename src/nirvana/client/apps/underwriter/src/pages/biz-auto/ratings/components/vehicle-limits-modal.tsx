import { formatNumber } from '@nirvana/core/utils';
import { Button, Dialog, Form, Select } from '@nirvana/ui';
import { useFormContext } from 'react-hook-form';

interface VehicleLimitsModalProps {
  open: boolean;
  onClose: () => void;
  index: number;
  vehiclesInfo: string[];
  limitOptions?: number[];
  deductibleOptions?: number[];
  hasVehicleLevelLimit?: boolean;
  hasVehicleLevelDeductible?: boolean;
}

const VehicleLimitsModal = ({
  open,
  onClose,
  index,
  vehiclesInfo,
  limitOptions,
  deductibleOptions,
  hasVehicleLevelLimit,
  hasVehicleLevelDeductible,
}: VehicleLimitsModalProps) => {
  const { control } = useFormContext();

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <Dialog.Content className="max-w-2xl">
        <Dialog.Header>
          <Dialog.Title>Vehicle Level Limits and Deductibles</Dialog.Title>
          <Dialog.Description>
            Set limits and deductibles for individual vehicles.
          </Dialog.Description>
        </Dialog.Header>

        <div className="mt-4 overflow-y-auto max-h-[400px] pr-3">
          <div className="grid grid-cols-[2fr_1fr_1fr] gap-4 mb-2">
            <div className="text-sm font-bold">Vehicle (VIN)</div>
            {hasVehicleLevelLimit && limitOptions && (
              <div className="text-sm font-bold">Limit</div>
            )}
            {hasVehicleLevelDeductible && deductibleOptions && (
              <div className="text-sm font-bold">Deductible</div>
            )}
          </div>

          {vehiclesInfo.map((vin, vIndex) => (
            <div key={vin} className="grid grid-cols-[2fr_1fr_1fr] gap-4 mb-4">
              <div className="text-sm">{vin}</div>

              {hasVehicleLevelLimit && limitOptions && (
                <Form.Field
                  control={control}
                  name={`ancillaryCoverages.${index}.vehicleLimits.${vIndex}`}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Control>
                        <Select
                          onValueChange={(v) =>
                            field.onChange({
                              vin,
                              selectedLimit: v ? +v : undefined,
                            })
                          }
                          value={field.value?.selectedLimit?.toString()}
                          allowClear
                        >
                          <Select.Trigger className="w-full">
                            <Select.Value placeholder="Select limit" />
                          </Select.Trigger>
                          <Select.Content>
                            <Select.Group>
                              <Select.Label>Select Limit</Select.Label>
                              {limitOptions?.map((option) => (
                                <Select.Item
                                  key={`${vin}-limit-${option}`}
                                  value={option.toString()}
                                >
                                  ${formatNumber(option)}
                                </Select.Item>
                              ))}
                            </Select.Group>
                          </Select.Content>
                        </Select>
                      </Form.Control>
                      <Form.Message />
                    </Form.Item>
                  )}
                />
              )}

              {hasVehicleLevelDeductible && deductibleOptions && (
                <Form.Field
                  control={control}
                  name={`ancillaryCoverages.${index}.vehicleDeductibles.${vIndex}`}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Control>
                        <Select
                          onValueChange={(v) =>
                            field.onChange({
                              vin,
                              selectedDeductible: v ? +v : undefined,
                            })
                          }
                          value={field.value?.selectedDeductible?.toString()}
                          allowClear
                        >
                          <Select.Trigger className="w-full">
                            <Select.Value placeholder="Select deductible" />
                          </Select.Trigger>
                          <Select.Content>
                            <Select.Group>
                              <Select.Label>Select Deductible</Select.Label>
                              {deductibleOptions?.map((option) => (
                                <Select.Item
                                  key={`${vin}-deductible-${option}`}
                                  value={option.toString()}
                                >
                                  ${formatNumber(option)}
                                </Select.Item>
                              ))}
                            </Select.Group>
                          </Select.Content>
                        </Select>
                      </Form.Control>
                      <Form.Message />
                    </Form.Item>
                  )}
                />
              )}
            </div>
          ))}
        </div>

        <Dialog.Footer>
          <Dialog.Close asChild>
            <Button variant="primary" type="button">
              Save
            </Button>
          </Dialog.Close>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};

export default VehicleLimitsModal;
