import {
  AncillaryCoverage,
  CoverageType,
  GetCoverageOptionsRequestBody,
} from '@nirvana/api/bizAuto';
import { formatNumber } from '@nirvana/core/utils';
import { Button, Checkbox, Form, Select } from '@nirvana/ui';
import { constants } from '@nirvana/ui-kit';
import { Fragment, useEffect, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useMutation } from 'react-query';
import Widget from 'src/pages/biz-auto/shared/components/widget';
import { z } from 'zod';
import { useApplicationContext } from '../../shared/use-application';
import { fetchCoverageOptions } from '../queries';
import VehicleLimitsModal from './vehicle-limits-modal';

const { Coverages } = constants;

export const FormAncillaryCoverageSchema = z.object({
  ancillaryCoverages: z.array(
    z.object({
      coverage: z.string(),
      primaryCoverage: z.string(),
      isEnabled: z.boolean().default(false),
      selectedLimit: z.number().optional(),
      selectedDeductible: z.number().optional(),
      vehicleLimits: z
        .array(
          z.object({
            vin: z.string(),
            selectedLimit: z.number().optional(),
          }),
        )
        .optional(),
      vehicleDeductibles: z
        .array(
          z.object({
            vin: z.string(),
            selectedDeductible: z.number().optional(),
          }),
        )
        .optional(),
    }),
  ),
});

type CoverageOptionType = {
  coverage: CoverageType;
  hasVehicleLevelLimit: boolean;
  hasVehicleLevelDeductible: boolean;
  limitOptions?: number[];
  deductibleOptions?: number[];
  vinList?: string[];
};

const SectionAncillaryCoverages = () => {
  const { control } = useFormContext();
  const { applicationDetails } = useApplicationContext();
  const [selectedCoverage, setSelectedCoverage] = useState<{
    coverage: CoverageOptionType;
    index: number;
  } | null>(null);
  const [coverageOptions, setCoverageOptions] = useState<
    Record<string, CoverageOptionType>
  >({});

  const ancillaryCoverages = useWatch({
    control,
    name: 'ancillaryCoverages',
  });

  const { mutate: fetchOptions } = useMutation(fetchCoverageOptions, {
    onSuccess: (data) => {
      // Create map of ancillary coverages
      const ancillaryCoveragesMap: Record<string, AncillaryCoverage> =
        applicationDetails?.underwritingOverrides?.ancillaryCoverages?.reduce(
          (acc, coverage) => {
            acc[coverage.coverage] = coverage;
            return acc;
          },
          {} as Record<string, AncillaryCoverage>,
        ) || {};

      // Update coverage options for all coverages
      const newOptions = { ...coverageOptions };
      data.forEach((option) => {
        if (option.coverage) {
          newOptions[option.coverage] = {
            coverage: option.coverage,
            hasVehicleLevelLimit: option.hasVehicleLevelLimit,
            hasVehicleLevelDeductible: option.hasVehicleLevelDeductible,
            vinList: option.vinList,
            // Keep existing limit/deductible options if they exist
            limitOptions: ancillaryCoveragesMap[option.coverage]?.limitOptions,
            deductibleOptions:
              ancillaryCoveragesMap[option.coverage]?.deductibleOptions,
          };
        }
      });
      setCoverageOptions(newOptions);
    },
  });

  const handleCoverageSelect = (
    coverage: CoverageOptionType,
    index: number,
  ) => {
    setSelectedCoverage({
      coverage,
      index,
    });
  };

  const handleCoverageToggle = (
    coverage: AncillaryCoverage,
    isEnabled: boolean,
  ) => {
    // Get current state of all coverages
    const currentCoverages =
      applicationDetails?.underwritingOverrides?.ancillaryCoverages || [];
    const currentStates = ancillaryCoverages || [];

    // Create request payload with all coverages
    const coveragesPayload = currentCoverages.map((cov, idx) => ({
      coverageType: cov.coverage as CoverageType,
      isEnabled:
        cov.coverage === coverage.coverage
          ? isEnabled
          : (currentStates[idx]?.isEnabled ?? false),
    }));

    // Fetch coverage options
    fetchOptions({
      applicationId: applicationDetails?.id || '',
      coverages: {
        coverages: coveragesPayload,
      } as GetCoverageOptionsRequestBody,
    });
  };

  // Effect to trigger handleCoverageToggle for pre-checked coverages on component load
  useEffect(() => {
    // Find the first selected coverage
    const firstSelectedCoverage =
      applicationDetails?.underwritingOverrides?.ancillaryCoverages?.find(
        (coverage) => coverage.isEnabled,
      );

    if (firstSelectedCoverage) {
      handleCoverageToggle(firstSelectedCoverage, true);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicationDetails?.underwritingOverrides?.ancillaryCoverages]);

  return (
    <Widget title="Ancillary Coverage">
      <div className="grid grid-cols-[1fr_1fr_1fr] gap-4">
        {/* Header */}
        <div />
        <div className="text-xs text-text-hint">Limit</div>
        <div className="text-xs text-text-hint">Deductible</div>

        {/* Form Fields */}
        {applicationDetails?.underwritingOverrides?.ancillaryCoverages?.map(
          (coverage, index) => {
            // Get isEnabled for this row, fallback to false if undefined
            const isEnabled = ancillaryCoverages?.[index]?.isEnabled ?? false;
            const vehicleLimits = ancillaryCoverages?.[index]?.vehicleLimits;
            const vehicleDeductibles =
              ancillaryCoverages?.[index]?.vehicleDeductibles;
            const coverageOption = coverageOptions[coverage.coverage];

            return (
              <Fragment key={coverage.coverage}>
                {/* Coverage Checkbox */}
                <input
                  type="hidden"
                  name={`ancillaryCoverages.${index}.coverage`}
                  value={coverage.coverage}
                />
                <input
                  type="hidden"
                  name={`ancillaryCoverages.${index}.primaryCoverage`}
                  value={coverage.primaryCoverage}
                />
                <Form.Field
                  control={control}
                  name={`ancillaryCoverages.${index}.isEnabled`}
                  render={({ field }) => (
                    <Form.Item className="flex items-center space-x-2 space-y-0">
                      <Form.Control>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            field.onChange(checked);
                            handleCoverageToggle(coverage, Boolean(checked));
                          }}
                          id={`coverage-${coverage.coverage}`}
                        />
                      </Form.Control>
                      <div className="leading-none">
                        <Form.Label htmlFor={`coverage-${coverage.coverage}`}>
                          {Coverages.getCoverageLabelShort(coverage.coverage) ||
                            coverage.coverage}
                        </Form.Label>
                      </div>
                    </Form.Item>
                  )}
                />

                {/* Limit Dropdown */}
                <div>
                  {!coverageOption?.hasVehicleLevelLimit &&
                  coverageOption?.limitOptions ? (
                    <Form.Field
                      control={control}
                      name={`ancillaryCoverages.${index}.selectedLimit`}
                      render={({ field }) => (
                        <Form.Item>
                          <Form.Control>
                            <Select
                              onValueChange={(v) =>
                                field.onChange(v ? +v : undefined)
                              }
                              value={field.value?.toString()}
                              disabled={!isEnabled}
                              allowClear
                            >
                              <Select.Trigger className="w-full">
                                <Select.Value placeholder="Select limit" />
                              </Select.Trigger>
                              <Select.Content>
                                <Select.Group>
                                  <Select.Label>Select Limit</Select.Label>
                                  {coverageOption.limitOptions?.map(
                                    (option) => (
                                      <Select.Item
                                        key={`${coverage}-${option}`}
                                        value={option.toString()}
                                      >
                                        ${formatNumber(option)}
                                      </Select.Item>
                                    ),
                                  )}
                                </Select.Group>
                              </Select.Content>
                            </Select>
                          </Form.Control>
                          <Form.Message />
                        </Form.Item>
                      )}
                    />
                  ) : coverageOption?.hasVehicleLevelLimit &&
                    coverageOption?.limitOptions ? (
                    <>
                      <Button
                        variant="link"
                        type="button"
                        onClick={() =>
                          handleCoverageSelect(coverageOption, index)
                        }
                        disabled={!isEnabled}
                      >
                        Set vehicle limits
                      </Button>
                      {vehicleLimits && (
                        <input
                          type="hidden"
                          name={`ancillaryCoverages.${index}.vehicleLimits`}
                          value={JSON.stringify(vehicleLimits)}
                        />
                      )}
                    </>
                  ) : (
                    '-'
                  )}
                </div>

                {/* Deductible Dropdown */}
                <div>
                  {!coverageOption?.hasVehicleLevelDeductible &&
                  coverageOption?.deductibleOptions ? (
                    <Form.Field
                      control={control}
                      name={`ancillaryCoverages.${index}.selectedDeductible`}
                      render={({ field }) => (
                        <Form.Item>
                          <Form.Control>
                            <Select
                              onValueChange={(v) =>
                                field.onChange(v ? +v : undefined)
                              }
                              value={field.value?.toString()}
                              disabled={!isEnabled}
                              allowClear
                            >
                              <Select.Trigger className="w-full">
                                <Select.Value placeholder="Select deductible" />
                              </Select.Trigger>
                              <Select.Content>
                                <Select.Group>
                                  <Select.Label>Select Deductible</Select.Label>
                                  {coverageOption.deductibleOptions?.map(
                                    (option) => (
                                      <Select.Item
                                        key={`${coverage.coverage}-${option}`}
                                        value={option.toString()}
                                      >
                                        ${formatNumber(option)}
                                      </Select.Item>
                                    ),
                                  )}
                                </Select.Group>
                              </Select.Content>
                            </Select>
                          </Form.Control>
                          <Form.Message />
                        </Form.Item>
                      )}
                    />
                  ) : coverageOption?.hasVehicleLevelDeductible &&
                    coverageOption?.deductibleOptions ? (
                    <>
                      <Button
                        variant="link"
                        type="button"
                        onClick={() =>
                          handleCoverageSelect(coverageOption, index)
                        }
                        disabled={!isEnabled}
                      >
                        Set vehicle deductible
                      </Button>
                      {vehicleDeductibles && (
                        <input
                          type="hidden"
                          name={`ancillaryCoverages.${index}.vehicleDeductibles`}
                          value={JSON.stringify(vehicleDeductibles)}
                        />
                      )}
                    </>
                  ) : (
                    '-'
                  )}
                </div>
              </Fragment>
            );
          },
        )}
      </div>

      {selectedCoverage && (
        <VehicleLimitsModal
          open={!!selectedCoverage}
          onClose={() => setSelectedCoverage(null)}
          index={selectedCoverage.index}
          vehiclesInfo={
            coverageOptions[selectedCoverage.coverage.coverage]?.vinList || []
          }
          limitOptions={selectedCoverage.coverage.limitOptions}
          deductibleOptions={selectedCoverage.coverage.deductibleOptions}
          hasVehicleLevelLimit={
            coverageOptions[selectedCoverage.coverage.coverage]
              ?.hasVehicleLevelLimit
          }
          hasVehicleLevelDeductible={
            coverageOptions[selectedCoverage.coverage.coverage]
              ?.hasVehicleLevelDeductible
          }
        />
      )}
    </Widget>
  );
};

export default SectionAncillaryCoverages;
