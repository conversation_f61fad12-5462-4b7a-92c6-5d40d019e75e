import * as React from 'react';
import { Meta } from '@storybook/react';

import { Avatar, Button, Form, Select } from '@nirvana/ui';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const meta: Meta<typeof Select> = {
  title: 'Design System/Select',
  component: Select,
};

export default meta;

export function BasicSelect() {
  return (
    <Select>
      <Select.Trigger className="w-[280px]">
        <Select.Value placeholder="Select an option" />
      </Select.Trigger>
      <Select.Content>
        <Select.Group>
          <Select.Label>Fruits</Select.Label>
          <Select.Item value="apple">Apple</Select.Item>
          <Select.Item value="banana">Banana</Select.Item>
          <Select.Item value="blueberry">Blueberry</Select.Item>
          <Select.Item value="grapes">Grapes</Select.Item>
          <Select.Item value="pineapple">Pineapple</Select.Item>
        </Select.Group>
      </Select.Content>
    </Select>
  );
}

export function Scrollable() {
  return (
    <Select>
      <Select.Trigger className="w-[280px]">
        <Select.Value placeholder="Select a timezone" />
      </Select.Trigger>
      <Select.Content>
        <Select.Group>
          <Select.Label>North America</Select.Label>
          <Select.Item value="est">Eastern Standard Time (EST)</Select.Item>
          <Select.Item value="cst">Central Standard Time (CST)</Select.Item>
          <Select.Item value="mst">Mountain Standard Time (MST)</Select.Item>
          <Select.Item value="pst">Pacific Standard Time (PST)</Select.Item>
          <Select.Item value="akst">Alaska Standard Time (AKST)</Select.Item>
          <Select.Item value="hst">Hawaii Standard Time (HST)</Select.Item>
        </Select.Group>
        <Select.Group>
          <Select.Label>Europe & Africa</Select.Label>
          <Select.Item value="gmt">Greenwich Mean Time (GMT)</Select.Item>
          <Select.Item value="cet">Central European Time (CET)</Select.Item>
          <Select.Item value="eet">Eastern European Time (EET)</Select.Item>
          <Select.Item value="west">
            Western European Summer Time (WEST)
          </Select.Item>
          <Select.Item value="cat">Central Africa Time (CAT)</Select.Item>
          <Select.Item value="eat">East Africa Time (EAT)</Select.Item>
        </Select.Group>
        <Select.Group>
          <Select.Label>Asia</Select.Label>
          <Select.Item value="msk">Moscow Time (MSK)</Select.Item>
          <Select.Item value="ist">India Standard Time (IST)</Select.Item>
          <Select.Item value="cst_china">China Standard Time (CST)</Select.Item>
          <Select.Item value="jst">Japan Standard Time (JST)</Select.Item>
          <Select.Item value="kst">Korea Standard Time (KST)</Select.Item>
          <Select.Item value="ist_indonesia">
            Indonesia Central Standard Time (WITA)
          </Select.Item>
        </Select.Group>
        <Select.Group>
          <Select.Label>Australia & Pacific</Select.Label>
          <Select.Item value="awst">
            Australian Western Standard Time (AWST)
          </Select.Item>
          <Select.Item value="acst">
            Australian Central Standard Time (ACST)
          </Select.Item>
          <Select.Item value="aest">
            Australian Eastern Standard Time (AEST)
          </Select.Item>
          <Select.Item value="nzst">
            New Zealand Standard Time (NZST)
          </Select.Item>
          <Select.Item value="fjt">Fiji Time (FJT)</Select.Item>
        </Select.Group>
        <Select.Group>
          <Select.Label>South America</Select.Label>
          <Select.Item value="art">Argentina Time (ART)</Select.Item>
          <Select.Item value="bot">Bolivia Time (BOT)</Select.Item>
          <Select.Item value="brt">Brasilia Time (BRT)</Select.Item>
          <Select.Item value="clt">Chile Standard Time (CLT)</Select.Item>
        </Select.Group>
      </Select.Content>
    </Select>
  );
}

const underwriters = [
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/1c327b098d59090352aad251dac6edaf.jpg',
    id: '802b74fd-ddd0-423f-881b-31f37a412bc5',
    name: 'Abbi Porter',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/038fd52ede210ddce15740d76e6c7b6a.jpg',
    id: 'c8d674e5-e245-4eb8-900f-03b93fe80505',
    name: 'Amanda Hensel',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/36f8bc37ec31ae7aa27960ddd51b05d2.jpg',
    id: '628c6597-707b-4741-8099-8aa567bcf507',
    name: 'Ashley Laubscher',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/9c3f39d067949c9509887843d52ee779.jpg',
    id: 'd0dcb1b6-2607-4b9f-ac45-f76b91ed2450',
    name: 'Joni Linhart',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/8ed994adf29f2865895ed070c7283c64.jpg',
    id: 'ad6bfe2e-2456-4e00-bfee-be802191c1dc',
    name: 'Jordan Cruz',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/35f8f3b74ae92aaabf238cf393663579.jpg',
    id: 'f0d31120-abf2-47a4-b482-84f759d7418f',
    name: 'Joseph Silvestro',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/f676e8442e2ba293ae505f969d0cf797.jpg',
    id: '48b27fd0-61df-4fd5-8f64-099e6d571508',
    name: 'Karleigh Schroeder',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/f7579a173a3b235d9f360486e4fee6b5.jpg',
    id: '4bf3cc35-9347-428f-a18b-3407fa691412',
    name: 'Mark McDonald',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/3a450b57e8ad88608a4c14a62bec24ea.jpg',
    id: '182462d6-0d6e-414f-b124-c6498ffebf14',
    name: 'Michael Givens',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/344bcc39885ef5e64d34bb29cfa36a14.jpg',
    id: '2e2eeeea-32f5-4351-8df6-857d0f99ffac',
    name: 'Michael Socrates',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/29e99a560362de626573830028ccc331.jpg',
    id: '24484d42-700b-4741-9442-1dc9f75280a4',
    name: 'Michele Smith',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/0c59e518ae56e18574c20caff3c30dc3.jpg',
    id: 'e00a9f90-cde9-4241-97c6-d4492317c5ec',
    name: 'Siran Wixom',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/9b92dc2b5fbef3054d91380965bfefd7.jpg',
    id: 'e5858799-4373-4a50-9577-29d84b07eb98',
    name: 'Stephanie Makowski',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/937da30c154c4399f58059614efdc0fd.jpg',
    id: '7dff3b41-98b2-41eb-9c67-8c96a5a7ce31',
    name: 'Taylor Gosiaco',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/6ebe07de085e46c4e02412d2d315ef93.jpg',
    id: '8bef0ba7-0ab4-4c03-8bd9-274c753957fb',
    name: 'Xandrea Powell',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/3922ed75f58b9b87127cbcd6acbc16b9.jpg',
    id: '4f288547-4ac5-4524-a0bd-40a3c3d790bd',
    name: 'Ash Harris',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/d5d7a4b9998bbf6dadf84c0b82afefd0.jpg',
    id: '63d3641b-7983-4051-9bb6-73d0bf77c0d6',
    name: 'Barbara Bateman',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/ba53d8ea40da4f0c6bbc2c66f6cbfe7d.jpg',
    id: '0c4e08d7-0c7a-445f-8249-d17c826cc51c',
    name: 'Bill Dahm',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/d40de09c9482cfd2e37e49513f89f049.jpg',
    id: '22a80508-0dc4-4491-9c67-ec1ef4b19622',
    name: 'Chevy Elliott',
  },
  {
    email: '<EMAIL>',
    iconUrl:
      'https://dywmdoipgmhli.cloudfront.net/assets/user-icons/e4a3c1b11db167ecacd1ab7040aefccd.jpg',
    id: '9a378a15-bb5d-4133-a65c-09e9be3e813e',
    name: 'Craig Harmon',
  },
];

export function WithComplexChildren() {
  return (
    <Select>
      <Select.Trigger className="w-[280px]">
        <Select.Value placeholder="Select an underwriter" />
      </Select.Trigger>
      <Select.Content className="h-96">
        {underwriters
          .sort((a, b) => a.name.localeCompare(b.name))
          .map((uw) => (
            <Select.Item key={uw.id} value={uw.id}>
              <div className="flex items-center gap-2">
                <Avatar>
                  <Avatar.Image className="object-cover" src={uw.iconUrl} />
                  <Avatar.Fallback>{uw.name.charAt(0)}</Avatar.Fallback>
                </Avatar>
                {uw.name}
              </div>
            </Select.Item>
          ))}
      </Select.Content>
    </Select>
  );
}

const declineReasons = [
  'Clearance/Duplicate',
  'Driver',
  'Losses',
  'Financials',
  'Operations',
  'Safety',
  'Telematics',
  'Incumbent Conflict',
];

const declineFormSchema = z.object({
  declineReason: z.string({ required_error: 'Please select a reason' }),
});

export function SelectWithForm() {
  const form = useForm<z.infer<typeof declineFormSchema>>({
    resolver: zodResolver(declineFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => alert(JSON.stringify(data)))}
        className="space-y-6"
      >
        <Form.Field
          name="declineReason"
          control={form.control}
          render={({ field }) => (
            <Form.Item>
              <Form.Label>Decline Reason</Form.Label>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <Form.Control>
                  <Select.Trigger className="w-[280px]">
                    <Select.Value placeholder="Select a reason" />
                  </Select.Trigger>
                </Form.Control>

                <Select.Content>
                  {declineReasons.map((reason) => (
                    <Select.Item key={reason} value={reason}>
                      {reason}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
              <Form.Description>
                Please select the reason for decline.
              </Form.Description>
              <Form.Message />
            </Form.Item>
          )}
        />

        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}

export function ClearSelectValue() {
  const [value, setValue] = React.useState<string>();

  const form = useForm<z.infer<typeof declineFormSchema>>({
    resolver: zodResolver(declineFormSchema),
  });

  return (
    <>
      <div className="flex gap-4 mb-6">
        <Select allowClear>
          <Select.Trigger className="w-[280px]">
            <Select.Value placeholder="Uncontrolled Select" />
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="apple">Apple</Select.Item>
            <Select.Item value="banana">Banana</Select.Item>
            <Select.Item value="blueberry">Blueberry</Select.Item>
            <Select.Item value="grapes">Grapes</Select.Item>
            <Select.Item value="pineapple">Pineapple</Select.Item>
          </Select.Content>
        </Select>

        <Select allowClear value={value} onValueChange={setValue}>
          <Select.Trigger className="w-[280px]">
            <Select.Value placeholder="Controlled Select" />
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="apple">Apple</Select.Item>
            <Select.Item value="banana">Banana</Select.Item>
            <Select.Item value="blueberry">Blueberry</Select.Item>
            <Select.Item value="grapes">Grapes</Select.Item>
            <Select.Item value="pineapple">Pineapple</Select.Item>
          </Select.Content>
        </Select>
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => alert(JSON.stringify(data)))}
          className="space-y-6"
        >
          <Form.Field
            name="declineReason"
            control={form.control}
            render={({ field }) => (
              <Form.Item>
                <Form.Label>Decline Reason</Form.Label>
                <Select
                  allowClear
                  defaultValue={field.value}
                  onValueChange={field.onChange}
                >
                  <Form.Control>
                    <Select.Trigger className="w-[280px]">
                      <Select.Value placeholder="Select within form" />
                    </Select.Trigger>
                  </Form.Control>

                  <Select.Content>
                    {declineReasons.map((reason) => (
                      <Select.Item key={reason} value={reason}>
                        {reason}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
                <Form.Description>
                  Please select the reason for decline.
                </Form.Description>
                <Form.Message />
              </Form.Item>
            )}
          />

          <Button type="submit">Submit</Button>
        </form>
      </Form>
    </>
  );
}
