// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package nars

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// ClaimParty is an object representing the database table.
type ClaimParty struct {
	ID          string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ClaimID     string      `boil:"claim_id" json:"claim_id" toml:"claim_id" yaml:"claim_id"`
	ExternalID  string      `boil:"external_id" json:"external_id" toml:"external_id" yaml:"external_id"`
	Entity      string      `boil:"entity" json:"entity" toml:"entity" yaml:"entity"`
	FirstName   null.String `boil:"first_name" json:"first_name,omitempty" toml:"first_name" yaml:"first_name,omitempty"`
	MiddleName  null.String `boil:"middle_name" json:"middle_name,omitempty" toml:"middle_name" yaml:"middle_name,omitempty"`
	LastName    null.String `boil:"last_name" json:"last_name,omitempty" toml:"last_name" yaml:"last_name,omitempty"`
	CompanyName null.String `boil:"company_name" json:"company_name,omitempty" toml:"company_name" yaml:"company_name,omitempty"`
	CreatedAt   time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	ModifiedAt  time.Time   `boil:"modified_at" json:"modified_at" toml:"modified_at" yaml:"modified_at"`
	UpdatedAt   time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`

	R *claimPartyR `boil:"" json:"" toml:"" yaml:""`
	L claimPartyL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ClaimPartyColumns = struct {
	ID          string
	ClaimID     string
	ExternalID  string
	Entity      string
	FirstName   string
	MiddleName  string
	LastName    string
	CompanyName string
	CreatedAt   string
	ModifiedAt  string
	UpdatedAt   string
}{
	ID:          "id",
	ClaimID:     "claim_id",
	ExternalID:  "external_id",
	Entity:      "entity",
	FirstName:   "first_name",
	MiddleName:  "middle_name",
	LastName:    "last_name",
	CompanyName: "company_name",
	CreatedAt:   "created_at",
	ModifiedAt:  "modified_at",
	UpdatedAt:   "updated_at",
}

var ClaimPartyTableColumns = struct {
	ID          string
	ClaimID     string
	ExternalID  string
	Entity      string
	FirstName   string
	MiddleName  string
	LastName    string
	CompanyName string
	CreatedAt   string
	ModifiedAt  string
	UpdatedAt   string
}{
	ID:          "claim_parties.id",
	ClaimID:     "claim_parties.claim_id",
	ExternalID:  "claim_parties.external_id",
	Entity:      "claim_parties.entity",
	FirstName:   "claim_parties.first_name",
	MiddleName:  "claim_parties.middle_name",
	LastName:    "claim_parties.last_name",
	CompanyName: "claim_parties.company_name",
	CreatedAt:   "claim_parties.created_at",
	ModifiedAt:  "claim_parties.modified_at",
	UpdatedAt:   "claim_parties.updated_at",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var ClaimPartyWhere = struct {
	ID          whereHelperstring
	ClaimID     whereHelperstring
	ExternalID  whereHelperstring
	Entity      whereHelperstring
	FirstName   whereHelpernull_String
	MiddleName  whereHelpernull_String
	LastName    whereHelpernull_String
	CompanyName whereHelpernull_String
	CreatedAt   whereHelpertime_Time
	ModifiedAt  whereHelpertime_Time
	UpdatedAt   whereHelpertime_Time
}{
	ID:          whereHelperstring{field: "\"nars\".\"claim_parties\".\"id\""},
	ClaimID:     whereHelperstring{field: "\"nars\".\"claim_parties\".\"claim_id\""},
	ExternalID:  whereHelperstring{field: "\"nars\".\"claim_parties\".\"external_id\""},
	Entity:      whereHelperstring{field: "\"nars\".\"claim_parties\".\"entity\""},
	FirstName:   whereHelpernull_String{field: "\"nars\".\"claim_parties\".\"first_name\""},
	MiddleName:  whereHelpernull_String{field: "\"nars\".\"claim_parties\".\"middle_name\""},
	LastName:    whereHelpernull_String{field: "\"nars\".\"claim_parties\".\"last_name\""},
	CompanyName: whereHelpernull_String{field: "\"nars\".\"claim_parties\".\"company_name\""},
	CreatedAt:   whereHelpertime_Time{field: "\"nars\".\"claim_parties\".\"created_at\""},
	ModifiedAt:  whereHelpertime_Time{field: "\"nars\".\"claim_parties\".\"modified_at\""},
	UpdatedAt:   whereHelpertime_Time{field: "\"nars\".\"claim_parties\".\"updated_at\""},
}

// ClaimPartyRels is where relationship names are stored.
var ClaimPartyRels = struct {
	Claim string
}{
	Claim: "Claim",
}

// claimPartyR is where relationships are stored.
type claimPartyR struct {
	Claim *Claim `boil:"Claim" json:"Claim" toml:"Claim" yaml:"Claim"`
}

// NewStruct creates a new relationship struct
func (*claimPartyR) NewStruct() *claimPartyR {
	return &claimPartyR{}
}

// claimPartyL is where Load methods for each relationship are stored.
type claimPartyL struct{}

var (
	claimPartyAllColumns            = []string{"id", "claim_id", "external_id", "entity", "first_name", "middle_name", "last_name", "company_name", "created_at", "modified_at", "updated_at"}
	claimPartyColumnsWithoutDefault = []string{"id", "claim_id", "external_id", "entity", "created_at", "modified_at"}
	claimPartyColumnsWithDefault    = []string{"first_name", "middle_name", "last_name", "company_name", "updated_at"}
	claimPartyPrimaryKeyColumns     = []string{"id"}
	claimPartyGeneratedColumns      = []string{}
)

type (
	// ClaimPartySlice is an alias for a slice of pointers to ClaimParty.
	// This should almost always be used instead of []ClaimParty.
	ClaimPartySlice []*ClaimParty
	// ClaimPartyHook is the signature for custom ClaimParty hook methods
	ClaimPartyHook func(context.Context, boil.ContextExecutor, *ClaimParty) error

	claimPartyQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	claimPartyType                 = reflect.TypeOf(&ClaimParty{})
	claimPartyMapping              = queries.MakeStructMapping(claimPartyType)
	claimPartyPrimaryKeyMapping, _ = queries.BindMapping(claimPartyType, claimPartyMapping, claimPartyPrimaryKeyColumns)
	claimPartyInsertCacheMut       sync.RWMutex
	claimPartyInsertCache          = make(map[string]insertCache)
	claimPartyUpdateCacheMut       sync.RWMutex
	claimPartyUpdateCache          = make(map[string]updateCache)
	claimPartyUpsertCacheMut       sync.RWMutex
	claimPartyUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var claimPartyAfterSelectHooks []ClaimPartyHook

var claimPartyBeforeInsertHooks []ClaimPartyHook
var claimPartyAfterInsertHooks []ClaimPartyHook

var claimPartyBeforeUpdateHooks []ClaimPartyHook
var claimPartyAfterUpdateHooks []ClaimPartyHook

var claimPartyBeforeDeleteHooks []ClaimPartyHook
var claimPartyAfterDeleteHooks []ClaimPartyHook

var claimPartyBeforeUpsertHooks []ClaimPartyHook
var claimPartyAfterUpsertHooks []ClaimPartyHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ClaimParty) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ClaimParty) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ClaimParty) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ClaimParty) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ClaimParty) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ClaimParty) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ClaimParty) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ClaimParty) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ClaimParty) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimPartyAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddClaimPartyHook registers your hook function for all future operations.
func AddClaimPartyHook(hookPoint boil.HookPoint, claimPartyHook ClaimPartyHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		claimPartyAfterSelectHooks = append(claimPartyAfterSelectHooks, claimPartyHook)
	case boil.BeforeInsertHook:
		claimPartyBeforeInsertHooks = append(claimPartyBeforeInsertHooks, claimPartyHook)
	case boil.AfterInsertHook:
		claimPartyAfterInsertHooks = append(claimPartyAfterInsertHooks, claimPartyHook)
	case boil.BeforeUpdateHook:
		claimPartyBeforeUpdateHooks = append(claimPartyBeforeUpdateHooks, claimPartyHook)
	case boil.AfterUpdateHook:
		claimPartyAfterUpdateHooks = append(claimPartyAfterUpdateHooks, claimPartyHook)
	case boil.BeforeDeleteHook:
		claimPartyBeforeDeleteHooks = append(claimPartyBeforeDeleteHooks, claimPartyHook)
	case boil.AfterDeleteHook:
		claimPartyAfterDeleteHooks = append(claimPartyAfterDeleteHooks, claimPartyHook)
	case boil.BeforeUpsertHook:
		claimPartyBeforeUpsertHooks = append(claimPartyBeforeUpsertHooks, claimPartyHook)
	case boil.AfterUpsertHook:
		claimPartyAfterUpsertHooks = append(claimPartyAfterUpsertHooks, claimPartyHook)
	}
}

// One returns a single claimParty record from the query.
func (q claimPartyQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ClaimParty, error) {
	o := &ClaimParty{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "nars: failed to execute a one query for claim_parties")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ClaimParty records from the query.
func (q claimPartyQuery) All(ctx context.Context, exec boil.ContextExecutor) (ClaimPartySlice, error) {
	var o []*ClaimParty

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "nars: failed to assign all query results to ClaimParty slice")
	}

	if len(claimPartyAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ClaimParty records in the query.
func (q claimPartyQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to count claim_parties rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q claimPartyQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "nars: failed to check if claim_parties exists")
	}

	return count > 0, nil
}

// Claim pointed to by the foreign key.
func (o *ClaimParty) Claim(mods ...qm.QueryMod) claimQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ClaimID),
	}

	queryMods = append(queryMods, mods...)

	return Claims(queryMods...)
}

// LoadClaim allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (claimPartyL) LoadClaim(ctx context.Context, e boil.ContextExecutor, singular bool, maybeClaimParty interface{}, mods queries.Applicator) error {
	var slice []*ClaimParty
	var object *ClaimParty

	if singular {
		object = maybeClaimParty.(*ClaimParty)
	} else {
		slice = *maybeClaimParty.(*[]*ClaimParty)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &claimPartyR{}
		}
		args = append(args, object.ClaimID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &claimPartyR{}
			}

			for _, a := range args {
				if a == obj.ClaimID {
					continue Outer
				}
			}

			args = append(args, obj.ClaimID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`nars.claims`),
		qm.WhereIn(`nars.claims.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Claim")
	}

	var resultSlice []*Claim
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Claim")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for claims")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for claims")
	}

	if len(claimPartyAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Claim = foreign
		if foreign.R == nil {
			foreign.R = &claimR{}
		}
		foreign.R.ClaimParties = append(foreign.R.ClaimParties, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ClaimID == foreign.ID {
				local.R.Claim = foreign
				if foreign.R == nil {
					foreign.R = &claimR{}
				}
				foreign.R.ClaimParties = append(foreign.R.ClaimParties, local)
				break
			}
		}
	}

	return nil
}

// SetClaim of the claimParty to the related item.
// Sets o.R.Claim to related.
// Adds o to related.R.ClaimParties.
func (o *ClaimParty) SetClaim(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Claim) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"nars\".\"claim_parties\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"claim_id"}),
		strmangle.WhereClause("\"", "\"", 2, claimPartyPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ClaimID = related.ID
	if o.R == nil {
		o.R = &claimPartyR{
			Claim: related,
		}
	} else {
		o.R.Claim = related
	}

	if related.R == nil {
		related.R = &claimR{
			ClaimParties: ClaimPartySlice{o},
		}
	} else {
		related.R.ClaimParties = append(related.R.ClaimParties, o)
	}

	return nil
}

// ClaimParties retrieves all the records using an executor.
func ClaimParties(mods ...qm.QueryMod) claimPartyQuery {
	mods = append(mods, qm.From("\"nars\".\"claim_parties\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"nars\".\"claim_parties\".*"})
	}

	return claimPartyQuery{q}
}

// FindClaimParty retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindClaimParty(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ClaimParty, error) {
	claimPartyObj := &ClaimParty{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"nars\".\"claim_parties\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, claimPartyObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "nars: unable to select from claim_parties")
	}

	if err = claimPartyObj.doAfterSelectHooks(ctx, exec); err != nil {
		return claimPartyObj, err
	}

	return claimPartyObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ClaimParty) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("nars: no claim_parties provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(claimPartyColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	claimPartyInsertCacheMut.RLock()
	cache, cached := claimPartyInsertCache[key]
	claimPartyInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			claimPartyAllColumns,
			claimPartyColumnsWithDefault,
			claimPartyColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(claimPartyType, claimPartyMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(claimPartyType, claimPartyMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"nars\".\"claim_parties\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"nars\".\"claim_parties\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "nars: unable to insert into claim_parties")
	}

	if !cached {
		claimPartyInsertCacheMut.Lock()
		claimPartyInsertCache[key] = cache
		claimPartyInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ClaimParty.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ClaimParty) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	claimPartyUpdateCacheMut.RLock()
	cache, cached := claimPartyUpdateCache[key]
	claimPartyUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			claimPartyAllColumns,
			claimPartyPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("nars: unable to update claim_parties, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"nars\".\"claim_parties\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, claimPartyPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(claimPartyType, claimPartyMapping, append(wl, claimPartyPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to update claim_parties row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by update for claim_parties")
	}

	if !cached {
		claimPartyUpdateCacheMut.Lock()
		claimPartyUpdateCache[key] = cache
		claimPartyUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q claimPartyQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to update all for claim_parties")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to retrieve rows affected for claim_parties")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ClaimPartySlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("nars: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPartyPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"nars\".\"claim_parties\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, claimPartyPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to update all in claimParty slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to retrieve rows affected all in update all claimParty")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ClaimParty) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("nars: no claim_parties provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(claimPartyColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	claimPartyUpsertCacheMut.RLock()
	cache, cached := claimPartyUpsertCache[key]
	claimPartyUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			claimPartyAllColumns,
			claimPartyColumnsWithDefault,
			claimPartyColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			claimPartyAllColumns,
			claimPartyPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("nars: unable to upsert claim_parties, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(claimPartyPrimaryKeyColumns))
			copy(conflict, claimPartyPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"nars\".\"claim_parties\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(claimPartyType, claimPartyMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(claimPartyType, claimPartyMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "nars: unable to upsert claim_parties")
	}

	if !cached {
		claimPartyUpsertCacheMut.Lock()
		claimPartyUpsertCache[key] = cache
		claimPartyUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ClaimParty record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ClaimParty) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("nars: no ClaimParty provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), claimPartyPrimaryKeyMapping)
	sql := "DELETE FROM \"nars\".\"claim_parties\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to delete from claim_parties")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by delete for claim_parties")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q claimPartyQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("nars: no claimPartyQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to delete all from claim_parties")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by deleteall for claim_parties")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ClaimPartySlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(claimPartyBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPartyPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"nars\".\"claim_parties\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, claimPartyPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to delete all from claimParty slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by deleteall for claim_parties")
	}

	if len(claimPartyAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ClaimParty) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindClaimParty(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ClaimPartySlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ClaimPartySlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPartyPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"nars\".\"claim_parties\".* FROM \"nars\".\"claim_parties\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, claimPartyPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "nars: unable to reload all in ClaimPartySlice")
	}

	*o = slice

	return nil
}

// ClaimPartyExists checks if the ClaimParty row exists.
func ClaimPartyExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"nars\".\"claim_parties\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "nars: unable to check if claim_parties exists")
	}

	return exists, nil
}
