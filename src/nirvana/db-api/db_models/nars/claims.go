// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package nars

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Claim is an object representing the database table.
type Claim struct {
	ID                  string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ExternalID          string      `boil:"external_id" json:"external_id" toml:"external_id" yaml:"external_id"`
	Status              string      `boil:"status" json:"status" toml:"status" yaml:"status"`
	PolicyNumber        string      `boil:"policy_number" json:"policy_number" toml:"policy_number" yaml:"policy_number"`
	LineOfBusiness      string      `boil:"line_of_business" json:"line_of_business" toml:"line_of_business" yaml:"line_of_business"`
	CreatedAt           time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt           time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	ModifiedAt          time.Time   `boil:"modified_at" json:"modified_at" toml:"modified_at" yaml:"modified_at"`
	ReportedBy          null.String `boil:"reported_by" json:"reported_by,omitempty" toml:"reported_by" yaml:"reported_by,omitempty"`
	ReportedAt          time.Time   `boil:"reported_at" json:"reported_at" toml:"reported_at" yaml:"reported_at"`
	AdjusterName        string      `boil:"adjuster_name" json:"adjuster_name" toml:"adjuster_name" yaml:"adjuster_name"`
	AdjusterEmail       string      `boil:"adjuster_email" json:"adjuster_email" toml:"adjuster_email" yaml:"adjuster_email"`
	AdjusterPhoneNumber null.String `boil:"adjuster_phone_number" json:"adjuster_phone_number,omitempty" toml:"adjuster_phone_number" yaml:"adjuster_phone_number,omitempty"`
	ClientClaimNumber   null.String `boil:"client_claim_number" json:"client_claim_number,omitempty" toml:"client_claim_number" yaml:"client_claim_number,omitempty"`
	LossDatetime        null.Time   `boil:"loss_datetime" json:"loss_datetime,omitempty" toml:"loss_datetime" yaml:"loss_datetime,omitempty"`
	LossState           null.String `boil:"loss_state" json:"loss_state,omitempty" toml:"loss_state" yaml:"loss_state,omitempty"`
	LossDescription     null.String `boil:"loss_description" json:"loss_description,omitempty" toml:"loss_description" yaml:"loss_description,omitempty"`

	R *claimR `boil:"" json:"" toml:"" yaml:""`
	L claimL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ClaimColumns = struct {
	ID                  string
	ExternalID          string
	Status              string
	PolicyNumber        string
	LineOfBusiness      string
	CreatedAt           string
	UpdatedAt           string
	ModifiedAt          string
	ReportedBy          string
	ReportedAt          string
	AdjusterName        string
	AdjusterEmail       string
	AdjusterPhoneNumber string
	ClientClaimNumber   string
	LossDatetime        string
	LossState           string
	LossDescription     string
}{
	ID:                  "id",
	ExternalID:          "external_id",
	Status:              "status",
	PolicyNumber:        "policy_number",
	LineOfBusiness:      "line_of_business",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	ModifiedAt:          "modified_at",
	ReportedBy:          "reported_by",
	ReportedAt:          "reported_at",
	AdjusterName:        "adjuster_name",
	AdjusterEmail:       "adjuster_email",
	AdjusterPhoneNumber: "adjuster_phone_number",
	ClientClaimNumber:   "client_claim_number",
	LossDatetime:        "loss_datetime",
	LossState:           "loss_state",
	LossDescription:     "loss_description",
}

var ClaimTableColumns = struct {
	ID                  string
	ExternalID          string
	Status              string
	PolicyNumber        string
	LineOfBusiness      string
	CreatedAt           string
	UpdatedAt           string
	ModifiedAt          string
	ReportedBy          string
	ReportedAt          string
	AdjusterName        string
	AdjusterEmail       string
	AdjusterPhoneNumber string
	ClientClaimNumber   string
	LossDatetime        string
	LossState           string
	LossDescription     string
}{
	ID:                  "claims.id",
	ExternalID:          "claims.external_id",
	Status:              "claims.status",
	PolicyNumber:        "claims.policy_number",
	LineOfBusiness:      "claims.line_of_business",
	CreatedAt:           "claims.created_at",
	UpdatedAt:           "claims.updated_at",
	ModifiedAt:          "claims.modified_at",
	ReportedBy:          "claims.reported_by",
	ReportedAt:          "claims.reported_at",
	AdjusterName:        "claims.adjuster_name",
	AdjusterEmail:       "claims.adjuster_email",
	AdjusterPhoneNumber: "claims.adjuster_phone_number",
	ClientClaimNumber:   "claims.client_claim_number",
	LossDatetime:        "claims.loss_datetime",
	LossState:           "claims.loss_state",
	LossDescription:     "claims.loss_description",
}

// Generated where

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ClaimWhere = struct {
	ID                  whereHelperstring
	ExternalID          whereHelperstring
	Status              whereHelperstring
	PolicyNumber        whereHelperstring
	LineOfBusiness      whereHelperstring
	CreatedAt           whereHelpertime_Time
	UpdatedAt           whereHelpertime_Time
	ModifiedAt          whereHelpertime_Time
	ReportedBy          whereHelpernull_String
	ReportedAt          whereHelpertime_Time
	AdjusterName        whereHelperstring
	AdjusterEmail       whereHelperstring
	AdjusterPhoneNumber whereHelpernull_String
	ClientClaimNumber   whereHelpernull_String
	LossDatetime        whereHelpernull_Time
	LossState           whereHelpernull_String
	LossDescription     whereHelpernull_String
}{
	ID:                  whereHelperstring{field: "\"nars\".\"claims\".\"id\""},
	ExternalID:          whereHelperstring{field: "\"nars\".\"claims\".\"external_id\""},
	Status:              whereHelperstring{field: "\"nars\".\"claims\".\"status\""},
	PolicyNumber:        whereHelperstring{field: "\"nars\".\"claims\".\"policy_number\""},
	LineOfBusiness:      whereHelperstring{field: "\"nars\".\"claims\".\"line_of_business\""},
	CreatedAt:           whereHelpertime_Time{field: "\"nars\".\"claims\".\"created_at\""},
	UpdatedAt:           whereHelpertime_Time{field: "\"nars\".\"claims\".\"updated_at\""},
	ModifiedAt:          whereHelpertime_Time{field: "\"nars\".\"claims\".\"modified_at\""},
	ReportedBy:          whereHelpernull_String{field: "\"nars\".\"claims\".\"reported_by\""},
	ReportedAt:          whereHelpertime_Time{field: "\"nars\".\"claims\".\"reported_at\""},
	AdjusterName:        whereHelperstring{field: "\"nars\".\"claims\".\"adjuster_name\""},
	AdjusterEmail:       whereHelperstring{field: "\"nars\".\"claims\".\"adjuster_email\""},
	AdjusterPhoneNumber: whereHelpernull_String{field: "\"nars\".\"claims\".\"adjuster_phone_number\""},
	ClientClaimNumber:   whereHelpernull_String{field: "\"nars\".\"claims\".\"client_claim_number\""},
	LossDatetime:        whereHelpernull_Time{field: "\"nars\".\"claims\".\"loss_datetime\""},
	LossState:           whereHelpernull_String{field: "\"nars\".\"claims\".\"loss_state\""},
	LossDescription:     whereHelpernull_String{field: "\"nars\".\"claims\".\"loss_description\""},
}

// ClaimRels is where relationship names are stored.
var ClaimRels = struct {
	ClaimParties                  string
	ClaimExternalNotes            string
	PostingsFetches               string
	ClaimExternalReserveSummaries string
	ClaimExternalStatuses         string
}{
	ClaimParties:                  "ClaimParties",
	ClaimExternalNotes:            "ClaimExternalNotes",
	PostingsFetches:               "PostingsFetches",
	ClaimExternalReserveSummaries: "ClaimExternalReserveSummaries",
	ClaimExternalStatuses:         "ClaimExternalStatuses",
}

// claimR is where relationships are stored.
type claimR struct {
	ClaimParties                  ClaimPartySlice     `boil:"ClaimParties" json:"ClaimParties" toml:"ClaimParties" yaml:"ClaimParties"`
	ClaimExternalNotes            NoteSlice           `boil:"ClaimExternalNotes" json:"ClaimExternalNotes" toml:"ClaimExternalNotes" yaml:"ClaimExternalNotes"`
	PostingsFetches               PostingsFetchSlice  `boil:"PostingsFetches" json:"PostingsFetches" toml:"PostingsFetches" yaml:"PostingsFetches"`
	ClaimExternalReserveSummaries ReserveSummarySlice `boil:"ClaimExternalReserveSummaries" json:"ClaimExternalReserveSummaries" toml:"ClaimExternalReserveSummaries" yaml:"ClaimExternalReserveSummaries"`
	ClaimExternalStatuses         StatusSlice         `boil:"ClaimExternalStatuses" json:"ClaimExternalStatuses" toml:"ClaimExternalStatuses" yaml:"ClaimExternalStatuses"`
}

// NewStruct creates a new relationship struct
func (*claimR) NewStruct() *claimR {
	return &claimR{}
}

// claimL is where Load methods for each relationship are stored.
type claimL struct{}

var (
	claimAllColumns            = []string{"id", "external_id", "status", "policy_number", "line_of_business", "created_at", "updated_at", "modified_at", "reported_by", "reported_at", "adjuster_name", "adjuster_email", "adjuster_phone_number", "client_claim_number", "loss_datetime", "loss_state", "loss_description"}
	claimColumnsWithoutDefault = []string{"id", "external_id", "status", "policy_number", "line_of_business", "created_at", "updated_at"}
	claimColumnsWithDefault    = []string{"modified_at", "reported_by", "reported_at", "adjuster_name", "adjuster_email", "adjuster_phone_number", "client_claim_number", "loss_datetime", "loss_state", "loss_description"}
	claimPrimaryKeyColumns     = []string{"id"}
	claimGeneratedColumns      = []string{}
)

type (
	// ClaimSlice is an alias for a slice of pointers to Claim.
	// This should almost always be used instead of []Claim.
	ClaimSlice []*Claim
	// ClaimHook is the signature for custom Claim hook methods
	ClaimHook func(context.Context, boil.ContextExecutor, *Claim) error

	claimQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	claimType                 = reflect.TypeOf(&Claim{})
	claimMapping              = queries.MakeStructMapping(claimType)
	claimPrimaryKeyMapping, _ = queries.BindMapping(claimType, claimMapping, claimPrimaryKeyColumns)
	claimInsertCacheMut       sync.RWMutex
	claimInsertCache          = make(map[string]insertCache)
	claimUpdateCacheMut       sync.RWMutex
	claimUpdateCache          = make(map[string]updateCache)
	claimUpsertCacheMut       sync.RWMutex
	claimUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var claimAfterSelectHooks []ClaimHook

var claimBeforeInsertHooks []ClaimHook
var claimAfterInsertHooks []ClaimHook

var claimBeforeUpdateHooks []ClaimHook
var claimAfterUpdateHooks []ClaimHook

var claimBeforeDeleteHooks []ClaimHook
var claimAfterDeleteHooks []ClaimHook

var claimBeforeUpsertHooks []ClaimHook
var claimAfterUpsertHooks []ClaimHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Claim) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Claim) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Claim) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Claim) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Claim) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Claim) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Claim) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Claim) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Claim) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddClaimHook registers your hook function for all future operations.
func AddClaimHook(hookPoint boil.HookPoint, claimHook ClaimHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		claimAfterSelectHooks = append(claimAfterSelectHooks, claimHook)
	case boil.BeforeInsertHook:
		claimBeforeInsertHooks = append(claimBeforeInsertHooks, claimHook)
	case boil.AfterInsertHook:
		claimAfterInsertHooks = append(claimAfterInsertHooks, claimHook)
	case boil.BeforeUpdateHook:
		claimBeforeUpdateHooks = append(claimBeforeUpdateHooks, claimHook)
	case boil.AfterUpdateHook:
		claimAfterUpdateHooks = append(claimAfterUpdateHooks, claimHook)
	case boil.BeforeDeleteHook:
		claimBeforeDeleteHooks = append(claimBeforeDeleteHooks, claimHook)
	case boil.AfterDeleteHook:
		claimAfterDeleteHooks = append(claimAfterDeleteHooks, claimHook)
	case boil.BeforeUpsertHook:
		claimBeforeUpsertHooks = append(claimBeforeUpsertHooks, claimHook)
	case boil.AfterUpsertHook:
		claimAfterUpsertHooks = append(claimAfterUpsertHooks, claimHook)
	}
}

// One returns a single claim record from the query.
func (q claimQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Claim, error) {
	o := &Claim{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "nars: failed to execute a one query for claims")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Claim records from the query.
func (q claimQuery) All(ctx context.Context, exec boil.ContextExecutor) (ClaimSlice, error) {
	var o []*Claim

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "nars: failed to assign all query results to Claim slice")
	}

	if len(claimAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Claim records in the query.
func (q claimQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to count claims rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q claimQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "nars: failed to check if claims exists")
	}

	return count > 0, nil
}

// ClaimParties retrieves all the claim_party's ClaimParties with an executor.
func (o *Claim) ClaimParties(mods ...qm.QueryMod) claimPartyQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"nars\".\"claim_parties\".\"claim_id\"=?", o.ID),
	)

	return ClaimParties(queryMods...)
}

// ClaimExternalNotes retrieves all the note's Notes with an executor via claim_external_id column.
func (o *Claim) ClaimExternalNotes(mods ...qm.QueryMod) noteQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"nars\".\"notes\".\"claim_external_id\"=?", o.ExternalID),
	)

	return Notes(queryMods...)
}

// PostingsFetches retrieves all the postings_fetch's PostingsFetches with an executor.
func (o *Claim) PostingsFetches(mods ...qm.QueryMod) postingsFetchQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"nars\".\"postings_fetches\".\"claim_id\"=?", o.ID),
	)

	return PostingsFetches(queryMods...)
}

// ClaimExternalReserveSummaries retrieves all the reserve_summary's ReserveSummaries with an executor via claim_external_id column.
func (o *Claim) ClaimExternalReserveSummaries(mods ...qm.QueryMod) reserveSummaryQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"nars\".\"reserve_summaries\".\"claim_external_id\"=?", o.ExternalID),
	)

	return ReserveSummaries(queryMods...)
}

// ClaimExternalStatuses retrieves all the status's Statuses with an executor via claim_external_id column.
func (o *Claim) ClaimExternalStatuses(mods ...qm.QueryMod) statusQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"nars\".\"status\".\"claim_external_id\"=?", o.ExternalID),
	)

	return Statuses(queryMods...)
}

// LoadClaimParties allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (claimL) LoadClaimParties(ctx context.Context, e boil.ContextExecutor, singular bool, maybeClaim interface{}, mods queries.Applicator) error {
	var slice []*Claim
	var object *Claim

	if singular {
		object = maybeClaim.(*Claim)
	} else {
		slice = *maybeClaim.(*[]*Claim)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &claimR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &claimR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`nars.claim_parties`),
		qm.WhereIn(`nars.claim_parties.claim_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load claim_parties")
	}

	var resultSlice []*ClaimParty
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice claim_parties")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on claim_parties")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for claim_parties")
	}

	if len(claimPartyAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ClaimParties = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &claimPartyR{}
			}
			foreign.R.Claim = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ClaimID {
				local.R.ClaimParties = append(local.R.ClaimParties, foreign)
				if foreign.R == nil {
					foreign.R = &claimPartyR{}
				}
				foreign.R.Claim = local
				break
			}
		}
	}

	return nil
}

// LoadClaimExternalNotes allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (claimL) LoadClaimExternalNotes(ctx context.Context, e boil.ContextExecutor, singular bool, maybeClaim interface{}, mods queries.Applicator) error {
	var slice []*Claim
	var object *Claim

	if singular {
		object = maybeClaim.(*Claim)
	} else {
		slice = *maybeClaim.(*[]*Claim)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &claimR{}
		}
		args = append(args, object.ExternalID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &claimR{}
			}

			for _, a := range args {
				if a == obj.ExternalID {
					continue Outer
				}
			}

			args = append(args, obj.ExternalID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`nars.notes`),
		qm.WhereIn(`nars.notes.claim_external_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load notes")
	}

	var resultSlice []*Note
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice notes")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on notes")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for notes")
	}

	if len(noteAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ClaimExternalNotes = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &noteR{}
			}
			foreign.R.ClaimExternal = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ExternalID == foreign.ClaimExternalID {
				local.R.ClaimExternalNotes = append(local.R.ClaimExternalNotes, foreign)
				if foreign.R == nil {
					foreign.R = &noteR{}
				}
				foreign.R.ClaimExternal = local
				break
			}
		}
	}

	return nil
}

// LoadPostingsFetches allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (claimL) LoadPostingsFetches(ctx context.Context, e boil.ContextExecutor, singular bool, maybeClaim interface{}, mods queries.Applicator) error {
	var slice []*Claim
	var object *Claim

	if singular {
		object = maybeClaim.(*Claim)
	} else {
		slice = *maybeClaim.(*[]*Claim)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &claimR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &claimR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`nars.postings_fetches`),
		qm.WhereIn(`nars.postings_fetches.claim_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load postings_fetches")
	}

	var resultSlice []*PostingsFetch
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice postings_fetches")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on postings_fetches")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for postings_fetches")
	}

	if len(postingsFetchAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.PostingsFetches = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &postingsFetchR{}
			}
			foreign.R.Claim = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ClaimID {
				local.R.PostingsFetches = append(local.R.PostingsFetches, foreign)
				if foreign.R == nil {
					foreign.R = &postingsFetchR{}
				}
				foreign.R.Claim = local
				break
			}
		}
	}

	return nil
}

// LoadClaimExternalReserveSummaries allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (claimL) LoadClaimExternalReserveSummaries(ctx context.Context, e boil.ContextExecutor, singular bool, maybeClaim interface{}, mods queries.Applicator) error {
	var slice []*Claim
	var object *Claim

	if singular {
		object = maybeClaim.(*Claim)
	} else {
		slice = *maybeClaim.(*[]*Claim)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &claimR{}
		}
		args = append(args, object.ExternalID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &claimR{}
			}

			for _, a := range args {
				if a == obj.ExternalID {
					continue Outer
				}
			}

			args = append(args, obj.ExternalID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`nars.reserve_summaries`),
		qm.WhereIn(`nars.reserve_summaries.claim_external_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load reserve_summaries")
	}

	var resultSlice []*ReserveSummary
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice reserve_summaries")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on reserve_summaries")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for reserve_summaries")
	}

	if len(reserveSummaryAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ClaimExternalReserveSummaries = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &reserveSummaryR{}
			}
			foreign.R.ClaimExternal = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ExternalID == foreign.ClaimExternalID {
				local.R.ClaimExternalReserveSummaries = append(local.R.ClaimExternalReserveSummaries, foreign)
				if foreign.R == nil {
					foreign.R = &reserveSummaryR{}
				}
				foreign.R.ClaimExternal = local
				break
			}
		}
	}

	return nil
}

// LoadClaimExternalStatuses allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (claimL) LoadClaimExternalStatuses(ctx context.Context, e boil.ContextExecutor, singular bool, maybeClaim interface{}, mods queries.Applicator) error {
	var slice []*Claim
	var object *Claim

	if singular {
		object = maybeClaim.(*Claim)
	} else {
		slice = *maybeClaim.(*[]*Claim)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &claimR{}
		}
		args = append(args, object.ExternalID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &claimR{}
			}

			for _, a := range args {
				if a == obj.ExternalID {
					continue Outer
				}
			}

			args = append(args, obj.ExternalID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`nars.status`),
		qm.WhereIn(`nars.status.claim_external_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load status")
	}

	var resultSlice []*Status
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice status")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on status")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for status")
	}

	if len(statusAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ClaimExternalStatuses = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &statusR{}
			}
			foreign.R.ClaimExternal = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ExternalID == foreign.ClaimExternalID {
				local.R.ClaimExternalStatuses = append(local.R.ClaimExternalStatuses, foreign)
				if foreign.R == nil {
					foreign.R = &statusR{}
				}
				foreign.R.ClaimExternal = local
				break
			}
		}
	}

	return nil
}

// AddClaimParties adds the given related objects to the existing relationships
// of the claim, optionally inserting them as new records.
// Appends related to o.R.ClaimParties.
// Sets related.R.Claim appropriately.
func (o *Claim) AddClaimParties(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ClaimParty) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ClaimID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"nars\".\"claim_parties\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"claim_id"}),
				strmangle.WhereClause("\"", "\"", 2, claimPartyPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ClaimID = o.ID
		}
	}

	if o.R == nil {
		o.R = &claimR{
			ClaimParties: related,
		}
	} else {
		o.R.ClaimParties = append(o.R.ClaimParties, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &claimPartyR{
				Claim: o,
			}
		} else {
			rel.R.Claim = o
		}
	}
	return nil
}

// AddClaimExternalNotes adds the given related objects to the existing relationships
// of the claim, optionally inserting them as new records.
// Appends related to o.R.ClaimExternalNotes.
// Sets related.R.ClaimExternal appropriately.
func (o *Claim) AddClaimExternalNotes(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Note) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ClaimExternalID = o.ExternalID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"nars\".\"notes\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"claim_external_id"}),
				strmangle.WhereClause("\"", "\"", 2, notePrimaryKeyColumns),
			)
			values := []interface{}{o.ExternalID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ClaimExternalID = o.ExternalID
		}
	}

	if o.R == nil {
		o.R = &claimR{
			ClaimExternalNotes: related,
		}
	} else {
		o.R.ClaimExternalNotes = append(o.R.ClaimExternalNotes, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &noteR{
				ClaimExternal: o,
			}
		} else {
			rel.R.ClaimExternal = o
		}
	}
	return nil
}

// AddPostingsFetches adds the given related objects to the existing relationships
// of the claim, optionally inserting them as new records.
// Appends related to o.R.PostingsFetches.
// Sets related.R.Claim appropriately.
func (o *Claim) AddPostingsFetches(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*PostingsFetch) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ClaimID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"nars\".\"postings_fetches\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"claim_id"}),
				strmangle.WhereClause("\"", "\"", 2, postingsFetchPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ClaimID = o.ID
		}
	}

	if o.R == nil {
		o.R = &claimR{
			PostingsFetches: related,
		}
	} else {
		o.R.PostingsFetches = append(o.R.PostingsFetches, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &postingsFetchR{
				Claim: o,
			}
		} else {
			rel.R.Claim = o
		}
	}
	return nil
}

// AddClaimExternalReserveSummaries adds the given related objects to the existing relationships
// of the claim, optionally inserting them as new records.
// Appends related to o.R.ClaimExternalReserveSummaries.
// Sets related.R.ClaimExternal appropriately.
func (o *Claim) AddClaimExternalReserveSummaries(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ReserveSummary) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ClaimExternalID = o.ExternalID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"nars\".\"reserve_summaries\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"claim_external_id"}),
				strmangle.WhereClause("\"", "\"", 2, reserveSummaryPrimaryKeyColumns),
			)
			values := []interface{}{o.ExternalID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ClaimExternalID = o.ExternalID
		}
	}

	if o.R == nil {
		o.R = &claimR{
			ClaimExternalReserveSummaries: related,
		}
	} else {
		o.R.ClaimExternalReserveSummaries = append(o.R.ClaimExternalReserveSummaries, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &reserveSummaryR{
				ClaimExternal: o,
			}
		} else {
			rel.R.ClaimExternal = o
		}
	}
	return nil
}

// AddClaimExternalStatuses adds the given related objects to the existing relationships
// of the claim, optionally inserting them as new records.
// Appends related to o.R.ClaimExternalStatuses.
// Sets related.R.ClaimExternal appropriately.
func (o *Claim) AddClaimExternalStatuses(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Status) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ClaimExternalID = o.ExternalID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"nars\".\"status\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"claim_external_id"}),
				strmangle.WhereClause("\"", "\"", 2, statusPrimaryKeyColumns),
			)
			values := []interface{}{o.ExternalID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ClaimExternalID = o.ExternalID
		}
	}

	if o.R == nil {
		o.R = &claimR{
			ClaimExternalStatuses: related,
		}
	} else {
		o.R.ClaimExternalStatuses = append(o.R.ClaimExternalStatuses, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &statusR{
				ClaimExternal: o,
			}
		} else {
			rel.R.ClaimExternal = o
		}
	}
	return nil
}

// Claims retrieves all the records using an executor.
func Claims(mods ...qm.QueryMod) claimQuery {
	mods = append(mods, qm.From("\"nars\".\"claims\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"nars\".\"claims\".*"})
	}

	return claimQuery{q}
}

// FindClaim retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindClaim(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Claim, error) {
	claimObj := &Claim{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"nars\".\"claims\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, claimObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "nars: unable to select from claims")
	}

	if err = claimObj.doAfterSelectHooks(ctx, exec); err != nil {
		return claimObj, err
	}

	return claimObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Claim) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("nars: no claims provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(claimColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	claimInsertCacheMut.RLock()
	cache, cached := claimInsertCache[key]
	claimInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			claimAllColumns,
			claimColumnsWithDefault,
			claimColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(claimType, claimMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(claimType, claimMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"nars\".\"claims\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"nars\".\"claims\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "nars: unable to insert into claims")
	}

	if !cached {
		claimInsertCacheMut.Lock()
		claimInsertCache[key] = cache
		claimInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Claim.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Claim) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	claimUpdateCacheMut.RLock()
	cache, cached := claimUpdateCache[key]
	claimUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			claimAllColumns,
			claimPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("nars: unable to update claims, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"nars\".\"claims\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, claimPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(claimType, claimMapping, append(wl, claimPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to update claims row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by update for claims")
	}

	if !cached {
		claimUpdateCacheMut.Lock()
		claimUpdateCache[key] = cache
		claimUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q claimQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to update all for claims")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to retrieve rows affected for claims")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ClaimSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("nars: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"nars\".\"claims\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, claimPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to update all in claim slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to retrieve rows affected all in update all claim")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Claim) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("nars: no claims provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(claimColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	claimUpsertCacheMut.RLock()
	cache, cached := claimUpsertCache[key]
	claimUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			claimAllColumns,
			claimColumnsWithDefault,
			claimColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			claimAllColumns,
			claimPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("nars: unable to upsert claims, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(claimPrimaryKeyColumns))
			copy(conflict, claimPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"nars\".\"claims\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(claimType, claimMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(claimType, claimMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "nars: unable to upsert claims")
	}

	if !cached {
		claimUpsertCacheMut.Lock()
		claimUpsertCache[key] = cache
		claimUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Claim record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Claim) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("nars: no Claim provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), claimPrimaryKeyMapping)
	sql := "DELETE FROM \"nars\".\"claims\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to delete from claims")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by delete for claims")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q claimQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("nars: no claimQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to delete all from claims")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by deleteall for claims")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ClaimSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(claimBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"nars\".\"claims\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, claimPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "nars: unable to delete all from claim slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "nars: failed to get rows affected by deleteall for claims")
	}

	if len(claimAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Claim) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindClaim(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ClaimSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ClaimSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"nars\".\"claims\".* FROM \"nars\".\"claims\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, claimPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "nars: unable to reload all in ClaimSlice")
	}

	*o = slice

	return nil
}

// ClaimExists checks if the Claim row exists.
func ClaimExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"nars\".\"claims\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "nars: unable to check if claims exists")
	}

	return exists, nil
}
