// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package endorsementapp

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Request is an object representing the database table.
type Request struct {
	ID                           string            `boil:"id" json:"id" toml:"id" yaml:"id"`
	State                        string            `boil:"state" json:"state" toml:"state" yaml:"state"`
	BaseID                       string            `boil:"base_id" json:"base_id" toml:"base_id" yaml:"base_id"`
	BaseType                     string            `boil:"base_type" json:"base_type" toml:"base_type" yaml:"base_type"`
	ProgramType                  string            `boil:"program_type" json:"program_type" toml:"program_type" yaml:"program_type"`
	IndicationPricingContextIds  types.StringArray `boil:"indication_pricing_context_ids" json:"indication_pricing_context_ids,omitempty" toml:"indication_pricing_context_ids" yaml:"indication_pricing_context_ids,omitempty"`
	BindablePricingContextIds    types.StringArray `boil:"bindable_pricing_context_ids" json:"bindable_pricing_context_ids,omitempty" toml:"bindable_pricing_context_ids" yaml:"bindable_pricing_context_ids,omitempty"`
	ProducerID                   string            `boil:"producer_id" json:"producer_id" toml:"producer_id" yaml:"producer_id"`
	MarketerID                   string            `boil:"marketer_id" json:"marketer_id" toml:"marketer_id" yaml:"marketer_id"`
	CreatedBy                    string            `boil:"created_by" json:"created_by" toml:"created_by" yaml:"created_by"`
	AgencyID                     string            `boil:"agency_id" json:"agency_id" toml:"agency_id" yaml:"agency_id"`
	CreatedAt                    time.Time         `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt                    time.Time         `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	BundleExternalID             string            `boil:"bundle_external_id" json:"bundle_external_id" toml:"bundle_external_id" yaml:"bundle_external_id"`
	DefaultEffectiveDate         null.Time         `boil:"default_effective_date" json:"default_effective_date,omitempty" toml:"default_effective_date" yaml:"default_effective_date,omitempty"`
	ProvisionalEndorsementNumber null.String       `boil:"provisional_endorsement_number" json:"provisional_endorsement_number,omitempty" toml:"provisional_endorsement_number" yaml:"provisional_endorsement_number,omitempty"`
	WrittenPremium               null.JSON         `boil:"written_premium" json:"written_premium,omitempty" toml:"written_premium" yaml:"written_premium,omitempty"`
	BoundAt                      null.Time         `boil:"bound_at" json:"bound_at,omitempty" toml:"bound_at" yaml:"bound_at,omitempty"`
	QuoteGenerationInfo          null.JSON         `boil:"quote_generation_info" json:"quote_generation_info,omitempty" toml:"quote_generation_info" yaml:"quote_generation_info,omitempty"`

	R *requestR `boil:"" json:"" toml:"" yaml:""`
	L requestL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var RequestColumns = struct {
	ID                           string
	State                        string
	BaseID                       string
	BaseType                     string
	ProgramType                  string
	IndicationPricingContextIds  string
	BindablePricingContextIds    string
	ProducerID                   string
	MarketerID                   string
	CreatedBy                    string
	AgencyID                     string
	CreatedAt                    string
	UpdatedAt                    string
	BundleExternalID             string
	DefaultEffectiveDate         string
	ProvisionalEndorsementNumber string
	WrittenPremium               string
	BoundAt                      string
	QuoteGenerationInfo          string
}{
	ID:                           "id",
	State:                        "state",
	BaseID:                       "base_id",
	BaseType:                     "base_type",
	ProgramType:                  "program_type",
	IndicationPricingContextIds:  "indication_pricing_context_ids",
	BindablePricingContextIds:    "bindable_pricing_context_ids",
	ProducerID:                   "producer_id",
	MarketerID:                   "marketer_id",
	CreatedBy:                    "created_by",
	AgencyID:                     "agency_id",
	CreatedAt:                    "created_at",
	UpdatedAt:                    "updated_at",
	BundleExternalID:             "bundle_external_id",
	DefaultEffectiveDate:         "default_effective_date",
	ProvisionalEndorsementNumber: "provisional_endorsement_number",
	WrittenPremium:               "written_premium",
	BoundAt:                      "bound_at",
	QuoteGenerationInfo:          "quote_generation_info",
}

var RequestTableColumns = struct {
	ID                           string
	State                        string
	BaseID                       string
	BaseType                     string
	ProgramType                  string
	IndicationPricingContextIds  string
	BindablePricingContextIds    string
	ProducerID                   string
	MarketerID                   string
	CreatedBy                    string
	AgencyID                     string
	CreatedAt                    string
	UpdatedAt                    string
	BundleExternalID             string
	DefaultEffectiveDate         string
	ProvisionalEndorsementNumber string
	WrittenPremium               string
	BoundAt                      string
	QuoteGenerationInfo          string
}{
	ID:                           "request.id",
	State:                        "request.state",
	BaseID:                       "request.base_id",
	BaseType:                     "request.base_type",
	ProgramType:                  "request.program_type",
	IndicationPricingContextIds:  "request.indication_pricing_context_ids",
	BindablePricingContextIds:    "request.bindable_pricing_context_ids",
	ProducerID:                   "request.producer_id",
	MarketerID:                   "request.marketer_id",
	CreatedBy:                    "request.created_by",
	AgencyID:                     "request.agency_id",
	CreatedAt:                    "request.created_at",
	UpdatedAt:                    "request.updated_at",
	BundleExternalID:             "request.bundle_external_id",
	DefaultEffectiveDate:         "request.default_effective_date",
	ProvisionalEndorsementNumber: "request.provisional_endorsement_number",
	WrittenPremium:               "request.written_premium",
	BoundAt:                      "request.bound_at",
	QuoteGenerationInfo:          "request.quote_generation_info",
}

// Generated where

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var RequestWhere = struct {
	ID                           whereHelperstring
	State                        whereHelperstring
	BaseID                       whereHelperstring
	BaseType                     whereHelperstring
	ProgramType                  whereHelperstring
	IndicationPricingContextIds  whereHelpertypes_StringArray
	BindablePricingContextIds    whereHelpertypes_StringArray
	ProducerID                   whereHelperstring
	MarketerID                   whereHelperstring
	CreatedBy                    whereHelperstring
	AgencyID                     whereHelperstring
	CreatedAt                    whereHelpertime_Time
	UpdatedAt                    whereHelpertime_Time
	BundleExternalID             whereHelperstring
	DefaultEffectiveDate         whereHelpernull_Time
	ProvisionalEndorsementNumber whereHelpernull_String
	WrittenPremium               whereHelpernull_JSON
	BoundAt                      whereHelpernull_Time
	QuoteGenerationInfo          whereHelpernull_JSON
}{
	ID:                           whereHelperstring{field: "\"endorsements\".\"request\".\"id\""},
	State:                        whereHelperstring{field: "\"endorsements\".\"request\".\"state\""},
	BaseID:                       whereHelperstring{field: "\"endorsements\".\"request\".\"base_id\""},
	BaseType:                     whereHelperstring{field: "\"endorsements\".\"request\".\"base_type\""},
	ProgramType:                  whereHelperstring{field: "\"endorsements\".\"request\".\"program_type\""},
	IndicationPricingContextIds:  whereHelpertypes_StringArray{field: "\"endorsements\".\"request\".\"indication_pricing_context_ids\""},
	BindablePricingContextIds:    whereHelpertypes_StringArray{field: "\"endorsements\".\"request\".\"bindable_pricing_context_ids\""},
	ProducerID:                   whereHelperstring{field: "\"endorsements\".\"request\".\"producer_id\""},
	MarketerID:                   whereHelperstring{field: "\"endorsements\".\"request\".\"marketer_id\""},
	CreatedBy:                    whereHelperstring{field: "\"endorsements\".\"request\".\"created_by\""},
	AgencyID:                     whereHelperstring{field: "\"endorsements\".\"request\".\"agency_id\""},
	CreatedAt:                    whereHelpertime_Time{field: "\"endorsements\".\"request\".\"created_at\""},
	UpdatedAt:                    whereHelpertime_Time{field: "\"endorsements\".\"request\".\"updated_at\""},
	BundleExternalID:             whereHelperstring{field: "\"endorsements\".\"request\".\"bundle_external_id\""},
	DefaultEffectiveDate:         whereHelpernull_Time{field: "\"endorsements\".\"request\".\"default_effective_date\""},
	ProvisionalEndorsementNumber: whereHelpernull_String{field: "\"endorsements\".\"request\".\"provisional_endorsement_number\""},
	WrittenPremium:               whereHelpernull_JSON{field: "\"endorsements\".\"request\".\"written_premium\""},
	BoundAt:                      whereHelpernull_Time{field: "\"endorsements\".\"request\".\"bound_at\""},
	QuoteGenerationInfo:          whereHelpernull_JSON{field: "\"endorsements\".\"request\".\"quote_generation_info\""},
}

// RequestRels is where relationship names are stored.
var RequestRels = struct {
	Changes string
	Reviews string
}{
	Changes: "Changes",
	Reviews: "Reviews",
}

// requestR is where relationships are stored.
type requestR struct {
	Changes ChangeSlice `boil:"Changes" json:"Changes" toml:"Changes" yaml:"Changes"`
	Reviews ReviewSlice `boil:"Reviews" json:"Reviews" toml:"Reviews" yaml:"Reviews"`
}

// NewStruct creates a new relationship struct
func (*requestR) NewStruct() *requestR {
	return &requestR{}
}

// requestL is where Load methods for each relationship are stored.
type requestL struct{}

var (
	requestAllColumns            = []string{"id", "state", "base_id", "base_type", "program_type", "indication_pricing_context_ids", "bindable_pricing_context_ids", "producer_id", "marketer_id", "created_by", "agency_id", "created_at", "updated_at", "bundle_external_id", "default_effective_date", "provisional_endorsement_number", "written_premium", "bound_at", "quote_generation_info"}
	requestColumnsWithoutDefault = []string{"id", "state", "base_id", "base_type", "program_type", "producer_id", "marketer_id", "created_by", "agency_id", "updated_at"}
	requestColumnsWithDefault    = []string{"indication_pricing_context_ids", "bindable_pricing_context_ids", "created_at", "bundle_external_id", "default_effective_date", "provisional_endorsement_number", "written_premium", "bound_at", "quote_generation_info"}
	requestPrimaryKeyColumns     = []string{"id"}
	requestGeneratedColumns      = []string{}
)

type (
	// RequestSlice is an alias for a slice of pointers to Request.
	// This should almost always be used instead of []Request.
	RequestSlice []*Request
	// RequestHook is the signature for custom Request hook methods
	RequestHook func(context.Context, boil.ContextExecutor, *Request) error

	requestQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	requestType                 = reflect.TypeOf(&Request{})
	requestMapping              = queries.MakeStructMapping(requestType)
	requestPrimaryKeyMapping, _ = queries.BindMapping(requestType, requestMapping, requestPrimaryKeyColumns)
	requestInsertCacheMut       sync.RWMutex
	requestInsertCache          = make(map[string]insertCache)
	requestUpdateCacheMut       sync.RWMutex
	requestUpdateCache          = make(map[string]updateCache)
	requestUpsertCacheMut       sync.RWMutex
	requestUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var requestAfterSelectHooks []RequestHook

var requestBeforeInsertHooks []RequestHook
var requestAfterInsertHooks []RequestHook

var requestBeforeUpdateHooks []RequestHook
var requestAfterUpdateHooks []RequestHook

var requestBeforeDeleteHooks []RequestHook
var requestAfterDeleteHooks []RequestHook

var requestBeforeUpsertHooks []RequestHook
var requestAfterUpsertHooks []RequestHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Request) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Request) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Request) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Request) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Request) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Request) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Request) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Request) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Request) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddRequestHook registers your hook function for all future operations.
func AddRequestHook(hookPoint boil.HookPoint, requestHook RequestHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		requestAfterSelectHooks = append(requestAfterSelectHooks, requestHook)
	case boil.BeforeInsertHook:
		requestBeforeInsertHooks = append(requestBeforeInsertHooks, requestHook)
	case boil.AfterInsertHook:
		requestAfterInsertHooks = append(requestAfterInsertHooks, requestHook)
	case boil.BeforeUpdateHook:
		requestBeforeUpdateHooks = append(requestBeforeUpdateHooks, requestHook)
	case boil.AfterUpdateHook:
		requestAfterUpdateHooks = append(requestAfterUpdateHooks, requestHook)
	case boil.BeforeDeleteHook:
		requestBeforeDeleteHooks = append(requestBeforeDeleteHooks, requestHook)
	case boil.AfterDeleteHook:
		requestAfterDeleteHooks = append(requestAfterDeleteHooks, requestHook)
	case boil.BeforeUpsertHook:
		requestBeforeUpsertHooks = append(requestBeforeUpsertHooks, requestHook)
	case boil.AfterUpsertHook:
		requestAfterUpsertHooks = append(requestAfterUpsertHooks, requestHook)
	}
}

// One returns a single request record from the query.
func (q requestQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Request, error) {
	o := &Request{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: failed to execute a one query for request")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Request records from the query.
func (q requestQuery) All(ctx context.Context, exec boil.ContextExecutor) (RequestSlice, error) {
	var o []*Request

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "endorsementapp: failed to assign all query results to Request slice")
	}

	if len(requestAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Request records in the query.
func (q requestQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to count request rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q requestQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: failed to check if request exists")
	}

	return count > 0, nil
}

// Changes retrieves all the change's Changes with an executor.
func (o *Request) Changes(mods ...qm.QueryMod) changeQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"endorsements\".\"changes\".\"request_id\"=?", o.ID),
	)

	return Changes(queryMods...)
}

// Reviews retrieves all the review's Reviews with an executor.
func (o *Request) Reviews(mods ...qm.QueryMod) reviewQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"endorsements\".\"review\".\"request_id\"=?", o.ID),
	)

	return Reviews(queryMods...)
}

// LoadChanges allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (requestL) LoadChanges(ctx context.Context, e boil.ContextExecutor, singular bool, maybeRequest interface{}, mods queries.Applicator) error {
	var slice []*Request
	var object *Request

	if singular {
		object = maybeRequest.(*Request)
	} else {
		slice = *maybeRequest.(*[]*Request)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &requestR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &requestR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.changes`),
		qm.WhereIn(`endorsements.changes.request_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load changes")
	}

	var resultSlice []*Change
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice changes")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on changes")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for changes")
	}

	if len(changeAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Changes = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &changeR{}
			}
			foreign.R.Request = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.RequestID {
				local.R.Changes = append(local.R.Changes, foreign)
				if foreign.R == nil {
					foreign.R = &changeR{}
				}
				foreign.R.Request = local
				break
			}
		}
	}

	return nil
}

// LoadReviews allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (requestL) LoadReviews(ctx context.Context, e boil.ContextExecutor, singular bool, maybeRequest interface{}, mods queries.Applicator) error {
	var slice []*Request
	var object *Request

	if singular {
		object = maybeRequest.(*Request)
	} else {
		slice = *maybeRequest.(*[]*Request)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &requestR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &requestR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.review`),
		qm.WhereIn(`endorsements.review.request_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load review")
	}

	var resultSlice []*Review
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice review")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on review")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for review")
	}

	if len(reviewAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Reviews = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &reviewR{}
			}
			foreign.R.Request = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.RequestID {
				local.R.Reviews = append(local.R.Reviews, foreign)
				if foreign.R == nil {
					foreign.R = &reviewR{}
				}
				foreign.R.Request = local
				break
			}
		}
	}

	return nil
}

// AddChanges adds the given related objects to the existing relationships
// of the request, optionally inserting them as new records.
// Appends related to o.R.Changes.
// Sets related.R.Request appropriately.
func (o *Request) AddChanges(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Change) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.RequestID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"endorsements\".\"changes\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"request_id"}),
				strmangle.WhereClause("\"", "\"", 2, changePrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.RequestID = o.ID
		}
	}

	if o.R == nil {
		o.R = &requestR{
			Changes: related,
		}
	} else {
		o.R.Changes = append(o.R.Changes, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &changeR{
				Request: o,
			}
		} else {
			rel.R.Request = o
		}
	}
	return nil
}

// AddReviews adds the given related objects to the existing relationships
// of the request, optionally inserting them as new records.
// Appends related to o.R.Reviews.
// Sets related.R.Request appropriately.
func (o *Request) AddReviews(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Review) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.RequestID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"endorsements\".\"review\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"request_id"}),
				strmangle.WhereClause("\"", "\"", 2, reviewPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.RequestID = o.ID
		}
	}

	if o.R == nil {
		o.R = &requestR{
			Reviews: related,
		}
	} else {
		o.R.Reviews = append(o.R.Reviews, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &reviewR{
				Request: o,
			}
		} else {
			rel.R.Request = o
		}
	}
	return nil
}

// Requests retrieves all the records using an executor.
func Requests(mods ...qm.QueryMod) requestQuery {
	mods = append(mods, qm.From("\"endorsements\".\"request\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"endorsements\".\"request\".*"})
	}

	return requestQuery{q}
}

// FindRequest retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindRequest(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Request, error) {
	requestObj := &Request{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"endorsements\".\"request\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, requestObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: unable to select from request")
	}

	if err = requestObj.doAfterSelectHooks(ctx, exec); err != nil {
		return requestObj, err
	}

	return requestObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Request) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no request provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(requestColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	requestInsertCacheMut.RLock()
	cache, cached := requestInsertCache[key]
	requestInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			requestAllColumns,
			requestColumnsWithDefault,
			requestColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(requestType, requestMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(requestType, requestMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"endorsements\".\"request\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"endorsements\".\"request\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to insert into request")
	}

	if !cached {
		requestInsertCacheMut.Lock()
		requestInsertCache[key] = cache
		requestInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Request.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Request) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	requestUpdateCacheMut.RLock()
	cache, cached := requestUpdateCache[key]
	requestUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			requestAllColumns,
			requestPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("endorsementapp: unable to update request, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"endorsements\".\"request\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, requestPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(requestType, requestMapping, append(wl, requestPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update request row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by update for request")
	}

	if !cached {
		requestUpdateCacheMut.Lock()
		requestUpdateCache[key] = cache
		requestUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q requestQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all for request")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected for request")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o RequestSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("endorsementapp: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), requestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"endorsements\".\"request\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, requestPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all in request slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected all in update all request")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Request) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no request provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(requestColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	requestUpsertCacheMut.RLock()
	cache, cached := requestUpsertCache[key]
	requestUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			requestAllColumns,
			requestColumnsWithDefault,
			requestColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			requestAllColumns,
			requestPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("endorsementapp: unable to upsert request, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(requestPrimaryKeyColumns))
			copy(conflict, requestPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"endorsements\".\"request\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(requestType, requestMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(requestType, requestMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to upsert request")
	}

	if !cached {
		requestUpsertCacheMut.Lock()
		requestUpsertCache[key] = cache
		requestUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Request record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Request) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("endorsementapp: no Request provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), requestPrimaryKeyMapping)
	sql := "DELETE FROM \"endorsements\".\"request\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete from request")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by delete for request")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q requestQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("endorsementapp: no requestQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from request")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for request")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o RequestSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(requestBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), requestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"endorsements\".\"request\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, requestPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from request slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for request")
	}

	if len(requestAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Request) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindRequest(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *RequestSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := RequestSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), requestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"endorsements\".\"request\".* FROM \"endorsements\".\"request\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, requestPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to reload all in RequestSlice")
	}

	*o = slice

	return nil
}

// RequestExists checks if the Request row exists.
func RequestExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"endorsements\".\"request\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: unable to check if request exists")
	}

	return exists, nil
}
