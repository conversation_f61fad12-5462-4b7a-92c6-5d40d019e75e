// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package claims

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Fnol is an object representing the database table.
type Fnol struct {
	ID                  string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ClientClaimNumber   null.String `boil:"client_claim_number" json:"client_claim_number,omitempty" toml:"client_claim_number" yaml:"client_claim_number,omitempty"`
	LossDatetime        null.Time   `boil:"loss_datetime" json:"loss_datetime,omitempty" toml:"loss_datetime" yaml:"loss_datetime,omitempty"`
	LossLocation        null.String `boil:"loss_location" json:"loss_location,omitempty" toml:"loss_location" yaml:"loss_location,omitempty"`
	LossState           null.String `boil:"loss_state" json:"loss_state,omitempty" toml:"loss_state" yaml:"loss_state,omitempty"`
	NoticeType          null.String `boil:"notice_type" json:"notice_type,omitempty" toml:"notice_type" yaml:"notice_type,omitempty"`
	PoliceAgencyName    null.String `boil:"police_agency_name" json:"police_agency_name,omitempty" toml:"police_agency_name" yaml:"police_agency_name,omitempty"`
	PoliceReportNumber  null.String `boil:"police_report_number" json:"police_report_number,omitempty" toml:"police_report_number" yaml:"police_report_number,omitempty"`
	IncidentDescription null.String `boil:"incident_description" json:"incident_description,omitempty" toml:"incident_description" yaml:"incident_description,omitempty"`
	PolicyNumber        null.String `boil:"policy_number" json:"policy_number,omitempty" toml:"policy_number" yaml:"policy_number,omitempty"`
	InjuriesInvolved    null.Bool   `boil:"injuries_involved" json:"injuries_involved,omitempty" toml:"injuries_involved" yaml:"injuries_involved,omitempty"`
	Status              string      `boil:"status" json:"status" toml:"status" yaml:"status"`
	SubmittedFrom       string      `boil:"submitted_from" json:"submitted_from" toml:"submitted_from" yaml:"submitted_from"`
	Source              string      `boil:"source" json:"source" toml:"source" yaml:"source"`
	CreatedBy           string      `boil:"created_by" json:"created_by" toml:"created_by" yaml:"created_by"`
	CreatedAt           time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	ArchivedAt          null.Time   `boil:"archived_at" json:"archived_at,omitempty" toml:"archived_at" yaml:"archived_at,omitempty"`
	UpdatedAt           time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	Original            null.JSON   `boil:"original" json:"original,omitempty" toml:"original" yaml:"original,omitempty"`
	InsuredName         null.String `boil:"insured_name" json:"insured_name,omitempty" toml:"insured_name" yaml:"insured_name,omitempty"`

	R *fnolR `boil:"" json:"" toml:"" yaml:""`
	L fnolL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var FnolColumns = struct {
	ID                  string
	ClientClaimNumber   string
	LossDatetime        string
	LossLocation        string
	LossState           string
	NoticeType          string
	PoliceAgencyName    string
	PoliceReportNumber  string
	IncidentDescription string
	PolicyNumber        string
	InjuriesInvolved    string
	Status              string
	SubmittedFrom       string
	Source              string
	CreatedBy           string
	CreatedAt           string
	ArchivedAt          string
	UpdatedAt           string
	Original            string
	InsuredName         string
}{
	ID:                  "id",
	ClientClaimNumber:   "client_claim_number",
	LossDatetime:        "loss_datetime",
	LossLocation:        "loss_location",
	LossState:           "loss_state",
	NoticeType:          "notice_type",
	PoliceAgencyName:    "police_agency_name",
	PoliceReportNumber:  "police_report_number",
	IncidentDescription: "incident_description",
	PolicyNumber:        "policy_number",
	InjuriesInvolved:    "injuries_involved",
	Status:              "status",
	SubmittedFrom:       "submitted_from",
	Source:              "source",
	CreatedBy:           "created_by",
	CreatedAt:           "created_at",
	ArchivedAt:          "archived_at",
	UpdatedAt:           "updated_at",
	Original:            "original",
	InsuredName:         "insured_name",
}

var FnolTableColumns = struct {
	ID                  string
	ClientClaimNumber   string
	LossDatetime        string
	LossLocation        string
	LossState           string
	NoticeType          string
	PoliceAgencyName    string
	PoliceReportNumber  string
	IncidentDescription string
	PolicyNumber        string
	InjuriesInvolved    string
	Status              string
	SubmittedFrom       string
	Source              string
	CreatedBy           string
	CreatedAt           string
	ArchivedAt          string
	UpdatedAt           string
	Original            string
	InsuredName         string
}{
	ID:                  "fnols.id",
	ClientClaimNumber:   "fnols.client_claim_number",
	LossDatetime:        "fnols.loss_datetime",
	LossLocation:        "fnols.loss_location",
	LossState:           "fnols.loss_state",
	NoticeType:          "fnols.notice_type",
	PoliceAgencyName:    "fnols.police_agency_name",
	PoliceReportNumber:  "fnols.police_report_number",
	IncidentDescription: "fnols.incident_description",
	PolicyNumber:        "fnols.policy_number",
	InjuriesInvolved:    "fnols.injuries_involved",
	Status:              "fnols.status",
	SubmittedFrom:       "fnols.submitted_from",
	Source:              "fnols.source",
	CreatedBy:           "fnols.created_by",
	CreatedAt:           "fnols.created_at",
	ArchivedAt:          "fnols.archived_at",
	UpdatedAt:           "fnols.updated_at",
	Original:            "fnols.original",
	InsuredName:         "fnols.insured_name",
}

// Generated where

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_Bool struct{ field string }

func (w whereHelpernull_Bool) EQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Bool) NEQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Bool) LT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Bool) LTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Bool) GT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Bool) GTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Bool) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Bool) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var FnolWhere = struct {
	ID                  whereHelperstring
	ClientClaimNumber   whereHelpernull_String
	LossDatetime        whereHelpernull_Time
	LossLocation        whereHelpernull_String
	LossState           whereHelpernull_String
	NoticeType          whereHelpernull_String
	PoliceAgencyName    whereHelpernull_String
	PoliceReportNumber  whereHelpernull_String
	IncidentDescription whereHelpernull_String
	PolicyNumber        whereHelpernull_String
	InjuriesInvolved    whereHelpernull_Bool
	Status              whereHelperstring
	SubmittedFrom       whereHelperstring
	Source              whereHelperstring
	CreatedBy           whereHelperstring
	CreatedAt           whereHelpertime_Time
	ArchivedAt          whereHelpernull_Time
	UpdatedAt           whereHelpertime_Time
	Original            whereHelpernull_JSON
	InsuredName         whereHelpernull_String
}{
	ID:                  whereHelperstring{field: "\"claims\".\"fnols\".\"id\""},
	ClientClaimNumber:   whereHelpernull_String{field: "\"claims\".\"fnols\".\"client_claim_number\""},
	LossDatetime:        whereHelpernull_Time{field: "\"claims\".\"fnols\".\"loss_datetime\""},
	LossLocation:        whereHelpernull_String{field: "\"claims\".\"fnols\".\"loss_location\""},
	LossState:           whereHelpernull_String{field: "\"claims\".\"fnols\".\"loss_state\""},
	NoticeType:          whereHelpernull_String{field: "\"claims\".\"fnols\".\"notice_type\""},
	PoliceAgencyName:    whereHelpernull_String{field: "\"claims\".\"fnols\".\"police_agency_name\""},
	PoliceReportNumber:  whereHelpernull_String{field: "\"claims\".\"fnols\".\"police_report_number\""},
	IncidentDescription: whereHelpernull_String{field: "\"claims\".\"fnols\".\"incident_description\""},
	PolicyNumber:        whereHelpernull_String{field: "\"claims\".\"fnols\".\"policy_number\""},
	InjuriesInvolved:    whereHelpernull_Bool{field: "\"claims\".\"fnols\".\"injuries_involved\""},
	Status:              whereHelperstring{field: "\"claims\".\"fnols\".\"status\""},
	SubmittedFrom:       whereHelperstring{field: "\"claims\".\"fnols\".\"submitted_from\""},
	Source:              whereHelperstring{field: "\"claims\".\"fnols\".\"source\""},
	CreatedBy:           whereHelperstring{field: "\"claims\".\"fnols\".\"created_by\""},
	CreatedAt:           whereHelpertime_Time{field: "\"claims\".\"fnols\".\"created_at\""},
	ArchivedAt:          whereHelpernull_Time{field: "\"claims\".\"fnols\".\"archived_at\""},
	UpdatedAt:           whereHelpertime_Time{field: "\"claims\".\"fnols\".\"updated_at\""},
	Original:            whereHelpernull_JSON{field: "\"claims\".\"fnols\".\"original\""},
	InsuredName:         whereHelpernull_String{field: "\"claims\".\"fnols\".\"insured_name\""},
}

// FnolRels is where relationship names are stored.
var FnolRels = struct {
	FnolAttachments  string
	FnolContacts     string
	FnolIntakeEmails string
	FnolVehicles     string
}{
	FnolAttachments:  "FnolAttachments",
	FnolContacts:     "FnolContacts",
	FnolIntakeEmails: "FnolIntakeEmails",
	FnolVehicles:     "FnolVehicles",
}

// fnolR is where relationships are stored.
type fnolR struct {
	FnolAttachments  FnolAttachmentSlice  `boil:"FnolAttachments" json:"FnolAttachments" toml:"FnolAttachments" yaml:"FnolAttachments"`
	FnolContacts     FnolContactSlice     `boil:"FnolContacts" json:"FnolContacts" toml:"FnolContacts" yaml:"FnolContacts"`
	FnolIntakeEmails FnolIntakeEmailSlice `boil:"FnolIntakeEmails" json:"FnolIntakeEmails" toml:"FnolIntakeEmails" yaml:"FnolIntakeEmails"`
	FnolVehicles     FnolVehicleSlice     `boil:"FnolVehicles" json:"FnolVehicles" toml:"FnolVehicles" yaml:"FnolVehicles"`
}

// NewStruct creates a new relationship struct
func (*fnolR) NewStruct() *fnolR {
	return &fnolR{}
}

// fnolL is where Load methods for each relationship are stored.
type fnolL struct{}

var (
	fnolAllColumns            = []string{"id", "client_claim_number", "loss_datetime", "loss_location", "loss_state", "notice_type", "police_agency_name", "police_report_number", "incident_description", "policy_number", "injuries_involved", "status", "submitted_from", "source", "created_by", "created_at", "archived_at", "updated_at", "original", "insured_name"}
	fnolColumnsWithoutDefault = []string{"id", "status", "submitted_from", "source", "created_by"}
	fnolColumnsWithDefault    = []string{"client_claim_number", "loss_datetime", "loss_location", "loss_state", "notice_type", "police_agency_name", "police_report_number", "incident_description", "policy_number", "injuries_involved", "created_at", "archived_at", "updated_at", "original", "insured_name"}
	fnolPrimaryKeyColumns     = []string{"id"}
	fnolGeneratedColumns      = []string{}
)

type (
	// FnolSlice is an alias for a slice of pointers to Fnol.
	// This should almost always be used instead of []Fnol.
	FnolSlice []*Fnol
	// FnolHook is the signature for custom Fnol hook methods
	FnolHook func(context.Context, boil.ContextExecutor, *Fnol) error

	fnolQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	fnolType                 = reflect.TypeOf(&Fnol{})
	fnolMapping              = queries.MakeStructMapping(fnolType)
	fnolPrimaryKeyMapping, _ = queries.BindMapping(fnolType, fnolMapping, fnolPrimaryKeyColumns)
	fnolInsertCacheMut       sync.RWMutex
	fnolInsertCache          = make(map[string]insertCache)
	fnolUpdateCacheMut       sync.RWMutex
	fnolUpdateCache          = make(map[string]updateCache)
	fnolUpsertCacheMut       sync.RWMutex
	fnolUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var fnolAfterSelectHooks []FnolHook

var fnolBeforeInsertHooks []FnolHook
var fnolAfterInsertHooks []FnolHook

var fnolBeforeUpdateHooks []FnolHook
var fnolAfterUpdateHooks []FnolHook

var fnolBeforeDeleteHooks []FnolHook
var fnolAfterDeleteHooks []FnolHook

var fnolBeforeUpsertHooks []FnolHook
var fnolAfterUpsertHooks []FnolHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Fnol) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Fnol) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Fnol) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Fnol) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Fnol) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Fnol) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Fnol) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Fnol) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Fnol) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddFnolHook registers your hook function for all future operations.
func AddFnolHook(hookPoint boil.HookPoint, fnolHook FnolHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		fnolAfterSelectHooks = append(fnolAfterSelectHooks, fnolHook)
	case boil.BeforeInsertHook:
		fnolBeforeInsertHooks = append(fnolBeforeInsertHooks, fnolHook)
	case boil.AfterInsertHook:
		fnolAfterInsertHooks = append(fnolAfterInsertHooks, fnolHook)
	case boil.BeforeUpdateHook:
		fnolBeforeUpdateHooks = append(fnolBeforeUpdateHooks, fnolHook)
	case boil.AfterUpdateHook:
		fnolAfterUpdateHooks = append(fnolAfterUpdateHooks, fnolHook)
	case boil.BeforeDeleteHook:
		fnolBeforeDeleteHooks = append(fnolBeforeDeleteHooks, fnolHook)
	case boil.AfterDeleteHook:
		fnolAfterDeleteHooks = append(fnolAfterDeleteHooks, fnolHook)
	case boil.BeforeUpsertHook:
		fnolBeforeUpsertHooks = append(fnolBeforeUpsertHooks, fnolHook)
	case boil.AfterUpsertHook:
		fnolAfterUpsertHooks = append(fnolAfterUpsertHooks, fnolHook)
	}
}

// One returns a single fnol record from the query.
func (q fnolQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Fnol, error) {
	o := &Fnol{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "claims: failed to execute a one query for fnols")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Fnol records from the query.
func (q fnolQuery) All(ctx context.Context, exec boil.ContextExecutor) (FnolSlice, error) {
	var o []*Fnol

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "claims: failed to assign all query results to Fnol slice")
	}

	if len(fnolAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Fnol records in the query.
func (q fnolQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to count fnols rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q fnolQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "claims: failed to check if fnols exists")
	}

	return count > 0, nil
}

// FnolAttachments retrieves all the fnol_attachment's FnolAttachments with an executor.
func (o *Fnol) FnolAttachments(mods ...qm.QueryMod) fnolAttachmentQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"claims\".\"fnol_attachments\".\"fnol_id\"=?", o.ID),
	)

	return FnolAttachments(queryMods...)
}

// FnolContacts retrieves all the fnol_contact's FnolContacts with an executor.
func (o *Fnol) FnolContacts(mods ...qm.QueryMod) fnolContactQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"claims\".\"fnol_contacts\".\"fnol_id\"=?", o.ID),
	)

	return FnolContacts(queryMods...)
}

// FnolIntakeEmails retrieves all the fnol_intake_email's FnolIntakeEmails with an executor.
func (o *Fnol) FnolIntakeEmails(mods ...qm.QueryMod) fnolIntakeEmailQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"claims\".\"fnol_intake_emails\".\"fnol_id\"=?", o.ID),
	)

	return FnolIntakeEmails(queryMods...)
}

// FnolVehicles retrieves all the fnol_vehicle's FnolVehicles with an executor.
func (o *Fnol) FnolVehicles(mods ...qm.QueryMod) fnolVehicleQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"claims\".\"fnol_vehicles\".\"fnol_id\"=?", o.ID),
	)

	return FnolVehicles(queryMods...)
}

// LoadFnolAttachments allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (fnolL) LoadFnolAttachments(ctx context.Context, e boil.ContextExecutor, singular bool, maybeFnol interface{}, mods queries.Applicator) error {
	var slice []*Fnol
	var object *Fnol

	if singular {
		object = maybeFnol.(*Fnol)
	} else {
		slice = *maybeFnol.(*[]*Fnol)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &fnolR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &fnolR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`claims.fnol_attachments`),
		qm.WhereIn(`claims.fnol_attachments.fnol_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load fnol_attachments")
	}

	var resultSlice []*FnolAttachment
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice fnol_attachments")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on fnol_attachments")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for fnol_attachments")
	}

	if len(fnolAttachmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.FnolAttachments = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &fnolAttachmentR{}
			}
			foreign.R.Fnol = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.FnolID {
				local.R.FnolAttachments = append(local.R.FnolAttachments, foreign)
				if foreign.R == nil {
					foreign.R = &fnolAttachmentR{}
				}
				foreign.R.Fnol = local
				break
			}
		}
	}

	return nil
}

// LoadFnolContacts allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (fnolL) LoadFnolContacts(ctx context.Context, e boil.ContextExecutor, singular bool, maybeFnol interface{}, mods queries.Applicator) error {
	var slice []*Fnol
	var object *Fnol

	if singular {
		object = maybeFnol.(*Fnol)
	} else {
		slice = *maybeFnol.(*[]*Fnol)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &fnolR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &fnolR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`claims.fnol_contacts`),
		qm.WhereIn(`claims.fnol_contacts.fnol_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load fnol_contacts")
	}

	var resultSlice []*FnolContact
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice fnol_contacts")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on fnol_contacts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for fnol_contacts")
	}

	if len(fnolContactAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.FnolContacts = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &fnolContactR{}
			}
			foreign.R.Fnol = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.FnolID {
				local.R.FnolContacts = append(local.R.FnolContacts, foreign)
				if foreign.R == nil {
					foreign.R = &fnolContactR{}
				}
				foreign.R.Fnol = local
				break
			}
		}
	}

	return nil
}

// LoadFnolIntakeEmails allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (fnolL) LoadFnolIntakeEmails(ctx context.Context, e boil.ContextExecutor, singular bool, maybeFnol interface{}, mods queries.Applicator) error {
	var slice []*Fnol
	var object *Fnol

	if singular {
		object = maybeFnol.(*Fnol)
	} else {
		slice = *maybeFnol.(*[]*Fnol)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &fnolR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &fnolR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`claims.fnol_intake_emails`),
		qm.WhereIn(`claims.fnol_intake_emails.fnol_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load fnol_intake_emails")
	}

	var resultSlice []*FnolIntakeEmail
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice fnol_intake_emails")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on fnol_intake_emails")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for fnol_intake_emails")
	}

	if len(fnolIntakeEmailAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.FnolIntakeEmails = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &fnolIntakeEmailR{}
			}
			foreign.R.Fnol = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.FnolID {
				local.R.FnolIntakeEmails = append(local.R.FnolIntakeEmails, foreign)
				if foreign.R == nil {
					foreign.R = &fnolIntakeEmailR{}
				}
				foreign.R.Fnol = local
				break
			}
		}
	}

	return nil
}

// LoadFnolVehicles allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (fnolL) LoadFnolVehicles(ctx context.Context, e boil.ContextExecutor, singular bool, maybeFnol interface{}, mods queries.Applicator) error {
	var slice []*Fnol
	var object *Fnol

	if singular {
		object = maybeFnol.(*Fnol)
	} else {
		slice = *maybeFnol.(*[]*Fnol)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &fnolR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &fnolR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`claims.fnol_vehicles`),
		qm.WhereIn(`claims.fnol_vehicles.fnol_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load fnol_vehicles")
	}

	var resultSlice []*FnolVehicle
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice fnol_vehicles")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on fnol_vehicles")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for fnol_vehicles")
	}

	if len(fnolVehicleAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.FnolVehicles = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &fnolVehicleR{}
			}
			foreign.R.Fnol = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.FnolID {
				local.R.FnolVehicles = append(local.R.FnolVehicles, foreign)
				if foreign.R == nil {
					foreign.R = &fnolVehicleR{}
				}
				foreign.R.Fnol = local
				break
			}
		}
	}

	return nil
}

// AddFnolAttachments adds the given related objects to the existing relationships
// of the fnol, optionally inserting them as new records.
// Appends related to o.R.FnolAttachments.
// Sets related.R.Fnol appropriately.
func (o *Fnol) AddFnolAttachments(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*FnolAttachment) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.FnolID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"claims\".\"fnol_attachments\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"fnol_id"}),
				strmangle.WhereClause("\"", "\"", 2, fnolAttachmentPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.FnolID = o.ID
		}
	}

	if o.R == nil {
		o.R = &fnolR{
			FnolAttachments: related,
		}
	} else {
		o.R.FnolAttachments = append(o.R.FnolAttachments, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &fnolAttachmentR{
				Fnol: o,
			}
		} else {
			rel.R.Fnol = o
		}
	}
	return nil
}

// AddFnolContacts adds the given related objects to the existing relationships
// of the fnol, optionally inserting them as new records.
// Appends related to o.R.FnolContacts.
// Sets related.R.Fnol appropriately.
func (o *Fnol) AddFnolContacts(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*FnolContact) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.FnolID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"claims\".\"fnol_contacts\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"fnol_id"}),
				strmangle.WhereClause("\"", "\"", 2, fnolContactPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.FnolID = o.ID
		}
	}

	if o.R == nil {
		o.R = &fnolR{
			FnolContacts: related,
		}
	} else {
		o.R.FnolContacts = append(o.R.FnolContacts, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &fnolContactR{
				Fnol: o,
			}
		} else {
			rel.R.Fnol = o
		}
	}
	return nil
}

// AddFnolIntakeEmails adds the given related objects to the existing relationships
// of the fnol, optionally inserting them as new records.
// Appends related to o.R.FnolIntakeEmails.
// Sets related.R.Fnol appropriately.
func (o *Fnol) AddFnolIntakeEmails(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*FnolIntakeEmail) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.FnolID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"claims\".\"fnol_intake_emails\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"fnol_id"}),
				strmangle.WhereClause("\"", "\"", 2, fnolIntakeEmailPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.FnolID = o.ID
		}
	}

	if o.R == nil {
		o.R = &fnolR{
			FnolIntakeEmails: related,
		}
	} else {
		o.R.FnolIntakeEmails = append(o.R.FnolIntakeEmails, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &fnolIntakeEmailR{
				Fnol: o,
			}
		} else {
			rel.R.Fnol = o
		}
	}
	return nil
}

// AddFnolVehicles adds the given related objects to the existing relationships
// of the fnol, optionally inserting them as new records.
// Appends related to o.R.FnolVehicles.
// Sets related.R.Fnol appropriately.
func (o *Fnol) AddFnolVehicles(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*FnolVehicle) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.FnolID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"claims\".\"fnol_vehicles\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"fnol_id"}),
				strmangle.WhereClause("\"", "\"", 2, fnolVehiclePrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.FnolID = o.ID
		}
	}

	if o.R == nil {
		o.R = &fnolR{
			FnolVehicles: related,
		}
	} else {
		o.R.FnolVehicles = append(o.R.FnolVehicles, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &fnolVehicleR{
				Fnol: o,
			}
		} else {
			rel.R.Fnol = o
		}
	}
	return nil
}

// Fnols retrieves all the records using an executor.
func Fnols(mods ...qm.QueryMod) fnolQuery {
	mods = append(mods, qm.From("\"claims\".\"fnols\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"claims\".\"fnols\".*"})
	}

	return fnolQuery{q}
}

// FindFnol retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindFnol(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Fnol, error) {
	fnolObj := &Fnol{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"claims\".\"fnols\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, fnolObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "claims: unable to select from fnols")
	}

	if err = fnolObj.doAfterSelectHooks(ctx, exec); err != nil {
		return fnolObj, err
	}

	return fnolObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Fnol) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("claims: no fnols provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fnolColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	fnolInsertCacheMut.RLock()
	cache, cached := fnolInsertCache[key]
	fnolInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			fnolAllColumns,
			fnolColumnsWithDefault,
			fnolColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(fnolType, fnolMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(fnolType, fnolMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"claims\".\"fnols\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"claims\".\"fnols\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "claims: unable to insert into fnols")
	}

	if !cached {
		fnolInsertCacheMut.Lock()
		fnolInsertCache[key] = cache
		fnolInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Fnol.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Fnol) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	fnolUpdateCacheMut.RLock()
	cache, cached := fnolUpdateCache[key]
	fnolUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			fnolAllColumns,
			fnolPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("claims: unable to update fnols, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"claims\".\"fnols\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, fnolPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(fnolType, fnolMapping, append(wl, fnolPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update fnols row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by update for fnols")
	}

	if !cached {
		fnolUpdateCacheMut.Lock()
		fnolUpdateCache[key] = cache
		fnolUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q fnolQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update all for fnols")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to retrieve rows affected for fnols")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o FnolSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("claims: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fnolPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"claims\".\"fnols\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, fnolPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update all in fnol slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to retrieve rows affected all in update all fnol")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Fnol) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("claims: no fnols provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fnolColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	fnolUpsertCacheMut.RLock()
	cache, cached := fnolUpsertCache[key]
	fnolUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			fnolAllColumns,
			fnolColumnsWithDefault,
			fnolColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			fnolAllColumns,
			fnolPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("claims: unable to upsert fnols, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(fnolPrimaryKeyColumns))
			copy(conflict, fnolPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"claims\".\"fnols\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(fnolType, fnolMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(fnolType, fnolMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "claims: unable to upsert fnols")
	}

	if !cached {
		fnolUpsertCacheMut.Lock()
		fnolUpsertCache[key] = cache
		fnolUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Fnol record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Fnol) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("claims: no Fnol provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), fnolPrimaryKeyMapping)
	sql := "DELETE FROM \"claims\".\"fnols\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete from fnols")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by delete for fnols")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q fnolQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("claims: no fnolQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete all from fnols")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by deleteall for fnols")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o FnolSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(fnolBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fnolPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"claims\".\"fnols\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fnolPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete all from fnol slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by deleteall for fnols")
	}

	if len(fnolAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Fnol) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindFnol(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *FnolSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := FnolSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fnolPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"claims\".\"fnols\".* FROM \"claims\".\"fnols\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fnolPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "claims: unable to reload all in FnolSlice")
	}

	*o = slice

	return nil
}

// FnolExists checks if the Fnol row exists.
func FnolExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"claims\".\"fnols\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "claims: unable to check if fnols exists")
	}

	return exists, nil
}
