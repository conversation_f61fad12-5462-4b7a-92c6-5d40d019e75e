load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "claims",
    srcs = [
        "boil_queries.go",
        "boil_table_names.go",
        "boil_types.go",
        "boil_view_names.go",
        "bordereaux_reports.go",
        "feedbacks.go",
        "fnol_attachments.go",
        "fnol_contacts.go",
        "fnol_intake_emails.go",
        "fnol_vehicles.go",
        "fnols.go",
        "psql_upsert.go",
        "summaries.go",
        "summary_feedbacks.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_models/claims",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_friendsofgo_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//drivers",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@com_github_volatiletech_sqlboiler_v4//queries/qmhelper",
        "@com_github_volatiletech_strmangle//:strmangle",
    ],
)
