package endorsement_review

import (
	"time"

	endorsement_request "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/application/endorsementapp"
	endreviewenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums"
	"nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
)

type Review struct {
	ID uuid.UUID

	State endreviewenums.EndorsementReviewState

	RequestID uuid.UUID

	PricingContextIDs []uuid.UUID

	Overrides []Override

	WrittenPremium endorsementapp.EndorsementWrittenPremium

	UnderwritingAssistantID uuid.UUID

	ApprovedAt *time.Time

	CreatedAt, UpdatedAt time.Time

	PrimaryInsuredName string

	DefaultEffectiveDate time.Time

	// Actions is a list of actions that have been taken on the review
	// Ex: Pull MVR
	Actions []EndorsementReviewAction

	// PolicyChangeFormHandleIds holds the handle IDs of the change doc for the policies affected.
	PolicyChangeFormHandleIds []PolicyChangeFormHandleId

	// SupportingDocsHandleIds holds a list of handle IDs for any supporting documents associated to that endorsement
	// review. For example, this could include the state filing docs for a vehicle addition.
	// Note that these docs are not a part of the endorsement review. Rather these docs are primarily used for
	// audit and claims purposes.
	SupportingDocsHandleIds []uuid.UUID

	Notes *string
}

// Override represents a change to the endorsement review
// This change can be a new change or an update to an existing change
// Ex: Add MVR data to a driver change in the original request
type Override struct {
	ID               uuid.UUID
	ReviewID         uuid.UUID
	OriginalChangeID *uuid.UUID // nil for overrides that lead to new changes
	ChangeData       *endorsement.ChangeData
	IsActive         bool
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

type UpdaterFunc func(review *Review) *Review

type CreateEndorsementReviewArgs struct {
	RequestID               uuid.UUID
	DefaultEffectiveDate    time.Time
	PrimaryInsuredName      string
	UnderwritingAssistantID uuid.UUID
	WrittenPremium          *endorsementapp.EndorsementWrittenPremium
	MVRPullDetails          *endorsement_request.MVRPullDetails
}

type EndorsementReviewAction struct {
	Action         endreviewenums.EndorsementReviewActionType
	LastModifiedAt time.Time
}

type PolicyChangeFormHandleId struct {
	PolicyNumber string
	HandleId     uuid.UUID
}
