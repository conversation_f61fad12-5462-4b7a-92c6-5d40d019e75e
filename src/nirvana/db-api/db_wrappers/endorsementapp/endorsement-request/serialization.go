package endorsement_request

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_models/endorsementapp"
	eTypes "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
)

const (
	coreChange     = "CoreChange"
	nonFleetChange = "NonFleetChange"
	fleetChange    = "FleetChange"
)

func endorsementRequestToDb(request *Request) (*endorsementapp.Request, *[]endorsementapp.Change, error) {
	if request == nil {
		return nil, nil, errors.New("endorsement request object is not present or is empty")
	}

	writtenPremium, err := json.Marshal(request.WrittenPremium)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to serialize endorsement written premium")
	}

	quoteGenerationInfo, err := json.Marshal(request.QuoteGenerationInfo)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to serialize pricing job info")
	}

	reqDbObj := endorsementapp.Request{
		ID:                           request.ID.String(),
		State:                        request.State.String(),
		BaseID:                       request.Base.ID.String(),
		BaseType:                     request.Base.Type.String(),
		ProgramType:                  request.ProgramType.String(),
		ProducerID:                   request.ProducerID.String(),
		MarketerID:                   request.MarketerID.String(),
		CreatedBy:                    request.CreatedBy.String(),
		AgencyID:                     request.AgencyID.String(),
		CreatedAt:                    request.CreatedAt,
		UpdatedAt:                    request.UpdatedAt,
		BundleExternalID:             request.BundleExternalID,
		DefaultEffectiveDate:         null.TimeFromPtr(request.DefaultEffectiveDate),
		ProvisionalEndorsementNumber: null.StringFrom(request.ProvisionalEndorsementNumber),
		WrittenPremium:               null.JSONFrom(writtenPremium),
		BoundAt:                      null.TimeFromPtr(request.BoundAt),
		QuoteGenerationInfo:          null.JSONFrom(quoteGenerationInfo),
	}

	changesDbObj, err := changesToDb(request.Changes, request.ID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to serialize endorsement changes")
	}

	return &reqDbObj, &changesDbObj, nil
}

func changesToDb(changes []eTypes.Change, requestID uuid.UUID) ([]endorsementapp.Change, error) {
	var endorsementChanges []endorsementapp.Change
	for i := range changes {
		endorsementChange, err := changeToDb(&changes[i], requestID)
		if err != nil {
			return nil, errors.Wrap(err, "failed to serialize endorsement change")
		}
		endorsementChanges = append(endorsementChanges, *endorsementChange)
	}

	return endorsementChanges, nil
}

func changeToDb(change *eTypes.Change, requestID uuid.UUID) (*endorsementapp.Change, error) {
	if change == nil {
		return nil, errors.New("endorsement change object is not present or is empty")
	}

	marshaller := protojson.MarshalOptions{
		EmitUnpopulated: false,
		UseProtoNames:   true,
	}

	changeData, err := marshaller.Marshal(proto.Message(change.Data))
	if err != nil {
		return nil, errors.Wrap(err, "failed to serialize endorsement change data")
	}

	changeType, err := getChangeType(change)
	if err != nil {
		return nil, errors.Wrap(err, "failed to serialize endorsement change type")
	}

	return &endorsementapp.Change{
		ID:                    change.Id,
		RequestID:             requestID.String(),
		ChangeType:            *changeType,
		ChangeData:            null.JSONFrom(changeData),
		AffectedPolicyNumbers: change.PolicyNumbers,
		Description:           null.StringFrom(change.Description),
		IsActive:              change.IsActive,
		EffectiveFrom:         change.EffectiveInterval.Start.AsTime(),
		ExpiresAt:             change.EffectiveInterval.End.AsTime(),
	}, nil
}

func getChangeType(change *eTypes.Change) (*string, error) {
	var changeType *string
	switch {
	case change.Data.GetCoreChange() != nil:
		changeType = pointer_utils.ToPointer(coreChange)
	case change.Data.GetNonFleetChange() != nil:
		changeType = pointer_utils.ToPointer(nonFleetChange)
	case change.Data.GetFleetChange() != nil:
		changeType = pointer_utils.ToPointer(fleetChange)
	default:
		return nil, errors.New("failed to serialize endorsement change type")
	}
	return changeType, nil
}
