load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "endorsement-request",
    srcs = [
        "authz_resource.go",
        "deserialization.go",
        "filters.go",
        "fx.go",
        "interfaces.go",
        "mock_wrapper.go",
        "object_defs.go",
        "serialization.go",
        "utils.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/application/endorsementapp",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/retry",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/endorsementapp",
        "//nirvana/db-api/db_wrappers/common",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/shared",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-core/proto",
        "//nirvana/jobber/jtypes",
        "//nirvana/nonfleet/model/endorsement",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
