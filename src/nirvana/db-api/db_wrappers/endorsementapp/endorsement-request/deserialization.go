package endorsement_request

import (
	"encoding/json"
	"time"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	dbendorsementapp "nirvanatech.com/nirvana/db-api/db_models/endorsementapp"
	eTypes "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endreqenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	"nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
)

func endorsementRequestFromDb(
	obj *dbendorsementapp.Request,
) (*Request, error) {
	if obj == nil {
		return nil, errors.New("endorsement request object is not present or is empty")
	}

	// Using an array of pointers to endorsement.Change to avoid copying the objects.
	// This also ensures that we do proper handling of the mutex locks in the Change object.
	var endorsementChanges []*eTypes.Change
	for _, change := range obj.R.Changes {
		endorsementChange, err := changesFromDb(change)
		if err != nil {
			return nil, errors.Wrap(err, "failed to deserialize endorsement change")
		}
		endorsementChanges = append(endorsementChanges, endorsementChange)
	}

	state, err := endreqenums.EndorsementRequestStateString(obj.State)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse endorsement request state")
	}

	basisType, err := endreqenums.BasisTypeString(obj.BaseType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse basis type")
	}

	id, err := uuid.Parse(obj.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse endorsement request ID: %s", obj.ID)
	}

	baseID, err := uuid.Parse(obj.BaseID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse endorsement request base ID: %s", obj.BaseID)
	}

	producerID, err := uuid.Parse(obj.ProducerID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse producer ID: %s", obj.ProducerID)
	}

	marketerID, err := uuid.Parse(obj.MarketerID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse marketer ID: %v", obj.MarketerID)
	}

	agencyID, err := uuid.Parse(obj.AgencyID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse agency ID: %v", obj.AgencyID)
	}

	createdBy, err := uuid.Parse(obj.CreatedBy)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse created by ID: %s", obj.CreatedBy)
	}

	var defaultEffectiveDate *time.Time
	if obj.DefaultEffectiveDate.Valid {
		defaultEffectiveDate = pointer_utils.ToPointer(obj.DefaultEffectiveDate.Time.UTC())
	}

	var provisionalEndorsementNumber string
	if obj.ProvisionalEndorsementNumber.Valid {
		provisionalEndorsementNumber = obj.ProvisionalEndorsementNumber.String
	}

	var writtenPremium *endorsementapp.EndorsementWrittenPremium
	if obj.WrittenPremium.Valid {
		err = json.Unmarshal(obj.WrittenPremium.JSON, &writtenPremium)
		if err != nil {
			return nil, errors.Wrap(err, "failed to deserialize endorsement written premium")
		}
	}

	var quoteGenerationInfo *QuoteGenerationInfo
	if obj.QuoteGenerationInfo.Valid {
		err = json.Unmarshal(obj.QuoteGenerationInfo.JSON, &quoteGenerationInfo)
		if err != nil {
			return nil, errors.Wrap(err, "failed to deserialize pricing job info")
		}
	}

	retval := Request{
		ID:                   id,
		State:                state,
		BundleExternalID:     obj.BundleExternalID,
		DefaultEffectiveDate: defaultEffectiveDate,
		Base: RequestBase{
			ID:   baseID,
			Type: basisType,
		},
		Changes: slice_utils.FromSliceOfPointers(endorsementChanges),
		PricingStages: PricingStages{
			Indication: nil,
			Bindable:   nil,
		},
		ProgramType:                  insurancecoreproto.ProgramType(insurancecoreproto.ProgramType_value[obj.ProgramType]),
		ProvisionalEndorsementNumber: provisionalEndorsementNumber,
		WrittenPremium:               writtenPremium,
		ProducerID:                   producerID,
		MarketerID:                   marketerID,
		CreatedBy:                    createdBy,
		AgencyID:                     agencyID,
		CreatedAt:                    obj.CreatedAt,
		UpdatedAt:                    obj.UpdatedAt,
		BoundAt:                      obj.BoundAt.Ptr(),
		QuoteGenerationInfo:          quoteGenerationInfo,
	}

	return &retval, nil
}

func changesFromDb(change *dbendorsementapp.Change) (*eTypes.Change, error) {
	if change == nil {
		return nil, errors.New("nil endorsement change object")
	}

	var description string
	if change.Description.Valid {
		description = change.Description.String
	}

	retval := eTypes.Change{
		PolicyNumbers: change.AffectedPolicyNumbers,
		EffectiveInterval: &proto.Interval{
			Start: timestamppb.New(change.EffectiveFrom),
			End:   timestamppb.New(change.ExpiresAt),
		},
		Id:          change.ID,
		IsActive:    change.IsActive,
		Description: description,
	}

	changeData := &endorsement.ChangeData{}
	unmarshaler := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	err := unmarshaler.Unmarshal(change.ChangeData.JSON, changeData)
	if err != nil {
		return nil, err
	}

	retval.Data = changeData
	return &retval, nil
}
