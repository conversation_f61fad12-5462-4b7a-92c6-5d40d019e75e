package endorsement_request

import (
	"time"

	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/openapi-specs/components/common"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/application/endorsementapp"
	dbendorsementapp "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endreqenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/shared"
)

type RequestBase struct {
	ID   uuid.UUID
	Type endreqenums.BasisType
}

type Request struct {
	ID    uuid.UUID
	State endreqenums.EndorsementRequestState

	// Base represents the foundation on which the endorsement application is based.
	// It can be a bundle or another endorsement. For NF, it would be a bundle,
	// but for other LOBs, it can be another endorsement.
	Base RequestBase

	// Changes is a slice of modifications to be applied to change the IB for this endorsement.
	Changes []dbendorsementapp.Change

	// PricingStages represents the different stages of pricing for this endorsement request
	PricingStages PricingStages

	ProgramType insurancecoreproto.ProgramType

	// BundleExternalID is the external ID of the bundle to which this endorsement request belongs
	// This is used for grouping endorsements together.
	BundleExternalID string

	// ProvisionalEndorsementNumber represents the tentative sequence (e.g., 1.1, 1.2)
	// before the endorsement is finalized
	ProvisionalEndorsementNumber string

	// DefaultEffectiveDate is the default effective date for the endorsement request.
	// Note: Each change in the endorsement request can have its own effective date as well
	DefaultEffectiveDate *time.Time

	// WrittenPremium is the premium for the endorsement request
	// once it has been approved by the UW
	// The source of truth for this written premium will still
	// remain the bundle even though we are persisting it here
	WrittenPremium *endorsementapp.EndorsementWrittenPremium

	// QuoteGenerationInfo contains information about actions related to pricing associated with this endorsement request
	QuoteGenerationInfo *QuoteGenerationInfo

	// Metadata for the endorsement request.
	ProducerID, MarketerID uuid.UUID
	CreatedBy              uuid.UUID
	AgencyID               uuid.UUID
	CreatedAt, UpdatedAt   time.Time
	BoundAt                *time.Time
}

type PricingStages struct {
	Indication []shared.PricingContext
	Bindable   []shared.PricingContext
}

// QuoteGenerationInfo tracks MVR pull status (when needed) and pricing job status.
// The overall quote generation state is derived from the combination of both statuses.
type QuoteGenerationInfo struct {
	PricingJobInfo *PricingJobInfo `json:"pricing_job_info,omitempty"`
	MVRPullDetails *MVRPullDetails `json:"mvr_pull_details,omitempty"`
}

// PricingJobInfo contains information about pricing job associated with an endorsement request
type PricingJobInfo struct {
	JobRunID jtypes.JobRunId                  `json:"job_run_id"`
	Status   endreqenums.QuoteGenerationState `json:"status"`
	Error    *string                          `json:"error,omitempty"`
}

type MVRPullDetails struct {
	Status         common.MVRPullStatus `json:"status"`
	LatestPullTime time.Time            `json:"latest_pull_time"`
}

type UpdaterFunc func(request *Request) *Request

type CreateEndorsementRequestArgs struct {
	BundleExternalID       string
	BaseID                 uuid.UUID
	ProgramType            insurancecoreproto.ProgramType
	ProducerID             uuid.UUID
	MarketerID             uuid.UUID
	CreatedBy              uuid.UUID
	AgencyID               uuid.UUID
	InsuranceBundleVersion int64
}
