package enums

//go:generate go run github.com/dmarkham/enumer -type=EndorsementRequestState -json -trimprefix=EndorsementRequestState
type EndorsementRequestState int

const (
	EndorsementRequestStateInvalid EndorsementRequestState = iota
	EndorsementRequestStateCreated
	EndorsementRequestStateUnderUWReview
	EndorsementRequestStateApproved
	EndorsementRequestStateBound
	EndorsementRequestStateDeclined
	EndorsementRequestStateClosed
	EndorsementRequestStateOutOfSync
)

//go:generate go run github.com/dmarkham/enumer -type=BasisType -json -trimprefix=BasedOutOf
type BasisType int

// Basis Type tells us what the endorsement is based out of.
// An endorsement can be based out of a bundle or another endorsement.
// Currently, we only support endorsements based out of a bundle.
// But in the future, we may support endorsements based out of another endorsement.
const (
	BasedOutOfInvalid BasisType = iota
	BasedOutOfBundle
	BasedOutOfEndorsement
)

//go:generate go run github.com/dmarkham/enumer -type=QuoteGenerationState -json -trimprefix=PricingJobStatus
type QuoteGenerationState int

const (
	PricingJobStatusInvalid QuoteGenerationState = iota
	PricingJobStatusRunning
	PricingJobStatusCompleted
	PricingJobStatusFailed
)
