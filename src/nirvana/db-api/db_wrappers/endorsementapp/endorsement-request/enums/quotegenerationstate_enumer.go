// Code generated by "enumer -type=QuoteGenerationState -json -trimprefix=PricingJobStatus"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _QuoteGenerationStateName = "InvalidRunningCompletedFailed"

var _QuoteGenerationStateIndex = [...]uint8{0, 7, 14, 23, 29}

const _QuoteGenerationStateLowerName = "invalidrunningcompletedfailed"

func (i QuoteGenerationState) String() string {
	if i < 0 || i >= QuoteGenerationState(len(_QuoteGenerationStateIndex)-1) {
		return fmt.Sprintf("QuoteGenerationState(%d)", i)
	}
	return _QuoteGenerationStateName[_QuoteGenerationStateIndex[i]:_QuoteGenerationStateIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _QuoteGenerationStateNoOp() {
	var x [1]struct{}
	_ = x[PricingJobStatusInvalid-(0)]
	_ = x[PricingJobStatusRunning-(1)]
	_ = x[PricingJobStatusCompleted-(2)]
	_ = x[PricingJobStatusFailed-(3)]
}

var _QuoteGenerationStateValues = []QuoteGenerationState{PricingJobStatusInvalid, PricingJobStatusRunning, PricingJobStatusCompleted, PricingJobStatusFailed}

var _QuoteGenerationStateNameToValueMap = map[string]QuoteGenerationState{
	_QuoteGenerationStateName[0:7]:        PricingJobStatusInvalid,
	_QuoteGenerationStateLowerName[0:7]:   PricingJobStatusInvalid,
	_QuoteGenerationStateName[7:14]:       PricingJobStatusRunning,
	_QuoteGenerationStateLowerName[7:14]:  PricingJobStatusRunning,
	_QuoteGenerationStateName[14:23]:      PricingJobStatusCompleted,
	_QuoteGenerationStateLowerName[14:23]: PricingJobStatusCompleted,
	_QuoteGenerationStateName[23:29]:      PricingJobStatusFailed,
	_QuoteGenerationStateLowerName[23:29]: PricingJobStatusFailed,
}

var _QuoteGenerationStateNames = []string{
	_QuoteGenerationStateName[0:7],
	_QuoteGenerationStateName[7:14],
	_QuoteGenerationStateName[14:23],
	_QuoteGenerationStateName[23:29],
}

// QuoteGenerationStateString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func QuoteGenerationStateString(s string) (QuoteGenerationState, error) {
	if val, ok := _QuoteGenerationStateNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _QuoteGenerationStateNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to QuoteGenerationState values", s)
}

// QuoteGenerationStateValues returns all values of the enum
func QuoteGenerationStateValues() []QuoteGenerationState {
	return _QuoteGenerationStateValues
}

// QuoteGenerationStateStrings returns a slice of all String values of the enum
func QuoteGenerationStateStrings() []string {
	strs := make([]string, len(_QuoteGenerationStateNames))
	copy(strs, _QuoteGenerationStateNames)
	return strs
}

// IsAQuoteGenerationState returns "true" if the value is listed in the enum definition. "false" otherwise
func (i QuoteGenerationState) IsAQuoteGenerationState() bool {
	for _, v := range _QuoteGenerationStateValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for QuoteGenerationState
func (i QuoteGenerationState) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for QuoteGenerationState
func (i *QuoteGenerationState) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("QuoteGenerationState should be a string, got %s", data)
	}

	var err error
	*i, err = QuoteGenerationStateString(s)
	return err
}
