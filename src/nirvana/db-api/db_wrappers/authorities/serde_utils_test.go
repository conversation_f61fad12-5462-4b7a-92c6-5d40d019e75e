package authorities_test

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	authorities_db_models "nirvanatech.com/nirvana/db-api/db_models/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	"nirvanatech.com/nirvana/underwriting/authority/types"
	authority_enums "nirvanatech.com/nirvana/underwriting/authority/enums"
)

func TestAuthorityRequestSerde(t *testing.T) {
	now := time.Now().Truncate(time.Microsecond) // Truncate to match DB precision

	originalRequest := authorities.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData:         null.JSONFrom([]byte(`{"reason": "test"}`)),
		RequesterID:         uuid.New(),
		LastReviewedBy:      null.StringFrom(uuid.New().String()),
		ReviewData:          null.JSONFrom([]byte(`{"comment": "needs review"}`)),
		CreatedAt:           now,
		UpdatedAt:           now,
		SubmittedAt:         null.TimeFrom(now),
		LastReviewedAt:      null.TimeFrom(now),
		ExecutedAt:          null.TimeFrom(now),
	}

	// Convert to DB model
	dbModel, err := authorities.AuthorityRequestToDB(originalRequest)
	require.NoError(t, err)

	// Verify DB model fields
	assert.Equal(t, originalRequest.ID.String(), dbModel.ID)
	assert.Equal(t, originalRequest.ApplicationReviewID.String(), dbModel.ApplicationReviewID)
	assert.Equal(t, originalRequest.RequestType.String(), dbModel.RequestType)
	assert.Equal(t, originalRequest.State.String(), dbModel.State)
	assert.Equal(t, originalRequest.RequestData, dbModel.RequestData)
	assert.Equal(t, originalRequest.RequesterID.String(), dbModel.RequesterID)
	assert.Equal(t, originalRequest.LastReviewedBy, dbModel.LastReviewedBy)
	assert.Equal(t, originalRequest.ReviewData, dbModel.ReviewData)
	assert.Equal(t, originalRequest.CreatedAt, dbModel.CreatedAt)
	assert.Equal(t, originalRequest.UpdatedAt, dbModel.UpdatedAt)
	assert.Equal(t, originalRequest.SubmittedAt, dbModel.SubmittedAt)
	assert.Equal(t, originalRequest.LastReviewedAt, dbModel.LastReviewedAt)
	assert.Equal(t, originalRequest.ExecutedAt, dbModel.ExecutedAt)

	// Convert back to domain model
	convertedRequest, err := authorities.AuthorityRequestFromDB(*dbModel)
	require.NoError(t, err)

	// Verify round-trip conversion
	assert.Equal(t, originalRequest.ID, convertedRequest.ID)
	assert.Equal(t, originalRequest.ApplicationReviewID, convertedRequest.ApplicationReviewID)
	assert.Equal(t, originalRequest.RequestType, convertedRequest.RequestType)
	assert.Equal(t, originalRequest.State, convertedRequest.State)
	assert.Equal(t, originalRequest.RequestData, convertedRequest.RequestData)
	assert.Equal(t, originalRequest.RequesterID, convertedRequest.RequesterID)
	assert.Equal(t, originalRequest.LastReviewedBy, convertedRequest.LastReviewedBy)
	assert.Equal(t, originalRequest.ReviewData, convertedRequest.ReviewData)
	assert.Equal(t, originalRequest.CreatedAt, convertedRequest.CreatedAt)
	assert.Equal(t, originalRequest.UpdatedAt, convertedRequest.UpdatedAt)
	assert.Equal(t, originalRequest.SubmittedAt, convertedRequest.SubmittedAt)
	assert.Equal(t, originalRequest.LastReviewedAt, convertedRequest.LastReviewedAt)
	assert.Equal(t, originalRequest.ExecutedAt, convertedRequest.ExecutedAt)
}

func TestAuthorityRequestFromDBInvalidIDs(t *testing.T) {
	tests := []struct {
		name        string
		setupDB     func() authorities_db_models.Request
		expectError string
	}{
		{
			name: "Invalid ID",
			setupDB: func() authorities_db_models.Request {
				return authorities_db_models.Request{
					ID:                  "invalid-uuid",
					ApplicationReviewID: uuid.New().String(),
					RequesterID:         uuid.New().String(),
					RequestType:         "decline",
					State:               "draft",
				}
			},
			expectError: "invalid request ID",
		},
		{
			name: "Invalid ApplicationReviewID",
			setupDB: func() authorities_db_models.Request {
				return authorities_db_models.Request{
					ID:                  uuid.New().String(),
					ApplicationReviewID: "invalid-uuid",
					RequesterID:         uuid.New().String(),
					RequestType:         "decline",
					State:               "draft",
				}
			},
			expectError: "invalid application review ID",
		},
		{
			name: "Invalid RequesterID",
			setupDB: func() authorities_db_models.Request {
				return authorities_db_models.Request{
					ID:                  uuid.New().String(),
					ApplicationReviewID: uuid.New().String(),
					RequesterID:         "invalid-uuid",
					RequestType:         "decline",
					State:               "draft",
				}
			},
			expectError: "invalid requester ID",
		},
		{
			name: "Invalid RequestType",
			setupDB: func() authorities_db_models.Request {
				return authorities_db_models.Request{
					ID:                  uuid.New().String(),
					ApplicationReviewID: uuid.New().String(),
					RequesterID:         uuid.New().String(),
					RequestType:         "invalid-type",
					State:               "draft",
				}
			},
			expectError: "invalid request type",
		},
		{
			name: "Invalid State",
			setupDB: func() authorities_db_models.Request {
				return authorities_db_models.Request{
					ID:                  uuid.New().String(),
					ApplicationReviewID: uuid.New().String(),
					RequesterID:         uuid.New().String(),
					RequestType:         "decline",
					State:               "invalid-state",
				}
			},
			expectError: "invalid request state",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dbModel := tt.setupDB()
			_, err := authorities.AuthorityRequestFromDB(dbModel)
			require.Error(t, err)
			assert.Contains(t, err.Error(), tt.expectError)
		})
	}
}

func TestAuthorityRequestStateTransitionSerde(t *testing.T) {
	now := time.Now().Truncate(time.Microsecond) // Truncate to match DB precision

	originalTransition := authorities.AuthorityRequestStateTransition{
		RequestID: uuid.New(),
		Timestamp: now,
		FromState: enums.RequestStateDraft,
		ToState:   enums.RequestStatePending,
		Metadata:  null.JSONFrom([]byte(`{"user_id": "123", "reason": "ready for review"}`)),
	}

	// Convert to DB model
	dbModel, err := authorities.AuthorityRequestStateTransitionToDB(originalTransition)
	require.NoError(t, err)

	// Verify DB model fields
	assert.Equal(t, originalTransition.RequestID.String(), dbModel.RequestID)
	assert.Equal(t, originalTransition.Timestamp, dbModel.Timestamp)
	assert.Equal(t, originalTransition.FromState.String(), dbModel.FromState)
	assert.Equal(t, originalTransition.ToState.String(), dbModel.ToState)
	assert.Equal(t, originalTransition.Metadata, dbModel.Metadata)

	// Convert back to domain model
	convertedTransition, err := authorities.AuthorityRequestStateTransitionFromDB(*dbModel)
	require.NoError(t, err)

	// Verify round-trip conversion
	assert.Equal(t, originalTransition.RequestID, convertedTransition.RequestID)
	assert.Equal(t, originalTransition.Timestamp, convertedTransition.Timestamp)
	assert.Equal(t, originalTransition.FromState, convertedTransition.FromState)
	assert.Equal(t, originalTransition.ToState, convertedTransition.ToState)
	assert.Equal(t, originalTransition.Metadata, convertedTransition.Metadata)
}

func TestAuthorityRequestStateTransitionFromDBInvalid(t *testing.T) {
	tests := []struct {
		name        string
		setupDB     func() authorities_db_models.RequestsStateTransition
		expectError string
	}{
		{
			name: "Invalid RequestID",
			setupDB: func() authorities_db_models.RequestsStateTransition {
				return authorities_db_models.RequestsStateTransition{
					RequestID: "invalid-uuid",
					Timestamp: time.Now(),
					FromState: "draft",
					ToState:   "pending",
				}
			},
			expectError: "invalid request ID",
		},
		{
			name: "Invalid FromState",
			setupDB: func() authorities_db_models.RequestsStateTransition {
				return authorities_db_models.RequestsStateTransition{
					RequestID: uuid.New().String(),
					Timestamp: time.Now(),
					FromState: "invalid-state",
					ToState:   "pending",
				}
			},
			expectError: "invalid from state",
		},
		{
			name: "Invalid ToState",
			setupDB: func() authorities_db_models.RequestsStateTransition {
				return authorities_db_models.RequestsStateTransition{
					RequestID: uuid.New().String(),
					Timestamp: time.Now(),
					FromState: "draft",
					ToState:   "invalid-state",
				}
			},
			expectError: "invalid to state",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dbModel := tt.setupDB()
			_, err := authorities.AuthorityRequestStateTransitionFromDB(dbModel)
			require.Error(t, err)
			assert.Contains(t, err.Error(), tt.expectError)
		})
	}
}

func TestMetadataToJSON(t *testing.T) {
	userID := uuid.New()
	reviewerID := uuid.New()
	
	tests := []struct {
		name     string
		metadata authorities.StateTransitionMetadata
		expected bool // whether result should be valid
	}{
		{
			name:     "Nil metadata",
			metadata: nil,
			expected: false, // null.JSON should be invalid/empty
		},
		{
			name:     "StateTransitionMetadata",
			metadata: types.NewStateTransitionMetadata("test reason", userID),
			expected: true,
		},
		{
			name:     "ApprovalMetadata",
			metadata: types.NewApprovalMetadata(reviewerID, authority_enums.ReviewActionApprove, "test notes", enums.RequestStateExecuted),
			expected: true,
		},
		{
			name:     "RejectionMetadata",
			metadata: types.NewRejectionMetadata(reviewerID, authority_enums.ReviewActionReject, "rejection notes", enums.RequestStateRejected),
			expected: true,
		},
		{
			name:     "CancellationMetadata",
			metadata: types.NewCancellationMetadata("superseded_by_new_request", userID),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := authorities.MetadataToJSON(tt.metadata)
			require.NoError(t, err)

			if tt.expected {
				assert.True(t, result.Valid)
				if tt.metadata != nil {
					assert.NotEmpty(t, result.JSON)
				}
			} else {
				assert.False(t, result.Valid)
			}
		})
	}
}
