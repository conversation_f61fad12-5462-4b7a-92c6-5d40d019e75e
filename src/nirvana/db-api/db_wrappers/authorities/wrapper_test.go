package authorities_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	authorities_db_models "nirvanatech.com/nirvana/db-api/db_models/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	"nirvanatech.com/nirvana/underwriting/authority/types"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func TestAuthorityRequestWrapperTestSuite(t *testing.T) {
	suite.Run(t, new(AuthorityRequestWrapperTestSuite))
}

type AuthorityRequestWrapperTestSuite struct {
	suite.Suite
	ctx     context.Context
	fxApp   *fxtest.App
	wrapper authorities.AuthorityRequestWrapper
}

func (s *AuthorityRequestWrapperTestSuite) SetupTest() {
	var env struct {
		fx.In
		Wrapper authorities.AuthorityRequestWrapper
	}
	s.ctx = context.Background()
	s.fxApp = testloader.RequireStart(s.T(), &env)
	s.wrapper = env.Wrapper
}

func (s *AuthorityRequestWrapperTestSuite) TearDownTest() {
	s.fxApp.RequireStop()
}

func (s *AuthorityRequestWrapperTestSuite) TestCreateRequest() {
	// Create test request
	request := authorities.AuthorityRequest{
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeDecline,
		RequesterID:         uuid.New(),
	}

	// Create request
	createdRequest, err := s.wrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)
	s.Assert().NotEqual(uuid.Nil, createdRequest.ID)
	s.Assert().Equal(enums.RequestStateDraft, createdRequest.State)
	s.Assert().False(createdRequest.CreatedAt.IsZero())
	s.Assert().False(createdRequest.UpdatedAt.IsZero())

	// Verify state transition was recorded
	transitions, err := s.wrapper.GetStateTransitions(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Len(transitions, 1)
	s.Assert().Equal(enums.RequestStateInvalid, transitions[0].FromState)
	s.Assert().Equal(enums.RequestStateDraft, transitions[0].ToState)
}

func (s *AuthorityRequestWrapperTestSuite) TestGetRequest() {
	// Create test request
	request := authorities.AuthorityRequest{
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeApprove,
		RequesterID:         uuid.New(),
	}

	createdRequest, err := s.wrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Get request by ID
	fetchedRequest, err := s.wrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(createdRequest.ID, fetchedRequest.ID)
	s.Assert().Equal(createdRequest.RequestType, fetchedRequest.RequestType)
	s.Assert().Equal(createdRequest.State, fetchedRequest.State)
}

func (s *AuthorityRequestWrapperTestSuite) TestGetRequestsByApplicationReview() {
	applicationReviewID := uuid.New()

	// Create multiple requests for the same application review
	for i := 0; i < 3; i++ {
		request := authorities.AuthorityRequest{
			ApplicationReviewID: applicationReviewID,
			RequestType:         enums.RequestTypeDecline,
			RequesterID:         uuid.New(),
		}
		_, err := s.wrapper.CreateRequest(s.ctx, request)
		s.Require().NoError(err)
	}

	// Fetch requests by application review using flexible GetRequests
	requests, err := s.wrapper.GetRequests(s.ctx,
		authorities_db_models.RequestWhere.ApplicationReviewID.EQ(applicationReviewID.String()),
		qm.OrderBy(authorities_db_models.RequestColumns.CreatedAt+" DESC"),
	)
	s.Require().NoError(err)
	s.Assert().Len(requests, 3)

	// Verify all requests belong to the same application review
	for _, req := range requests {
		s.Assert().Equal(applicationReviewID, req.ApplicationReviewID)
	}
}

func (s *AuthorityRequestWrapperTestSuite) TestGetPendingRequests() {
	// Create a request and transition it to pending
	request := authorities.AuthorityRequest{
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeApprove,
		RequesterID:         uuid.New(),
	}

	createdRequest, err := s.wrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Transition to pending
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStatePending
		return req, &authorities.StateTransitionInfo{}, nil
	})
	s.Require().NoError(err)

	// Get pending requests using flexible GetRequests
	pendingRequests, err := s.wrapper.GetRequests(s.ctx,
		authorities_db_models.RequestWhere.State.EQ(enums.RequestStatePending.String()),
	)
	s.Require().NoError(err)

	// Verify our request is in the pending list
	found := false
	for _, req := range pendingRequests {
		if req.ID == createdRequest.ID {
			found = true
			s.Assert().Equal(enums.RequestStatePending, req.State)
			break
		}
	}
	s.Assert().True(found, "Created request should be in pending list")
}

func (s *AuthorityRequestWrapperTestSuite) TestUpdateRequest() {
	// Create test request
	request := authorities.AuthorityRequest{
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeDecline,
		RequesterID:         uuid.New(),
	}

	createdRequest, err := s.wrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Update request
	testMetadata := types.NewStateTransitionMetadata("test reason", uuid.New())
	testDataJSON, err := authorities.MetadataToJSON(testMetadata)
	s.Require().NoError(err)

	updatedRequest, err := s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.RequestData = testDataJSON
		return req, nil, nil
	})
	s.Require().NoError(err)

	s.Assert().Equal(testDataJSON, updatedRequest.RequestData)
	s.Assert().True(updatedRequest.UpdatedAt.After(createdRequest.UpdatedAt))
}

func (s *AuthorityRequestWrapperTestSuite) TestTransitionState() {
	// Create test request
	request := authorities.AuthorityRequest{
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeApprove,
		RequesterID:         uuid.New(),
	}

	createdRequest, err := s.wrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	reviewerID := uuid.New()
	metadata := types.NewStateTransitionMetadata("looks good", uuid.New())

	// Transition to pending
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStatePending
		return req, &authorities.StateTransitionInfo{
			Metadata: metadata,
		}, nil
	})
	s.Require().NoError(err)

	// Transition to approved
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStateApproved
		return req, &authorities.StateTransitionInfo{
			Metadata:   metadata,
			ReviewerID: &reviewerID,
		}, nil
	})
	s.Require().NoError(err)

	// Verify final state
	updatedRequest, err := s.wrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateApproved, updatedRequest.State)
	s.Assert().Equal(reviewerID.String(), updatedRequest.LastReviewedBy.String)
	s.Assert().True(updatedRequest.LastReviewedAt.Valid)
	s.Assert().True(updatedRequest.SubmittedAt.Valid)

	// Verify state transitions
	transitions, err := s.wrapper.GetStateTransitions(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Len(transitions, 3) // created -> draft, draft -> pending, pending -> approved

	// Check the approval transition
	approvalTransition := transitions[2]
	s.Assert().Equal(enums.RequestStatePending, approvalTransition.FromState)
	s.Assert().Equal(enums.RequestStateApproved, approvalTransition.ToState)
	s.Assert().True(approvalTransition.Metadata.Valid)
}

func (s *AuthorityRequestWrapperTestSuite) TestGetRequestsWithFilters() {
	// Create test requests with different states
	appReviewID := uuid.New()

	// Create a pending request
	pendingRequest := authorities.AuthorityRequest{
		ApplicationReviewID: appReviewID,
		RequestType:         enums.RequestTypeApprove,
		RequesterID:         uuid.New(),
	}
	createdPending, err := s.wrapper.CreateRequest(s.ctx, pendingRequest)
	s.Require().NoError(err)

	// Transition to pending
	_, err = s.wrapper.UpdateRequest(s.ctx, createdPending.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStatePending
		return req, &authorities.StateTransitionInfo{}, nil
	})
	s.Require().NoError(err)

	// Create a draft request
	draftRequest := authorities.AuthorityRequest{
		ApplicationReviewID: appReviewID,
		RequestType:         enums.RequestTypeDecline,
		RequesterID:         uuid.New(),
	}
	_, err = s.wrapper.CreateRequest(s.ctx, draftRequest)
	s.Require().NoError(err)

	// Test: Get all requests for this application review
	allRequests, err := s.wrapper.GetRequests(s.ctx,
		authorities_db_models.RequestWhere.ApplicationReviewID.EQ(appReviewID.String()),
		qm.OrderBy(authorities_db_models.RequestColumns.CreatedAt+" ASC"),
	)
	s.Require().NoError(err)
	s.Assert().Len(allRequests, 2)

	// Test: Get only pending requests
	pendingRequests, err := s.wrapper.GetRequests(s.ctx,
		authorities_db_models.RequestWhere.State.EQ(enums.RequestStatePending.String()),
	)
	s.Require().NoError(err)
	s.Assert().Len(pendingRequests, 1)
	s.Assert().Equal(enums.RequestStatePending, pendingRequests[0].State)

	// Test: Get requests by type
	approveRequests, err := s.wrapper.GetRequests(s.ctx,
		authorities_db_models.RequestWhere.RequestType.EQ(enums.RequestTypeApprove.String()),
	)
	s.Require().NoError(err)
	s.Assert().Len(approveRequests, 1)
	s.Assert().Equal(enums.RequestTypeApprove, approveRequests[0].RequestType)

	// Test: Complex filter - pending approve requests for specific application
	complexFilter, err := s.wrapper.GetRequests(s.ctx,
		authorities_db_models.RequestWhere.ApplicationReviewID.EQ(appReviewID.String()),
		authorities_db_models.RequestWhere.State.EQ(enums.RequestStatePending.String()),
		authorities_db_models.RequestWhere.RequestType.EQ(enums.RequestTypeApprove.String()),
	)
	s.Require().NoError(err)
	s.Assert().Len(complexFilter, 1)
	s.Assert().Equal(createdPending.ID, complexFilter[0].ID)
}

func (s *AuthorityRequestWrapperTestSuite) TestGetStateTransitions() {
	// Create test request
	request := authorities.AuthorityRequest{
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeClose,
		RequesterID:         uuid.New(),
	}

	createdRequest, err := s.wrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Perform multiple state transitions
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStatePending
		return req, &authorities.StateTransitionInfo{}, nil
	})
	s.Require().NoError(err)

	reviewerID := uuid.New()
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStateRejected
		return req, &authorities.StateTransitionInfo{
			Metadata:   types.NewStateTransitionMetadata("insufficient info", reviewerID),
			ReviewerID: &reviewerID,
		}, nil
	})
	s.Require().NoError(err)

	// Get all transitions
	transitions, err := s.wrapper.GetStateTransitions(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Len(transitions, 3)

	// Verify transitions are in chronological order
	for i := 1; i < len(transitions); i++ {
		s.Assert().True(transitions[i].Timestamp.After(transitions[i-1].Timestamp) || transitions[i].Timestamp.Equal(transitions[i-1].Timestamp))
	}

	// Verify the sequence
	s.Assert().Equal(enums.RequestStateInvalid, transitions[0].FromState)
	s.Assert().Equal(enums.RequestStateDraft, transitions[0].ToState)

	s.Assert().Equal(enums.RequestStateDraft, transitions[1].FromState)
	s.Assert().Equal(enums.RequestStatePending, transitions[1].ToState)

	s.Assert().Equal(enums.RequestStatePending, transitions[2].FromState)
	s.Assert().Equal(enums.RequestStateRejected, transitions[2].ToState)
}

func (s *AuthorityRequestWrapperTestSuite) TestExecutedStateTransition() {
	// Create and approve a request
	request := authorities.AuthorityRequest{
		ApplicationReviewID: uuid.New(),
		RequestType:         enums.RequestTypeDecline,
		RequesterID:         uuid.New(),
	}

	createdRequest, err := s.wrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Transition through the workflow
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStatePending
		return req, &authorities.StateTransitionInfo{}, nil
	})
	s.Require().NoError(err)

	reviewerID := uuid.New()
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStateApproved
		return req, &authorities.StateTransitionInfo{
			ReviewerID: &reviewerID,
		}, nil
	})
	s.Require().NoError(err)

	// Execute the request
	_, err = s.wrapper.UpdateRequest(s.ctx, createdRequest.ID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStateExecuted
		return req, &authorities.StateTransitionInfo{}, nil
	})
	s.Require().NoError(err)

	// Verify executed timestamp is set
	executedRequest, err := s.wrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateExecuted, executedRequest.State)
	s.Assert().True(executedRequest.ExecutedAt.Valid)
	s.Assert().True(executedRequest.SubmittedAt.Valid)
	s.Assert().True(executedRequest.LastReviewedAt.Valid)
}

func (s *AuthorityRequestWrapperTestSuite) TestNonExistentRequest() {
	nonExistentID := uuid.New()

	// Try to get non-existent request
	_, err := s.wrapper.GetRequest(s.ctx, nonExistentID)
	s.Assert().Error(err)
	s.Assert().Contains(err.Error(), "not found")

	// Try to update non-existent request
	_, err = s.wrapper.UpdateRequest(s.ctx, nonExistentID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		return req, nil, nil
	})
	s.Assert().Error(err)
	s.Assert().Contains(err.Error(), "not found")

	// Try to update non-existent request state
	_, err = s.wrapper.UpdateRequest(s.ctx, nonExistentID, func(req *authorities.AuthorityRequest) (*authorities.AuthorityRequest, *authorities.StateTransitionInfo, error) {
		req.State = enums.RequestStatePending
		return req, &authorities.StateTransitionInfo{}, nil
	})
	s.Assert().Error(err)
	s.Assert().Contains(err.Error(), "not found")
}
