load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "authorities",
    srcs = [
        "fx.go",
        "interfaces.go",
        "serde_utils.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/authorities",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/postgres_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "authorities_test",
    srcs = [
        "serde_utils_test.go",
        "wrapper_test.go",
    ],
    deps = [
        ":authorities",
        "//nirvana/db-api/db_models/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/infra/fx/testloader",
        "//nirvana/underwriting/authority/enums",
        "//nirvana/underwriting/authority/types",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
