package admitted_app

import (
	"context"
	"strings"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/email_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/common-go/zip_code_utils"
	admitted_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/nirvanaapp/enums"
	nirvanaapp "nirvanatech.com/nirvana/nirvanaapp/models/application"
	"nirvanatech.com/nirvana/nonfleet/coverages/admitted"
)

const (
	ValidationFailedErrMsg = "failed to validate application"
)

func validateCompany(c nirvanaapp.CompanyDetails) error {
	if c.Name == "" {
		return errors.New("Company name cannot be empty")
	}

	if c.DOTNumber == nil {
		return errors.New("DOTNumber cannot be empty")
	}

	if c.NoOfPowerUnits == nil {
		return errors.New("NoOfPowerUnits cannot be empty")
	}

	if c.USState == us_states.InvalidStateCode {
		return errors.New("Invalid State of Company")
	}

	if c.BusinessOwner.FirstName == "" {
		return errors.New("Business Owner FirstName cannot be empty")
	}

	if c.BusinessOwner.LastName == "" {
		return errors.New("Business Owner LastName cannot be empty")
	}

	if c.MailingAddress != nil {
		err := validateAddress(*c.MailingAddress)
		if err != nil {
			return errors.Wrap(err, "Mailing Address is invalid")
		}
	}

	err := validateAddress(c.PhysicalAddress)
	if err != nil {
		return errors.Wrap(err, "Physical Address is invalid")
	}

	err = validateAddress(c.GarageAddress)
	if err != nil {
		return errors.Wrap(err, "Garage Address is invalid")
	}

	// Validate Zip Code as this zip code is used for garaging zip in rating
	_, err = zip_code_utils.DecodeZip(context.Background(), c.GarageAddress.Zip)
	if err != nil {
		return errors.Wrapf(err, "Failed to decode zip code %v", c.GarageAddress.Zip)
	}

	return nil
}

func validateAddress(a nirvanaapp.Address) error {
	if a.Street == "" {
		return errors.New("Street cannot be empty")
	}

	if a.City == "" {
		return errors.New("City cannot be empty")
	}

	if a.State == "" {
		return errors.New("State cannot be empty")
	}

	if a.Zip == "" {
		return errors.New("Zip cannot be empty")
	}

	return nil
}

func validateEquipmentInfo(app nirvanaapp.App) error {
	if len(app.EquipmentDetails.Vehicles) == 0 {
		return errors.New("At least one vehicle is required")
	}

	hasAutoPhysicalDamageCoverage := false
	for _, cov := range app.CoverageDetails.Coverages {
		if cov.CoverageType == enums.CoverageTypeAutoPhysicalDamage {
			hasAutoPhysicalDamageCoverage = true
			break
		}
	}

	if hasAutoPhysicalDamageCoverage {
		// Validate all vehicles have non-nil StatedValues
		statedValueSum := 0
		for _, v := range app.EquipmentDetails.Vehicles {
			if v.StatedValue == nil {
				return errors.New("All vehicles must have a StatedValue when Auto Physical Damage coverage is selected")
			}
			statedValueSum += int(*v.StatedValue)
		}

		//  Validate the sum of all StatedValues is not zero
		if statedValueSum == 0 {
			return errors.New("StatedValue sum for all vehicles cannot be zero when Auto Physical Damage coverage is selected")
		}
	}

	hasAtleastOneTruckOrTractor := false
	for _, v := range app.EquipmentDetails.Vehicles {
		vehicle, err := convertToAdmittedVehicle(v)
		if err != nil {
			return err
		}

		if err = ValidateVehicle(*vehicle); err != nil {
			return err
		}

		if v.VehicleType == enums.VehicleTypeTruck || v.VehicleType == enums.VehicleTypeTractor {
			hasAtleastOneTruckOrTractor = true
		}
	}

	if !hasAtleastOneTruckOrTractor {
		return errors.New("Atleast one truck or tractor is required")
	}
	return nil
}

func validateDriverInfo(d nirvanaapp.DriverDetails) error {
	for _, dr := range d.Drivers {
		if dr.FirstName == "" {
			return errors.New("Driver FirstName cannot be empty")
		}

		if dr.LastName == "" {
			return errors.New("Driver LastName cannot be empty")
		}

		if dr.LicenseNumber == "" {
			return errors.New("LicenseNumber cannot be empty")
		}

		if dr.LicenseState == "" {
			return errors.New("LicenseState cannot be empty")
		}

		if dr.YearsOfExperience == nil {
			return errors.New("driver YearsOfExperience cannot be empty")
		}

		// driver date of birth should be at least 14 yrs and at most 125 yrs similar to the validation
		// that we have in the FE
		if dr.DateOfBirth.AddDate(14, 0, 0).After(time_utils.DateFromTime(time.Now())) ||
			dr.DateOfBirth.AddDate(125, 0, 0).Before(time_utils.DateFromTime(time.Now())) {
			return errors.New(ValidationFailedErrMsg + "Driver DateOfBirth is not valid")
		}
	}
	return nil
}

func validateCoverageInfo(c nirvanaapp.CoverageDetails) error {
	isAlCoveragePresent := false
	seenCoverages := make(map[enums.CoverageType]bool)

	for _, cov := range c.Coverages {
		if cov.CoverageType == enums.CoverageTypeInvalid {
			return errors.New("Invalid Coverage type")
		}

		if seenCoverages[cov.CoverageType] {
			return errors.Newf("Duplicate coverage found: %s", cov.CoverageType.String())
		}
		seenCoverages[cov.CoverageType] = true

		if cov.CoverageType == enums.CoverageTypeAutoLiability {
			isAlCoveragePresent = true
		}

		if cov.CoverageType == enums.CoverageTypeAutoLiability {
			if cov.Deductible != nil && *cov.Deductible != 0 {
				return errors.New("Deductible is not allowed for Auto Liability Coverage")
			}
		}

		if cov.CoverageType == enums.CoverageTypeAutoLiability || cov.CoverageType == enums.CoverageTypeMotorTruckCargo {
			if cov.Limit == nil {
				return errors.Newf("Limit is mandatory for %s Coverage", cov.CoverageType.String())
			}

			covType, err := getCoverageType(cov.CoverageType)
			if err != nil {
				return errors.Wrap(err, "Failed to get coverage type")
			}
			availableLimits := admitted.LimitOptions[*covType]
			if !slice_utils.Contains(availableLimits, int(*cov.Limit)) {
				return errors.Newf("Invalid Limit %v for %s Coverage. Available limits Options for %s are: %v", *cov.Limit, cov.CoverageType.String(), cov.CoverageType.String(), availableLimits)
			}
		}

		if cov.CoverageType == enums.CoverageTypeMotorTruckCargo || cov.CoverageType == enums.CoverageTypeAutoPhysicalDamage {
			if cov.Deductible == nil {
				return errors.Newf("Deductible is mandatory for %s Coverage", cov.CoverageType.String())
			}

			covType, err := getCoverageType(cov.CoverageType)
			if err != nil {
				return errors.Wrap(err, "Failed to get coverage type")
			}
			availableDeductibles := admitted.DeductibleOptions[*covType]
			if !slice_utils.Contains(availableDeductibles, int(*cov.Deductible)) {
				return errors.Newf("Invalid Deductible %v for %s Coverage. Available Deductible Options for %s are: %v", *cov.Deductible, cov.CoverageType.String(), cov.CoverageType.String(), availableDeductibles)
			}
		}
	}
	if !isAlCoveragePresent {
		return errors.New("Auto Liability Coverage is mandatory")
	}
	return nil
}

func validateCommodityInfo(app nirvanaapp.App) error {
	for _, cov := range app.CoverageDetails.Coverages {
		if cov.CoverageType == enums.CoverageTypeMotorTruckCargo && len(app.CommodityDetails.Commodities) == 0 {
			return errors.New("Motor Truck Cargo Coverage requires at least one commodity")
		}
	}

	totalPercentage := 0
	seenCommodities := map[enums.Commodity]struct{}{}
	for _, com := range app.CommodityDetails.Commodities {
		if com.Name == enums.CommodityInvalid {
			return errors.New("Invalid Commodity")
		}
		if _, ok := seenCommodities[com.Name]; ok {
			return errors.Newf("Duplicate Commodity %s found", com.Name)
		}
		seenCommodities[com.Name] = struct{}{}
		totalPercentage += int(*com.Percentage)
	}

	if totalPercentage != 100 {
		return errors.New("Total percentage of commodities should be 100")
	}
	return nil
}

func validateClassInfo(r nirvanaapp.RadiusDetails) error {
	if r.MaxRadiusOfOperationInMiles == nil {
		return errors.New("maximum radius of operation cannot be empty")
	}

	for _, rb := range r.RadiusBuckets {
		if rb.MinRadiusInMiles == nil {
			return errors.New("min miles cannot be empty")
		}

		if rb.Percentage == nil {
			return errors.New("radius distribution percentage cannot be empty")
		}
	}
	return nil
}

func validateTSPUser(t nirvanaapp.TSPUser) error {
	if t.Name == "" {
		return errors.New("TSP User Name cannot be empty")
	}

	if t.Email == "" {
		return errors.New("TSP User Email cannot be empty")
	}

	if !email_utils.IsValidEmail(strings.ToLower(t.Email)) {
		return errors.Newf("Invalid TSP User Email %v", t.Email)
	}
	return nil
}

func ValidateVehicle(vehicle VehicleDetails) error {
	if vehicle.VIN == "" {
		return errors.New("VIN cannot be empty")
	}

	if vehicle.Make == "" {
		return errors.New("Make cannot be empty")
	}

	if vehicle.Type == admitted_enums.VehicleTypeUnknown {
		return errors.New("invalid Vehicle type")
	}

	if vehicle.Class == admitted_enums.VehicleClassUnidentified {
		return errors.New("invalid Vehicle class")
	}

	if vehicle.WeightClass == admitted_enums.WeightClassNil {
		return errors.New("WeightClass cannot be empty")
	}

	err := ValidateWeightClass(vehicle.Type, vehicle.Class, vehicle.WeightClass)
	if err != nil {
		return err
	}

	return ValidateVehicleClass(vehicle.Type, vehicle.Class)
}

func ValidateWeightClass(vehType admitted_enums.VehicleType, vehClass admitted_enums.VehicleClass, weightClass admitted_enums.WeightClass) error {
	var validWeightClasses []admitted_enums.WeightClass
	// nolint:exhaustive
	switch vehType {
	case admitted_enums.VehicleTypeTractor:
		validWeightClasses = []admitted_enums.WeightClass{
			admitted_enums.WeightClass5,
			admitted_enums.WeightClass6,
			admitted_enums.WeightClass7,
			admitted_enums.WeightClass8,
		}
	case admitted_enums.VehicleTypeTruck:
		// nolint:exhaustive
		switch vehClass {
		case admitted_enums.VehicleClassGarbage:
			validWeightClasses = []admitted_enums.WeightClass{
				admitted_enums.WeightClass7,
				admitted_enums.WeightClass8,
			}
		case admitted_enums.VehicleClassAutoHauler:
			validWeightClasses = []admitted_enums.WeightClass{
				admitted_enums.WeightClass5,
				admitted_enums.WeightClass6,
				admitted_enums.WeightClass7,
				admitted_enums.WeightClass8,
			}
		default:
			validWeightClasses = []admitted_enums.WeightClass{
				admitted_enums.WeightClass3,
				admitted_enums.WeightClass4,
				admitted_enums.WeightClass5,
				admitted_enums.WeightClass6,
				admitted_enums.WeightClass7,
				admitted_enums.WeightClass8,
			}
		}
	case admitted_enums.VehicleTypeTrailer:
		validWeightClasses = []admitted_enums.WeightClass{
			admitted_enums.WeightClass7,
			admitted_enums.WeightClass8,
		}
	case admitted_enums.VehicleTypePickup:
		validWeightClasses = []admitted_enums.WeightClass{
			admitted_enums.WeightClass3,
			admitted_enums.WeightClass4,
		}
	}

	if !slice_utils.Contains(validWeightClasses, weightClass) {
		return errors.Errorf(
			"Invalid Weight Class : %s for the given Vehicle Type : %v and Vehicle class : %v. Valid Vehicle Weight classes are %v",
			weightClass.String(), vehType, vehClass, validWeightClasses,
		)
	}

	return nil
}

func ValidateVehicleClass(
	vehType admitted_enums.VehicleType,
	vehClass admitted_enums.VehicleClass,
) error {
	allowedVehClassForType := VehicleTypeToClassesMap[vehType]
	var allowedVehClassToDisplay []string
	for _, allowedClass := range allowedVehClassForType {
		if vehClass == allowedClass {
			return nil
		}
		allowedVehClassToDisplay = append(allowedVehClassToDisplay,
			strings.TrimPrefix(allowedClass.String(), "VehicleClass"))
	}
	return errors.Newf("Invalid Vehicle Class for the given Vehicle Type: %s. "+
		"Vehicle class should belong to one of: %v ",
		strings.TrimPrefix(vehType.String(), "VehicleType"), allowedVehClassToDisplay)
}
