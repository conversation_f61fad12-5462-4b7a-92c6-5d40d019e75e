CREATE TABLE IF NOT EXISTS nars.claim_feedbacks (
  id uuid PRIMARY KEY NOT NULL,
  claim_id uuid NOT NULL,
  created_by uuid NOT NULL,
  rating integer NOT NULL,
  value varchar(1024),
  created_at timestamp NOT NULL,
  category varchar NOT NULL
);

CREATE INDEX IF NOT EXISTS nars_claim_feedbacks_claims_id ON nars.claim_feedbacks(claim_id);
ALTER TABLE IF EXISTS nars.claim_feedbacks ADD CONSTRAINT claim_feedbacks_claims_id_fkey FOREIGN KEY (claim_id) REFERENCES nars.claims(id);
