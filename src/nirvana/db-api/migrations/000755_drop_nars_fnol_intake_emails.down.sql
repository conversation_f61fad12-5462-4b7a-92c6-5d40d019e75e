CREATE TABLE IF NOT EXISTS nars.fnol_intake_emails (
  id uuid PRIMARY KEY NOT NULL,
  message_id varchar NOT NULL,
  draft_fnol_id uuid,
  fnol_id uuid,
  sent_at timestamp NOT NULL,
  created_at timestamp NOT NULL
);

CREATE INDEX IF NOT EXISTS fnol_intake_emails_draft_fnol_id_idx ON nars.fnol_intake_emails(draft_fnol_id);
CREATE UNIQUE INDEX IF NOT EXISTS fnol_intake_emails_message_id_key ON nars.fnol_intake_emails(message_id);
