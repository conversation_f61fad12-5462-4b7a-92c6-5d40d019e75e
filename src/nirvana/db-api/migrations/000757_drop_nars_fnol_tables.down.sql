CREATE TABLE IF NOT EXISTS nars.fnol (
    id uuid PRIMARY KEY NOT NULL,
    policy_number varchar NOT NULL,
    loss_datetime timestamp NOT NULL,
    loss_location varchar NOT NULL,
    police_agency_name varchar,
    police_report_number varchar,
    incident_description varchar(5000) NOT NULL,
    notice_type varchar NOT NULL,
    created_at timestamp NOT NULL,
    loss_state varchar NOT NULL,
    injuries_involved boolean NOT NULL,
    client_claim_number varchar NOT NULL,
    created_by uuid NOT NULL,
    status varchar NOT NULL,
    source varchar
);

CREATE INDEX IF NOT EXISTS nars_fnol_client_claim_number ON nars.fnol(client_claim_number);

CREATE TABLE IF NOT EXISTS nars.fnol_vehicles (
  id uuid PRIMARY KEY NOT NULL,
  fnol_id uuid NOT NULL,
  is_insured_vehicle boolean NOT NULL,
  registration_number varchar,
  vin varchar
);

CREATE INDEX IF NOT EXISTS nars_fnol_vehicles_fnol_id ON nars.fnol_vehicles(fnol_id);
ALTER TABLE IF EXISTS nars.fnol_vehicles ADD CONSTRAINT fnol_vehicles_fnol_id_fkey FOREIGN KEY (fnol_id) REFERENCES nars.fnol(id);

CREATE TABLE IF NOT EXISTS nars.fnol_contacts(
    id uuid PRIMARY KEY NOT NULL,
    fnol_id uuid NOT NULL,
    contact_type varchar NOT NULL,
    first_name varchar NOT NULL,
    last_name varchar NOT NULL,
    phone varchar NOT NULL,
    email varchar
);

CREATE INDEX IF NOT EXISTS nars_fnol_contacts_fnol_id ON nars.fnol_contacts(fnol_id);
ALTER TABLE IF EXISTS nars.fnol_contacts ADD CONSTRAINT fnol_contacts_fnol_id_fkey FOREIGN KEY (fnol_id) REFERENCES nars.fnol(id);

CREATE TABLE IF NOT EXISTS nars.fnol_attachments(
    id uuid PRIMARY KEY NOT NULL,
    fnol_id uuid NOT NULL,
    handle_id uuid NOT NULL
);

CREATE INDEX IF NOT EXISTS nars_fnol_attachments_fnol_id ON nars.fnol_attachments(fnol_id);
ALTER TABLE IF EXISTS nars.fnol_attachments ADD CONSTRAINT fnol_attachments_fnol_id_fkey FOREIGN KEY (fnol_id) REFERENCES nars.fnol(id);
