CREATE TABLE IF NOT EXISTS nars.claim_summaries (
  id uuid PRIMARY KEY NOT NULL,
  claim_id uuid NOT NULL,
  summary text NOT NULL,
  created_at timestamp NOT NULL,
  title varchar(255) NOT NULL,
  scheduled boolean NOT NULL,
  interval_start timestamp NOT NULL,
  interval_end timestamp NOT NULL
);

CREATE INDEX IF NOT EXISTS nars_claim_summaries_claim_id ON nars.claim_summaries(claim_id);
ALTER TABLE IF EXISTS nars.claim_summaries ADD CONSTRAINT claim_summaries_claim_id_fkey FOREIGN KEY (claim_id) REFERENCES nars.claims(id);

CREATE TABLE IF NOT EXISTS nars.claim_summary_feedbacks (
  id uuid PRIMARY KEY NOT NULL,
  claim_summary_id uuid NOT NULL,
  created_by uuid NOT NULL,
  rating integer NOT NULL,
  created_at timestamp NOT NULL,
  updated_at timestamp NOT NULL,
  category varchar NOT NULL
);

CREATE INDEX IF NOT EXISTS nars_claim_summary_feedbacks_claim_summaries_id ON nars.claim_summary_feedbacks(claim_summary_id);
ALTER TABLE IF EXISTS nars.claim_summary_feedbacks ADD CONSTRAINT claim_summary_feedbacks_claim_summaries_id_fkey FOREIGN KEY (claim_summary_id) REFERENCES nars.claim_summaries(id);

CREATE UNIQUE INDEX IF NOT EXISTS unique_summary_user_feedback ON nars.claim_summary_feedbacks(claim_summary_id, created_by);
