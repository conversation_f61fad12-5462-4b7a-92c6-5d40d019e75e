CREATE TABLE IF NOT EXISTS nars.draft_fnols (
    id                   uuid PRIMARY KEY NOT NULL,
    policy_number        character varying,
    loss_datetime        timestamp without time zone,
    loss_location        character varying,
    loss_state           character varying,
    police_agency_name   character varying,
    police_report_number character varying,
    incident_description character varying(5000),
    notice_type          character varying,
    injuries_involved    boolean,
    created_by           character varying NOT NULL,
    created_at           timestamp without time zone NOT NULL,
    updated_at           timestamp without time zone NOT NULL,
    fnol_id              uuid,
    dot_number           bigint,
    archived_at          timestamp with time zone,
    source               character varying,
    insured_name         character varying,
    original             jsonb
);

CREATE TABLE IF NOT EXISTS nars.draft_fnol_vehicles (
    id                  uuid PRIMARY KEY NOT NULL,
    draft_fnol_id       uuid NOT NULL,
    is_insured_vehicle  boolean,
    registration_number character varying,
    vin                 character varying,
    created_at          timestamp without time zone NOT NULL,
    updated_at          timestamp without time zone NOT NULL
);

CREATE INDEX IF NOT EXISTS draft_fnol_vehicles_draft_fnols_id_idx ON nars.draft_fnol_vehicles(draft_fnol_id);
ALTER TABLE IF EXISTS nars.draft_fnol_vehicles ADD CONSTRAINT draft_fnol_vehicles_draft_fnols_id_fkey FOREIGN KEY (draft_fnol_id) REFERENCES nars.draft_fnols(id);

CREATE TABLE IF NOT EXISTS nars.draft_fnol_contacts (
    id            uuid PRIMARY KEY NOT NULL,
    draft_fnol_id uuid NOT NULL,
    contact_type  character varying,
    first_name    character varying,
    last_name     character varying,
    phone         character varying,
    email         character varying,
    created_at    timestamp without time zone NOT NULL,
    updated_at    timestamp without time zone NOT NULL
);

CREATE INDEX IF NOT EXISTS draft_fnol_contacts_draft_fnol_id_idx ON nars.draft_fnol_contacts(draft_fnol_id);
ALTER TABLE IF EXISTS nars.draft_fnol_contacts ADD CONSTRAINT draft_fnol_contacts_draft_fnol_id_fkey FOREIGN KEY (draft_fnol_id) REFERENCES nars.draft_fnols(id);

CREATE TABLE IF NOT EXISTS nars.draft_fnol_attachments (
    id            uuid PRIMARY KEY NOT NULL,
    draft_fnol_id uuid NOT NULL,
    handle_id     uuid NOT NULL,
    created_at    timestamp without time zone NOT NULL
);

CREATE INDEX IF NOT EXISTS draft_fnol_attachments_draft_fnol_id_idx ON nars.draft_fnol_attachments(draft_fnol_id);
ALTER TABLE IF EXISTS nars.draft_fnol_attachments ADD CONSTRAINT draft_fnol_attachments_draft_fnol_id_fkey FOREIGN KEY (draft_fnol_id) REFERENCES nars.draft_fnols(id);
