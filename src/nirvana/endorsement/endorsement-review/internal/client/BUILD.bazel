load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "client",
    srcs = [
        "approval.go",
        "client.go",
        "count.go",
        "fx.go",
        "list.go",
        "mvr.go",
        "request_handlers.go",
        "state_machine.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/endorsement/endorsement-review/internal/client",
    visibility = ["//nirvana/endorsement/endorsement-review:__subpackages__"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/endorsement/change/enums",
        "//nirvana/db-api/db_wrappers/endorsement/change/non-fleet/admitted/schedule-change",
        "//nirvana/db-api/db_wrappers/endorsement/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsement/endorsement-review/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted",
        "//nirvana/endorsement/common",
        "//nirvana/endorsement/endorsement-review/internal",
        "//nirvana/endorsement/endorsement-review/internal/state-machine",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/openapi-specs/components/endorsement",
        "//nirvana/rating/mvr",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_launchdarkly_go_sdk_common_v3//ldcontext",
        "@com_github_looplab_fsm//:fsm",
        "@org_uber_go_fx//:fx",
    ],
)
