package client

import (
	"context"
	"slices"

	"nirvanatech.com/nirvana/rating/mvr"

	underwriting "nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"nirvanatech.com/nirvana/common-go/us_states"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/launchdarkly/go-sdk-common/v3/ldcontext"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	endorsementenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/enums"
	schedulechange "nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/non-fleet/admitted/schedule-change"
	endorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/endorsement-review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nfapp "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

const (
	endorsementReviewIDKey = "endorsementReviewID"
)

func (i *Impl) PopulateMVRDetailsMap(
	ctx context.Context, review *endorsementreview.EndorsementReview, policy *policy.Policy,
	scheduleChange *schedulechange.ScheduleChange,
) error {
	programData, err := policy.GetAdmittedProgramData(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to get admitted program data")
	}
	originalDriversList := programData.DriverInfo.Drivers
	newDriversList := scheduleChange.Drivers
	driverChangeList := schedulechange.ConvertToDriverChangeLists(originalDriversList, newDriversList)
	driverDetails := slices.Clone(driverChangeList.Added)
	for _, updatedDriverDetail := range driverChangeList.Updated {
		driverDetails = append(driverDetails, updatedDriverDetail.New)
	}
	mvrDetailsMap, err := getMVRDetailsMap(ctx, driverDetails, review, programData.CompanyInfo.USState, i.deps.FetcherClientFactory)
	if err != nil {
		return errors.Wrap(err, "couldn't get mvr details map")
	}
	scheduleChange.MVRDetailsMap = mvrDetailsMap
	return nil
}

func getMVRDetailsMap(
	ctx context.Context,
	driverDetails []admitted.DriverDetails,
	endorsementReview *endorsementreview.EndorsementReview,
	usState us_states.USState,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) (map[string]underwriting.MVRDetails, error) {
	mvrDetailsMap := make(map[string]underwriting.MVRDetails)
	driverBasicDetails, err := getDriverBasicDetails(driverDetails)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't get driver basic details slice")
	}
	mvrDetails := underwriting.GetMVRDetails(
		ctx,
		driverBasicDetails,
		endorsementReview.EffectiveDate,
		enums.ProgramTypeNonFleetAdmitted,
		usState,
		fetcherClientFactory,
	)
	for _, mvrDetail := range mvrDetails {
		mvrDetailsMap[mvrDetail.CdlNumber] = mvrDetail
	}
	return mvrDetailsMap, nil
}

func getDriverBasicDetails(driverDetails []admitted.DriverDetails) ([]application.DriverBasicDetails, error) {
	var drivers []application.DriverBasicDetails
	for _, driver := range driverDetails {
		driverBasicDetail := driver.ToAdmittedAppDriver().DriverBasicDetails
		drivers = append(drivers, driverBasicDetail)
	}
	return drivers, nil
}

func (i *Impl) validateSetMVRPullFlagRequest(ctx context.Context, endorsementReviewID uuid.UUID) error {
	if endorsementReviewID == uuid.Nil {
		return errors.New("nil endorsement review id")
	}
	endorsementReview, err := i.Get(ctx, endorsementReviewID)
	if err != nil {
		log.Error(
			ctx, "failed to get endorsement review", log.Err(err),
			log.Stringer("endorsementReviewID", endorsementReviewID),
		)
		return errors.Wrapf(err, "failed to get endorsement review for id %s", endorsementReviewID)
	}
	if !endorsementReview.State.IsNonTerminal() {
		log.Error(
			ctx, "failed to set mvr pull flag for endorsement review in terminal state",
			log.Stringer("endorsementReviewID", endorsementReviewID),
			log.Stringer("endorsementReviewState", endorsementReview.State),
		)
		return errors.Newf(
			"failed to set mvr pull flag for endorsement review in terminal state %s",
			endorsementReview.State.String(),
		)
	}
	for _, changeReview := range endorsementReview.Data.ChangeReviews {
		if changeReview.Change.Type == endorsementenums.ChangeTypeNonFleetAdmittedScheduleChange {
			_, ok := changeReview.Change.Data.(*schedulechange.ScheduleChange)
			if !ok {
				return errors.New("unable to convert data to scheduleChange")
			}
			return nil
		}
	}
	return errors.New("endorsement review does not contain schedule type change")
}

func (i *Impl) SetDriverViolations(
	ctx context.Context,
	endorsementReviewID uuid.UUID,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) (*endorsementreview.EndorsementReview, error) {
	endorsementReview, err := i.Get(ctx, endorsementReviewID)
	if err != nil {
		log.Error(
			ctx, "failed to get endorsement review", log.Err(err),
			log.Stringer("endorsementReviewID", endorsementReviewID),
		)
		return nil, errors.Wrapf(err, "failed to get endorsement review for id %s", endorsementReviewID)
	}

	endorsementContext := ldcontext.NewBuilder(endorsementReview.ID.String()).
		Kind(endorsementKindKey).
		Anonymous(true).
		SetString(endorsementReviewIDKey, endorsementReview.ID.String()).
		Build()

	// TODO: Remove this Feature Flag check once we migrate to the
	// new logic of giving UW flexibility to override the violations
	flagEnabled, err := i.deps.FeatureFlagClient.BoolVariation(
		[]ldcontext.Context{endorsementContext},
		feature_flag_lib.FeatureMVROverrides,
		false,
	)
	if err != nil {
		// Log the error and continue, we don't want to block the process
		log.Warn(ctx, "Unable to check flag", log.Err(err))
	}

	// If flag is enabled, don't pull MVR & directly use the previously persisted overrides
	if flagEnabled {
		log.Info(ctx, "Skipping MVR pull as feature flag is enabled")
		return endorsementReview, nil
	}

	for idx, changeReview := range endorsementReview.Data.ChangeReviews {
		changeType := changeReview.Change.Data.GetType()
		if changeType == endorsementenums.ChangeTypeNonFleetAdmittedScheduleChange {
			endorsementReview, err = i.setViolationsForScheduleChange(
				ctx,
				changeReview,
				idx,
				endorsementReview,
				fetcherClientFactory,
			)
			if err != nil {
				log.Error(
					ctx, "failed to set driver violations for change", log.Err(err),
					log.Stringer("endorsementReviewID", endorsementReviewID),
				)
				return nil, errors.Wrapf(
					err, "failed to set driver violations for change at index %d, "+
						"in endorsement review with id %s", idx, endorsementReviewID,
				)
			}
		}
	}

	return endorsementReview, nil
}

func (i *Impl) setViolationsForScheduleChange(
	ctx context.Context,
	changeReview *endorsementreview.ChangeReview,
	changeReviewIdx int,
	endorsementReview *endorsementreview.EndorsementReview,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) (*endorsementreview.EndorsementReview, error) {
	scheduleChange, ok := changeReview.Change.Data.(*schedulechange.ScheduleChange)
	if !ok {
		return nil, errors.Newf(
			"data is not a ScheduleChange for change at index %d in endorsement review with id %s",
			changeReviewIdx, endorsementReview.ID,
		)
	}
	driversList := scheduleChange.Drivers
	if driversList == nil {
		// Implies that this is a vehicle change only or there are no drivers in the updated list.
		// Therefore, we can skip this change review.
		log.Info(
			ctx, "no drivers list found for schedule change",
			log.Stringer("endorsementReviewID", endorsementReview.ID),
		)
		return endorsementReview, nil
	}

	policyNumber := endorsementReview.PolicyNumber
	originalDriversList, err := i.getOriginalDriversList(ctx, policyNumber)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get original drivers list")
	}
	addedDrivers := getAddedDrivers(originalDriversList, driversList)

	if len(addedDrivers) == 0 {
		// Implies that this is a vehicle change only or there are no added drivers in the updated list.
		// Therefore, we can skip this change review.
		log.Info(
			ctx, "no added drivers list found for schedule change",
			log.Stringer("endorsementReviewID", endorsementReview.ID),
		)
		return endorsementReview, nil
	}

	latestPolicy, err := i.deps.PolicyClient.GetLatestPolicy(ctx, policyNumber.String(), true)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get latest policy for policy number %s", policyNumber)
	}
	var drivers []admitted_app.DriverDetails
	for _, d := range addedDrivers {
		drivers = append(drivers, admitted_app.DriverDetails{
			DriverBasicDetails: d,
		})
	}
	mvrViolations, reports, errs := mvr.GetMovingViolationCountsForDrivers(
		ctx,
		drivers,
		endorsementReview.EffectiveDate,
		enums.ProgramTypeNonFleetAdmitted,
		latestPolicy.CompanyInfo.USState,
		fetcherClientFactory,
	)
	violations, _, err := underwriting.GetDriverVioOverridesAndInfo(
		ctx,
		endorsementReview.EffectiveDate,
		addedDrivers,
		enums.ProgramTypeNonFleetAdmitted,
		underwriting.DriverViolationDetail{
			MVRReports: reports,
			Records:    mvrViolations,
			Errors:     errs,
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get driver violations")
	}
	if violations.DriverViolations == nil {
		return nil, errors.New("violations cannot be nil")
	}

	endorsementReview, err = i.updateEndorsementReviewWithDriverViolations(
		ctx, scheduleChange, endorsementReview, *violations.DriverViolations, changeReviewIdx,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to update endorsement review with driver violations")
	}

	return endorsementReview, nil
}

func (i *Impl) getOriginalDriversList(
	ctx context.Context, policyNumber *policy.NirvanaPolicyNumberImpl,
) ([]admitted.DriverDetails, error) {
	latestPolicy, err := i.deps.PolicyClient.GetLatestPolicy(ctx, policyNumber.String(), true)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get latest policy for policy number %s", policyNumber)
	}
	latestProgramData, err := latestPolicy.GetAdmittedProgramData(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get latest admitted program data")
	}

	return latestProgramData.DriverInfo.Drivers, nil
}

func getAddedDrivers(originalDriversList, driversList []admitted.DriverDetails) []nfapp.DriverBasicDetails {
	driverListChanges := schedulechange.ConvertToDriverChangeLists(originalDriversList, driversList)
	addedDrivers := driverListChanges.Added

	var convertedAddedDrivers []nfapp.DriverBasicDetails
	for _, addedDriver := range addedDrivers {
		convertedAddedDriver := addedDriver.ToAdmittedAppDriver()
		convertedAddedDrivers = append(convertedAddedDrivers, convertedAddedDriver.DriverBasicDetails)
	}
	return convertedAddedDrivers
}

func (i *Impl) updateEndorsementReviewWithDriverViolations(
	ctx context.Context,
	scheduleChange *schedulechange.ScheduleChange, endorsementReview *endorsementreview.EndorsementReview,
	violationsForAddedDrivers []admitted_app.DriverViolation, changeIdx int,
) (*endorsementreview.EndorsementReview, error) {
	dlNumberToIdxMap := schedulechange.ConstructIndexMap(
		scheduleChange.DriverViolations, schedulechange.LicenseNumberFromViolationExtractor,
	)
	for _, violation := range violationsForAddedDrivers {
		idxInOriginalViolationsList := dlNumberToIdxMap[violation.LicenseNumber]
		scheduleChange.DriverViolations[idxInOriginalViolationsList] = violation
	}

	updateFn := func(
		review *endorsementreview.EndorsementReview,
	) (*endorsementreview.EndorsementReview, error) {
		review.Data.ChangeReviews[changeIdx].Change.Data = scheduleChange
		return review, nil
	}
	endorsementReview, err := updateFn(endorsementReview)
	if err != nil {
		return nil, errors.Wrap(err, "failed to update endorsement review")
	}
	if err = i.deps.EndorsementReviewWrapper.Update(ctx, endorsementReview.ID, updateFn); err != nil {
		return nil, errors.Wrap(err, "failed to update endorsement review")
	}

	return endorsementReview, nil
}
