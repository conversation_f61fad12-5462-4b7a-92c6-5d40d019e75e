load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "model_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/insurance-bundle/model",
    proto = "//proto/insurance_bundle/model:model_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/model/proto",
        "//nirvana/common-go/proto",
        "//nirvana/fleet/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/model",
    ],
)

go_library(
    name = "model",
    srcs = [
        "charge_adjustments.go",
        "charge_utils.go",
        "constants.go",
        "coverage.go",
        "deductible.go",
        "hacky_duration.go",
        "insurance_bundle_builder.go",
        "insurance_bundle_utils.go",
        "insurance_bundle_validations.go",
        "limit.go",
        "policy_builder.go",
        "policy_utils.go",
        "policy_validations.go",
        "slice_utils.go",
        "sub_coverage.go",
    ],
    embed = [":model_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/insurance-bundle/model",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/math_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/fleet/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-core/coverage",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/model",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_zap//:zap",
    ],
)

# keep
go_proto_link(
    name = "model_go_proto_link",
    dep = ":model_go_proto",
    version = "v1",
)

go_test(
    name = "model_test",
    srcs = [
        "charge_utils_test.go",
        "coverage_test.go",
        "deductible_test.go",
        "insurance_bundle_utils_test.go",
        "insurance_bundle_validations_test.go",
        "limit_test.go",
        "policy_utils_test.go",
    ],
    embed = [":model"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/fleet/model",
        "//nirvana/insurance-core/proto",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
