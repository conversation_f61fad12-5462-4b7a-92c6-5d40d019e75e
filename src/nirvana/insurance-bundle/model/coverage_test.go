package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	fleetmodel "nirvanatech.com/nirvana/fleet/model"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
)

var (
	mockValidSingleLimit = &Limit{
		Id:             "mockID",
		DisplayName:    "mockDisplayName",
		SubCoverageIds: []string{"1"},
		Amount:         500,
		Grouping:       LimitGrouping_LimitGrouping_Single,
	}
	mockValidCombinedDeductible = &Deductible{
		SubCoverageIds: []string{"1"},
		Amount:         500,
	}
)

func TestValidateCoverageCriteria(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name       string
		criteria   *CoverageCriteria
		wantErr    bool
		wantErrMsg string
	}{
		{
			name:       "nil criteria",
			criteria:   nil,
			wantErr:    true,
			wantErrMsg: "nil coverage criteria",
		},
		{
			name: "invalid limits",
			criteria: &CoverageCriteria{
				Limits: []*Limit{{}},
			},
			wantErr:    true,
			wantErrMsg: "invalid limits: invalid coverage term at index 0",
		},
		{
			name: "invalid deductible amount",
			criteria: &CoverageCriteria{
				Limits: []*Limit{mockValidSingleLimit},
				Deductibles: []*Deductible{{
					Amount:         -1,
					SubCoverageIds: []string{"CoverageDummy"},
				}},
			},
			wantErr:    true,
			wantErrMsg: "invalid deductibles: invalid coverage term at index 0",
		},
		{
			name: "zero deductible",
			criteria: &CoverageCriteria{
				Limits: []*Limit{mockValidSingleLimit},
				Deductibles: []*Deductible{{
					SubCoverageIds: []string{"CoverageDummy"},
				}},
			},
			wantErr:    false,
			wantErrMsg: "",
		},
		{
			name: "empty deductible sub coverage ids",
			criteria: &CoverageCriteria{
				Limits: []*Limit{mockValidSingleLimit},
				Deductibles: []*Deductible{{
					Amount:         10,
					SubCoverageIds: nil,
				}},
			},
			wantErr:    true,
			wantErrMsg: "invalid deductibles: invalid coverage term at index 0",
		},
		{
			name: "duplicate limit keys",
			criteria: &CoverageCriteria{
				Limits: []*Limit{
					mockValidSingleLimit,
					{
						Id:             "mockID", // Same ID as mockValidSingleLimit
						DisplayName:    "mockDisplayName2",
						SubCoverageIds: []string{"2"},
						Amount:         1000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
					},
				},
			},
			wantErr:    true,
			wantErrMsg: "invalid limits: duplicate coverage term key mockID",
		},
		{
			name: "duplicate deductible keys",
			criteria: &CoverageCriteria{
				Limits: []*Limit{mockValidSingleLimit},
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"1"},
						Amount:         500,
					},
					{
						SubCoverageIds: []string{"1"}, // Same SubCoverageIds results in same key
						Amount:         1000,
					},
				},
			},
			wantErr:    true,
			wantErrMsg: "invalid deductibles: duplicate coverage term key 1",
		},
		{
			name: "invalid combined deductibles",
			criteria: &CoverageCriteria{
				Limits:              []*Limit{mockValidSingleLimit},
				Deductibles:         []*Deductible{mockValidCombinedDeductible},
				CombinedDeductibles: []*CombinedDeductible{{}},
			},
			wantErr:    true,
			wantErrMsg: "invalid combined deductible at index 0",
		},
		{
			name: "exposure entity variations - same type and id with single exposure entity (now allowed)",
			criteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "limit1",
						DisplayName:    "Limit 1",
						SubCoverageIds: []string{"sub1"},
						Amount:         1000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "vehicle1",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
			},
			wantErr:    false,
			wantErrMsg: "",
		},
		{
			name: "exposure entity variations - same type and id with multiple exposure entities",
			criteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "limit1",
						DisplayName:    "Limit 1",
						SubCoverageIds: []string{"sub1"},
						Amount:         1000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "vehicle1",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
					{
						Id:             "limit2",
						DisplayName:    "Limit 2",
						SubCoverageIds: []string{"sub1"},
						Amount:         2000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "vehicle1", // Same ID and type as above
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
			},
			wantErr: true,
			wantErrMsg: "coverage terms with sub-coverages sub1 do not vary by type or id (entity vehicle1:EXPOSURE_ENTITY_TYPE_VEHICLE);" +
				" exposure entities list must have at most one entity as this case is not supported",
		},
		{
			name: "exposure entity variations - different types not supported",
			criteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "limit1",
						DisplayName:    "Limit 1",
						SubCoverageIds: []string{"sub1"},
						Amount:         1000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "vehicle1",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
					{
						Id:             "limit2",
						DisplayName:    "Limit 2",
						SubCoverageIds: []string{"sub1"},
						Amount:         2000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "cargo1",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO, // Different type
							},
						},
					},
				},
			},
			wantErr:    true,
			wantErrMsg: "coverage terms with sub-coverages sub1 vary by type, which is not supported",
		},
		{
			name: "exposure entity variations - same type different ids is valid",
			criteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "limit1",
						DisplayName:    "Limit 1",
						SubCoverageIds: []string{"sub1"},
						Amount:         1000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "vehicle1",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
					{
						Id:             "limit2",
						DisplayName:    "Limit 2",
						SubCoverageIds: []string{"sub1"},
						Amount:         2000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "vehicle2", // Different ID, same type
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
			},
			wantErr:    false,
			wantErrMsg: "",
		},
		{
			name: "deductible exposure entity variations - type variation not supported",
			criteria: &CoverageCriteria{
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"sub1"},
						Amount:         500,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "vehicle1",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
					{
						SubCoverageIds: []string{"sub1"},
						Amount:         1000,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "cargo1",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO, // Different type
							},
						},
					},
				},
			},
			wantErr:    true,
			wantErrMsg: "invalid deductibles: invalid exposure entity variations for coverage terms: coverage terms with sub-coverages sub1 vary by type, which is not supported",
		},
		{
			name: "valid limits and deductibles without exposure entities",
			criteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:               "limit1",
						DisplayName:      "Limit 1",
						SubCoverageIds:   []string{"sub1"},
						Amount:           1000,
						Grouping:         LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{}, // Empty exposure entities
					},
					{
						Id:               "limit2",
						DisplayName:      "Limit 2",
						SubCoverageIds:   []string{"sub2"},
						Amount:           2000,
						Grouping:         LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{}, // Empty exposure entities
					},
				},
				Deductibles: []*Deductible{
					{
						SubCoverageIds:   []string{"sub1"},
						Amount:           500,
						ExposureEntities: []*ExposureEntity{}, // Empty exposure entities
					},
					{
						SubCoverageIds:   []string{"sub2"},
						Amount:           1000,
						ExposureEntities: []*ExposureEntity{}, // Empty exposure entities
					},
				},
			},
			wantErr:    false,
			wantErrMsg: "",
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.criteria.Validate()
			if tt.wantErr {
				require.Error(t, err)
				assert.ErrorContains(t, err, tt.wantErrMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateCoverageCriteriaAgainstPolicies(t *testing.T) {
	t.Parallel()

	// Create mock policies with different coverages and sub-coverages
	mockPolicies := createMockPolicies()

	testCases := []struct {
		name             string
		coverageCriteria *CoverageCriteria
		policies         map[string]*Policy
		programType      insurancecoreproto.ProgramType
		wantErr          bool
		wantErrExact     string
	}{
		{
			name: "success - valid limits and deductibles",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "CoverageAutoLiability",
						DisplayName:    "Auto Liability",
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
					},
				},
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000,
					},
				},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     false,
		},
		{
			name: "success - empty limits and deductibles",
			coverageCriteria: &CoverageCriteria{
				Limits:      []*Limit{},
				Deductibles: []*Deductible{},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     false,
		},
		{
			name: "failure - limit validation fails due to sub-cov not being found in the policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "InvalidLimit",
						DisplayName:    "Invalid Limit",
						SubCoverageIds: []string{"InvalidSubCoverage"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "SOME_VIN",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id InvalidLimit against policies: failed to validate" +
				" coverageTerm exposure entities against policies: failed to get applicable policies for exposure entities:" +
				" failed to get applicable policies for sub coverage IDs: no policy found for sub coverage id InvalidSubCoverage",
		},
		{
			name: "failure - deductible validation fails due to invalid sub coverage id",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{},
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"InvalidSubCoverage"},
						Amount:         1000,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "SOME_VIN",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     true,
			wantErrExact: "failed to validate deductible with key InvalidSubCoverage#SOME_VIN:EXPOSURE_ENTITY_TYPE_VEHICLE" +
				" against policies: failed to validate coverageTerm exposure entities against policies: failed to get" +
				" applicable policies for exposure entities: failed to get applicable policies for sub coverage IDs:" +
				" no policy found for sub coverage id InvalidSubCoverage",
		},
		{
			name: "failure - limit validation fails with exposure entity that doesn't exist in policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "VehicleSpecificLimit",
						DisplayName:    "Vehicle Specific Limit",
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "NONEXISTENT_VIN",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    createMockBusinessAutoPolicies(),
			programType: insurancecoreproto.ProgramType_ProgramType_BusinessAuto,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id VehicleSpecificLimit against policies: failed to validate" +
				" coverageTerm exposure entities against policies: exposure entity with key NONEXISTENT_VIN:EXPOSURE_ENTITY_TYPE_VEHICLE" +
				" validation against policies failed: exposure entity validation against policy TSNBA0012345-24 failed:" +
				" vehicle exposure entity validation failed against biz auto policy: vehicle with vin NONEXISTENT_VIN not" +
				" found for program type ProgramType_BusinessAuto",
		},
		{
			name: "failure - deductible validation fails with exposure entity that doesn't exist in policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{},
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "NONEXISTENT_VIN",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
			},
			policies:    createMockBusinessAutoPolicies(),
			programType: insurancecoreproto.ProgramType_ProgramType_BusinessAuto,
			wantErr:     true,
			wantErrExact: "failed to validate deductible with key CoverageBodilyInjury#NONEXISTENT_VIN:EXPOSURE_ENTITY_TYPE_VEHICLE" +
				" against policies: failed to validate coverageTerm exposure entities against policies: exposure entity" +
				" with key NONEXISTENT_VIN:EXPOSURE_ENTITY_TYPE_VEHICLE validation against policies failed: exposure" +
				" entity validation against policy TSNBA0012345-24 failed: vehicle exposure entity validation failed" +
				" against biz auto policy: vehicle with vin NONEXISTENT_VIN not found for program type ProgramType_BusinessAuto",
		},
		{
			name: "failure - no policies provided for required sub coverage",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "CoverageAutoLiability",
						DisplayName:    "Auto Liability",
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "SOME_VIN",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    map[string]*Policy{}, // Empty policies map
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id CoverageAutoLiability against policies: failed to validate" +
				" coverageTerm exposure entities against policies: failed to get applicable policies for exposure entities:" +
				" failed to get applicable policies for sub coverage IDs: no policy found for sub coverage id CoverageBodilyInjury",
		},
		{
			name: "success - multiple limits and deductibles with different sub coverages",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "CoverageAutoLiability",
						DisplayName:    "Auto Liability",
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
					},
					{
						Id:             "CoverageGeneralLiability",
						DisplayName:    "General Liability",
						SubCoverageIds: []string{"CoverageGeneralLiability"},
						Amount:         2000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
					},
				},
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000,
					},
					{
						SubCoverageIds: []string{"CoverageGeneralLiability"},
						Amount:         2000,
					},
				},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     false,
		},
		{
			name: "failure - multiple sub coverages map to different policies",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "CombinedLimit",
						DisplayName:    "Combined Limit",
						SubCoverageIds: []string{"CoverageBodilyInjury", "CoverageGeneralLiability"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Combined,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "SOME_VIN",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id CombinedLimit against policies: failed to validate " +
				"coverageTerm exposure entities against policies: failed to get applicable policies for exposure entities:" +
				" exposure entities must be applicable to only one policy",
		},
		{
			name: "failure - unsupported exposure entity type",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "UnsupportedEntityLimit",
						DisplayName:    "Unsupported Entity Limit",
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "SOME_NAMED_SHIPPER_ID",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id UnsupportedEntityLimit against policies: failed to " +
				"validate coverageTerm exposure entities against policies: exposure entity with key " +
				"SOME_NAMED_SHIPPER_ID:EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO validation against policies failed: exposure entity" +
				" validation against policy NNFTK0012345-24 failed: unsupported exposure entity type EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO",
		},
		{
			name: "failure - vehicle exposure entity unsupported for non-business auto program type",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "VehicleLimit",
						DisplayName:    "Vehicle Limit",
						SubCoverageIds: []string{"CoverageBodilyInjury"},
						Amount:         1000000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "SOME_VIN",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    mockPolicies,
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id VehicleLimit against policies: failed to validate" +
				" coverageTerm exposure entities against policies: exposure entity with key" +
				" SOME_VIN:EXPOSURE_ENTITY_TYPE_VEHICLE validation against policies failed: exposure entity validation" +
				" against policy NNFTK0012345-24 failed: vehicle exposure entity validation failed against biz auto policy:" +
				" exposure entity type EXPOSURE_ENTITY_TYPE_VEHICLE is not supported for program type ProgramType_NonFleetAdmitted",
		},
		{
			name: "success - terminal cargo exposure entity that exists in fleet policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "TerminalCargoLimit",
						DisplayName:    "Terminal Cargo Limit",
						SubCoverageIds: []string{"CoverageCargoAtScheduledTerminals"},
						Amount:         100000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "123 Test St", // This matches a terminal location in the default fleet builder
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    createMockFleetPolicies(),
			programType: insurancecoreproto.ProgramType_ProgramType_Fleet,
			wantErr:     false,
		},
		{
			name: "failure - terminal cargo exposure entity that doesn't exist in fleet policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "TerminalCargoLimit",
						DisplayName:    "Terminal Cargo Limit",
						SubCoverageIds: []string{"CoverageCargoAtScheduledTerminals"},
						Amount:         100000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "NONEXISTENT_TERMINAL_ID",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    createMockFleetPolicies(),
			programType: insurancecoreproto.ProgramType_ProgramType_Fleet,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id TerminalCargoLimit against policies: failed to validate" +
				" coverageTerm exposure entities against policies: exposure entity with key" +
				" NONEXISTENT_TERMINAL_ID:EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO validation against policies failed:" +
				" exposure entity validation against policy FLEETPOLICY001-24 failed: terminal cargo exposure entity" +
				" validation failed against fleet policy: terminal cargo exposure entity with id NONEXISTENT_TERMINAL_ID" +
				" not found in fleet policy with number FLEETPOLICY001-24",
		},
		{
			name: "failure - terminal cargo exposure entity with non-fleet program type",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "TerminalCargoLimit",
						DisplayName:    "Terminal Cargo Limit",
						SubCoverageIds: []string{"CoverageMotorTruckCargo"},
						Amount:         100000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "123 Test St",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    createMockPolicies(),
			programType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id TerminalCargoLimit against policies: failed to validate" +
				" coverageTerm exposure entities against policies: exposure entity with key" +
				" 123 Test St:EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO validation against policies failed: exposure entity" +
				" validation against policy NNFMC0012345-24 failed: terminal cargo exposure entity validation failed" +
				" against fleet policy: exposure entity type EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO is not supported" +
				" for program type ProgramType_NonFleetAdmitted",
		},
		{
			name: "failure - terminal cargo exposure entity with no terminal locations in fleet policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{
					{
						Id:             "TerminalCargoLimit",
						DisplayName:    "Terminal Cargo Limit",
						SubCoverageIds: []string{"CoverageCargoAtScheduledTerminals"},
						Amount:         100000,
						Grouping:       LimitGrouping_LimitGrouping_Single,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "123 Test St",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
							},
						},
					},
				},
				Deductibles: []*Deductible{},
			},
			policies:    createMockFleetPoliciesWithNoTerminalLocations(),
			programType: insurancecoreproto.ProgramType_ProgramType_Fleet,
			wantErr:     true,
			wantErrExact: "failed to validate limit with id TerminalCargoLimit against policies: failed to validate" +
				" coverageTerm exposure entities against policies: exposure entity with key" +
				" 123 Test St:EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO validation against policies failed: exposure entity" +
				" validation against policy FLEETPOLICY002-24 failed: terminal cargo exposure entity validation" +
				" failed against fleet policy: no terminal locations found",
		},
		{
			name: "success - terminal cargo deductible that exists in fleet policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{},
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"CoverageCargoAtScheduledTerminals"},
						Amount:         5000,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "456 Another St", // This matches another terminal location in the default fleet builder
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
							},
						},
					},
				},
			},
			policies:    createMockFleetPolicies(),
			programType: insurancecoreproto.ProgramType_ProgramType_Fleet,
			wantErr:     false,
		},
		{
			name: "failure - terminal cargo deductible that doesn't exist in fleet policy",
			coverageCriteria: &CoverageCriteria{
				Limits: []*Limit{},
				Deductibles: []*Deductible{
					{
						SubCoverageIds: []string{"CoverageCargoAtScheduledTerminals"},
						Amount:         5000,
						ExposureEntities: []*ExposureEntity{
							{
								Id:   "NONEXISTENT_TERMINAL_ID",
								Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
							},
						},
					},
				},
			},
			policies:    createMockFleetPolicies(),
			programType: insurancecoreproto.ProgramType_ProgramType_Fleet,
			wantErr:     true,
			wantErrExact: "failed to validate deductible with key CoverageCargoAtScheduledTerminals#NONEXISTENT_TERMINAL_ID:EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO" +
				" against policies: failed to validate coverageTerm exposure entities against policies: exposure entity" +
				" with key NONEXISTENT_TERMINAL_ID:EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO validation against policies failed:" +
				" exposure entity validation against policy FLEETPOLICY001-24 failed: terminal cargo exposure entity" +
				" validation failed against fleet policy: terminal cargo exposure entity with id NONEXISTENT_TERMINAL_ID" +
				" not found in fleet policy with number FLEETPOLICY001-24",
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			err := validateCoverageCriteriaAgainstPolicies(
				tt.coverageCriteria,
				tt.policies,
				tt.programType,
			)

			if tt.wantErr {
				require.Error(t, err)
				if tt.wantErrExact != "" {
					assert.EqualError(t, err, tt.wantErrExact)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// createMockPolicies creates a set of mock policies for testing
func createMockPolicies() map[string]*Policy {
	return map[string]*Policy{
		"NNFTK0012345-24": NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			WithPolicyNumber("NNFTK0012345-24").
			WithCoverages([]*Coverage{
				{
					Id:          "CoverageAutoLiability",
					DisplayName: "Auto Liability",
					SubCoverages: []*SubCoverage{
						{
							Id:          "CoverageBodilyInjury",
							DisplayName: "Bodily Injury",
						},
						{
							Id:          "CoveragePropertyDamage",
							DisplayName: "Property Damage",
						},
					},
				},
			}).
			Build(),
		"NNFGL0012345-24": NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			WithPolicyNumber("NNFGL0012345-24").
			WithCoverages([]*Coverage{
				{
					Id:          "CoverageGeneralLiability",
					DisplayName: "General Liability",
					SubCoverages: []*SubCoverage{
						{
							Id:          "CoverageGeneralLiability",
							DisplayName: "General Liability",
						},
					},
				},
			}).
			Build(),
		"NNFMC0012345-24": NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			WithPolicyNumber("NNFMC0012345-24").
			WithCoverages([]*Coverage{
				{
					Id:          "CoverageMotorTruckCargo",
					DisplayName: "Motor Truck Cargo",
					SubCoverages: []*SubCoverage{
						{
							Id:          "CoverageMotorTruckCargo",
							DisplayName: "Motor Truck Cargo",
						},
					},
				},
			}).
			Build(),
	}
}

// createMockBusinessAutoPolicies creates a set of mock business auto policies for testing
func createMockBusinessAutoPolicies() map[string]*Policy {
	return map[string]*Policy{
		"TSNBA0012345-24": NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_BusinessAuto).
			WithPolicyNumber("TSNBA0012345-24").
			WithCoverages([]*Coverage{
				{
					Id:          "CoverageAutoLiability",
					DisplayName: "Auto Liability",
					SubCoverages: []*SubCoverage{
						{
							Id:          "CoverageBodilyInjury",
							DisplayName: "Bodily Injury",
						},
						{
							Id:          "CoveragePropertyDamage",
							DisplayName: "Property Damage",
						},
					},
				},
			}).
			Build(),
	}
}

// createMockFleetPolicies creates a set of mock fleet policies for testing terminal cargo validation
func createMockFleetPolicies() map[string]*Policy {
	return map[string]*Policy{
		"FLEETPOLICY001-24": NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
			WithPolicyNumber("FLEETPOLICY001-24").
			WithCoverages([]*Coverage{
				{
					Id:          "CoverageMotorTruckCargo",
					DisplayName: "Motor Truck Cargo",
					SubCoverages: []*SubCoverage{
						{
							Id:          "CoverageCargoAtScheduledTerminals",
							DisplayName: "Cargo At Scheduled Terminals",
						},
					},
				},
			}).
			Build(),
	}
}

// createMockFleetPoliciesWithNoTerminalLocations creates fleet policies with no terminal locations for testing
func createMockFleetPoliciesWithNoTerminalLocations() map[string]*Policy {
	// Create a fleet policy with empty terminal locations
	policy := NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
		WithPolicyNumber("FLEETPOLICY002-24").
		WithCoverages([]*Coverage{
			{
				Id:          "CoverageMotorTruckCargo",
				DisplayName: "Motor Truck Cargo",
				SubCoverages: []*SubCoverage{
					{
						Id:          "CoverageCargoAtScheduledTerminals",
						DisplayName: "Cargo At Scheduled Terminals",
					},
				},
			},
		}).
		Build()

	// Set fleet program data with empty terminal locations
	if policy.ProgramData != nil && policy.ProgramData.GetFleetData() != nil &&
		policy.ProgramData.GetFleetData().GetCompany() != nil {
		policy.ProgramData.GetFleetData().GetCompany().TerminalLocations = []*fleetmodel.TerminalLocation{}
	}

	return map[string]*Policy{
		"FLEETPOLICY002-24": policy,
	}
}
