package model

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
)

// Declare a package-level codec so we create it only once.
var subCoverageIDsCodec = slice_utils.NewStringSliceCodec[string]()

// coverageTerm defines the common behaviour shared by the various "coverage terms"
// that make up a CoverageCriteria – currently *Limit and *Deductible.
//
// A coverage term is a logical element that applies to a set of sub-coverages
// (e.g. BI, PD, Cargo) and, optionally, to a subset of exposure entities
// (such as specific vehicles identified by VIN).  The helpers in this file use
// this interface to perform validation that is agnostic of the concrete term
// type – for example, checking for duplicate keys, enforcing exposure-entity
// variation rules, and validating applicability against policies.
//
// NOTE: The interface is intentionally **unexported** as it is only consumed by
// the helper functions in this file; exposing it wider would leak internal
// validation details of the model package.
type coverageTerm interface {
	GetKey() string
	GetExposureEntities() []*ExposureEntity
	GetSubCoverageIds() []string
	Validate() error
}

func (c *CoverageCriteria) Validate() error {
	if c == nil {
		return errors.New("nil coverage criteria")
	}

	if err := validateCoverageTerms(c.GetLimits()); err != nil {
		return errors.Wrap(err, "invalid limits")
	}

	if err := validateCoverageTerms(c.GetDeductibles()); err != nil {
		return errors.Wrap(err, "invalid deductibles")
	}

	for i, combinedDeductible := range c.GetCombinedDeductibles() {
		if err := combinedDeductible.Validate(); err != nil {
			return errors.Wrapf(err, "invalid combined deductible at index %d", i)
		}
	}

	return nil
}

func validateCoverageTerms[T coverageTerm](coverageTerms []T) error {
	seenCoverageTermKeys := map[string]bool{}
	for i, coverageTermVal := range coverageTerms {
		if err := coverageTermVal.Validate(); err != nil {
			return errors.Wrapf(err, "invalid coverage term at index %d", i)
		}
		if seenCoverageTermKeys[coverageTermVal.GetKey()] {
			return errors.Newf("duplicate coverage term key %s", coverageTermVal.GetKey())
		}
		seenCoverageTermKeys[coverageTermVal.GetKey()] = true
	}

	if err := validateExposureEntityVariationsForCoverageTerms(coverageTerms); err != nil {
		return errors.Wrap(err, "invalid exposure entity variations for coverage terms")
	}

	return nil
}

func validateExposureEntityVariationsForCoverageTerms[T coverageTerm](coverageTerms []T) error {
	// Validate exposure entity variations for coverage terms(limits or deductibles) with the same sub-coverage sets
	//  - Variation by type is not supported.
	//  - If type is not varied, then ids must be varied.
	coverageTermsBySubCoverageSet := map[string][]coverageTerm{}
	for _, coverageTermVal := range coverageTerms {
		subCoverageKey, err := subCoverageIDsCodec.Encode(coverageTermVal.GetSubCoverageIds(), "")
		if err != nil {
			return errors.Wrapf(
				err, "failed to encode sub coverage ids for coverageTerm %s", coverageTermVal.GetKey(),
			)
		}
		coverageTermsBySubCoverageSet[subCoverageKey] = append(
			coverageTermsBySubCoverageSet[subCoverageKey], coverageTermVal,
		)
	}

	for subCoverageKey, coverageTermsInGroup := range coverageTermsBySubCoverageSet {
		// Collect all exposure entities from this group of coverage terms
		var allExposureEntities []*ExposureEntity
		for _, coverageTerm := range coverageTermsInGroup {
			allExposureEntities = append(allExposureEntities, coverageTerm.GetExposureEntities()...)
		}

		if len(allExposureEntities) == 0 {
			continue // No exposure entities to validate
		}

		// Determine variation along type and id axes. We calculate both flags up-front –
		// the switch below short-circuits as soon as `variesByType` is true, i.e. the
		// `variesById` value is ignored in that branch. Doing the combined computation
		// in one helper keeps the traversal of `allExposureEntities` to a single pass.
		variesByType, variesById := getExposureEntityVariation(allExposureEntities)

		// Apply validation rules based on the support matrix
		switch {
		case !variesByType && !variesById:
			// Pattern 1: not yet supported – list MUST have at most one entity.
			if len(allExposureEntities) > 1 {
				first := allExposureEntities[0]
				return errors.Newf(
					"coverage terms with sub-coverages %s do not vary by type or id (entity %s); "+
						"exposure entities list must have at most one entity as this case is not supported",
					subCoverageKey, first.Key(),
				)
			}
		case !variesByType && variesById:
			// Pattern 2: supported – one entry per id of the same type.
			// No additional validation required.
		case variesByType:
			// Patterns 3 & 4: variation by type is not supported.
			return errors.Newf(
				"coverage terms with sub-coverages %s vary by type, which is not supported", subCoverageKey,
			)
		}
	}

	return nil
}

// getExposureEntityVariation walks the slice once and reports whether the
// exposure entities vary by (a) type and (b) id. The reference point is always
// the first entity – any subsequent difference toggles the corresponding
// boolean.
func getExposureEntityVariation(entities []*ExposureEntity) (variesByType, variesById bool) {
	if len(entities) <= 1 {
		return false, false
	}

	refType := entities[0].GetType()
	refId := entities[0].GetId()

	for _, e := range entities[1:] {
		if e.GetType() != refType {
			variesByType = true
		}
		if e.GetId() != refId {
			variesById = true
		}

		// Early exit if both are true
		if variesByType && variesById {
			return
		}
	}
	return
}

func validateCoverageCriteriaAgainstPolicies(
	coverageCriteria *CoverageCriteria, policies map[string]*Policy, programType insurancecoreproto.ProgramType,
) error {
	for _, limit := range coverageCriteria.GetLimits() {
		if err := validateCoverageTermAgainstPolicies(limit, policies, programType); err != nil {
			return errors.Wrapf(err, "failed to validate limit with id %s against policies", limit.GetId())
		}
	}

	for _, deductible := range coverageCriteria.GetDeductibles() {
		if err := validateCoverageTermAgainstPolicies(deductible, policies, programType); err != nil {
			return errors.Wrapf(
				err, "failed to validate deductible with key %s against policies", deductible.GetKey(),
			)
		}
	}

	return nil
}

func validateCoverageTermAgainstPolicies(
	coverageTerm coverageTerm, policies map[string]*Policy, programType insurancecoreproto.ProgramType,
) error {
	if err := validateExposureEntitiesAgainstPolicies(
		coverageTerm.GetExposureEntities(), coverageTerm.GetSubCoverageIds(), policies, programType,
	); err != nil {
		return errors.Wrap(err, "failed to validate coverageTerm exposure entities against policies")
	}

	return nil
}

func validateExposureEntitiesAgainstPolicies(
	exposureEntities []*ExposureEntity, subCoverageIds []string, policies map[string]*Policy,
	programType insurancecoreproto.ProgramType,
) error {
	if len(exposureEntities) == 0 {
		return nil
	}

	exposureEntityToApplicablePoliciesMap, err := getPoliciesApplicableToExposureEntities(
		exposureEntities, subCoverageIds, policies,
	)
	if err != nil {
		return errors.Wrap(err, "failed to get applicable policies for exposure entities")
	}

	// Iterate directly over the slice to avoid constructing an additional map of
	// exposure entities. Complexity stays O(n), but we save one allocation and
	// a pass over the data.
	for _, entity := range exposureEntities {
		applicablePolicy := exposureEntityToApplicablePoliciesMap[entity.Key()]
		if err := validateExposureEntityAgainstPolicies(entity, applicablePolicy, programType); err != nil {
			return errors.Wrapf(
				err, "exposure entity with key %s validation against policies failed", entity.Key(),
			)
		}
	}

	return nil
}

// getPoliciesApplicableToExposureEntities returns a map where the key is the exposure entity key and the value is the
// policy object that is applicable to the exposure entity.
// NOTE: This currently assumes that the exposure entities are applicable to all sub coverages provided.
// However, this is not true. The following facts are true:
//  1. Each sub-coverage has its set of exposure entities. For eg: BI sub-coverage will have a set of exposure entities
//     {THIRD_PERSON}, while the Cargo sub-coverage will have a set of exposure entities {IN_TRANSIT_CARGO}.
//  2. A sub-coverage can be a part of only one policy.
//  3. A given exposure entity can only be applicable to one sub-coverage.
//  4. Given 1, 2 and 3, it can be concluded that a sub-coverage can be applicable to only one policy.
//
// Given the aforementioned facts, the correct way to solve this is:
//  1. For each exposure entity, get the sub-coverage id that is applicable to it.
//  2. For each sub-coverage id, get the policy that is applicable to it.
func getPoliciesApplicableToExposureEntities(
	exposureEntities []*ExposureEntity, subCoverageIds []string, policies map[string]*Policy,
) (map[string]*Policy, error) {
	// The key is the exposure entity key
	exposureEntitiesToApplicablePoliciesMap := map[string]*Policy{}

	applicablePoliciesMap, err := getPoliciesApplicableToSubCoverageIds(subCoverageIds, policies)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get applicable policies for sub coverage IDs")
	}
	// We do this since we assume that the exposure entities are applicable to all sub coverages provided.
	// If multiple policies are applicable to the sub-cov IDs, then we don't have a way to map the exposure entities
	// to the policies since we don't have a subCovID->exposureEntity mapping.
	if len(applicablePoliciesMap) > 1 {
		return nil, errors.New("exposure entities must be applicable to only one policy")
	}
	var applicablePolicy *Policy
	for _, policy := range applicablePoliciesMap {
		applicablePolicy = policy
		break
	}

	for _, exposureEntity := range exposureEntities {
		exposureEntitiesToApplicablePoliciesMap[exposureEntity.Key()] = applicablePolicy
	}

	return exposureEntitiesToApplicablePoliciesMap, nil
}

func getPoliciesApplicableToSubCoverageIds(
	subCoverageIds []string, policies map[string]*Policy,
) (map[string]*Policy, error) {
	applicablePoliciesMap := map[string]*Policy{} // key is policy number

	subCovIDToPolicyMap := make(map[string]*Policy)
	for _, policy := range policies {
		for _, coverage := range policy.GetCoverages() {
			for _, subCov := range coverage.GetSubCoverages() {
				subCovIDToPolicyMap[subCov.Id] = policy
			}
		}
	}

	for _, subCoverageId := range subCoverageIds {
		applicablePolicy, ok := subCovIDToPolicyMap[subCoverageId]
		if !ok {
			return nil, errors.Newf("no policy found for sub coverage id %s", subCoverageId)
		}
		applicablePoliciesMap[applicablePolicy.GetPolicyNumber()] = applicablePolicy
	}
	if len(applicablePoliciesMap) == 0 {
		return nil, errors.New("no applicable policies found for sub coverage ids")
	}

	return applicablePoliciesMap, nil
}

func validateExposureEntityAgainstPolicies(
	exposureEntity *ExposureEntity, applicablePolicy *Policy,
	programType insurancecoreproto.ProgramType,
) error {
	if err := validateExposureEntityAgainstPolicy(
		exposureEntity, applicablePolicy, programType,
	); err != nil {
		return errors.Wrapf(
			err, "exposure entity validation against policy %s failed", applicablePolicy.GetPolicyNumber(),
		)
	}

	return nil
}

func validateExposureEntityAgainstPolicy(
	exposureEntity *ExposureEntity, policy *Policy, programType insurancecoreproto.ProgramType,
) error {
	switch exposureEntity.GetType() { //exhaustive:enforce
	case ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE:
		if err := validateVehicleExposureEntityAgainstBizAutoPolicy(exposureEntity, policy, programType); err != nil {
			return errors.Wrapf(err, "vehicle exposure entity validation failed against biz auto policy")
		}
	case ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO:
		if err := validateTerminalCargoExposureEntityAgainstFleetPolicy(exposureEntity, policy, programType); err != nil {
			return errors.Wrapf(err, "terminal cargo exposure entity validation failed against fleet policy")
		}
	default:
		return errors.Newf("unsupported exposure entity type %s", exposureEntity.GetType())
	}

	return nil
}

func validateVehicleExposureEntityAgainstBizAutoPolicy(
	exposureEntity *ExposureEntity, policy *Policy, programType insurancecoreproto.ProgramType,
) error {
	if programType != insurancecoreproto.ProgramType_ProgramType_BusinessAuto {
		return errors.Newf(
			"exposure entity type %s is not supported for program type %s",
			exposureEntity.GetType(), programType,
		)
	}

	bizAutoProgramData := policy.GetProgramData().GetBusinessAutoData()
	vehicles := bizAutoProgramData.GetVehicles()
	var found bool
	for _, vehicle := range vehicles {
		if vehicle.GetVin() == exposureEntity.GetId() {
			found = true
			break
		}
	}
	if !found {
		return errors.Newf(
			"vehicle with vin %s not found for program type %s",
			exposureEntity.GetId(), programType,
		)
	}

	return nil
}

func validateTerminalCargoExposureEntityAgainstFleetPolicy(
	exposureEntity *ExposureEntity, policy *Policy, programType insurancecoreproto.ProgramType,
) error {
	if programType != insurancecoreproto.ProgramType_ProgramType_Fleet {
		return errors.Newf(
			"exposure entity type %s is not supported for program type %s",
			exposureEntity.GetType(), programType,
		)
	}

	fleetProgramData := policy.GetProgramData().GetFleetData()
	terminalLocations := fleetProgramData.GetCompany().GetTerminalLocations()
	if len(terminalLocations) == 0 {
		return errors.New("no terminal locations found")
	}

	var found bool
	for _, terminalLocation := range terminalLocations {
		if terminalLocation.GetKey() == exposureEntity.GetId() {
			found = true
			break
		}
	}
	if !found {
		return errors.Newf(
			"terminal cargo exposure entity with id %s not found in fleet policy with number %s",
			exposureEntity.GetId(), policy.GetPolicyNumber(),
		)
	}

	return nil
}
