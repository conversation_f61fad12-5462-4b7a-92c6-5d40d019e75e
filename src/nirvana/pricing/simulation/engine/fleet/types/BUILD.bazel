load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "types",
    srcs = [
        "modifier.go",
        "pricing_input.go",
        "pricing_output.go",
        "runner_input.go",
    ],
    importpath = "nirvanatech.com/nirvana/pricing/simulation/engine/fleet/types",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/problem",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/pricing/simulation/engine/common",
        "//nirvana/rating/adaptors/fleet_adaptor/common",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/rtypes",
        "//nirvana/rating/utils",
        "@com_github_google_uuid//:uuid",
    ],
)
