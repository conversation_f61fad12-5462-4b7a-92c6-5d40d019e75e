package types

import (
	"github.com/google/uuid"

	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
)

// PricingOutputMetadata bundles the subset of submission fields plus the artifact file key required to generate the
// simulation output.  Passing a narrow wrapper keeps the builder decoupled from
// the full submission schema while avoiding ever-growing parameter lists as we
// add more submission-level attributes.
type PricingOutputMetadata struct {
	SubmissionID    uuid.UUID
	PackageType     string
	ArtifactFileKey string
	ModelKey        string
}

type PricingOutput struct {
	PricingOutputMetadata

	// Fleet and Policy Metrics
	FleetPowerUnitCount float64
	TIV                 float64

	// Surcharge Premiums
	ByPuSurchargePremium       *float64
	TotalSurchargePremiumMi    *float64
	TotalByCovSurchargePremium *float64

	// Negotiation and Exemption Flags
	NegotiatedRateFlag       bool
	Rule15ExemptionFlag      bool
	Rule15ExemptionThreshold float64
	Rule15ExemptionPremium   float64

	// Premium Calculations
	TotalPolicyPremium                  float64
	LiabPolicyPremium                   float64
	LiabPolicyPremiumPpu                float64
	PhysPolicyPremium                   float64
	PhysPolicyPremiumPtiv               float64
	GlPolicyPremium                     float64
	MtcPolicyPremium                    float64
	MtcPolicyPremiumPpu                 float64
	FlatPolicyPremium                   float64
	NonFlatLiabPolicyPremium            float64
	NonFlatLiabPolicyPremiumPpu         float64
	NonFlatPhysPolicyPremium            float64
	NonFlatPhysPolicyPremiumPtiv        float64
	NegotiatedNonFlatLiabPolicyPremium  float64
	NegotiatedNonFlatPhysPolicyPremium  float64
	TraditionalNonFlatLiabPolicyPremium float64
	TraditionalNonFlatPhysPolicyPremium float64

	// Collision and Comprehensive Premiums
	CollisionPremium     *float64
	ComprehensivePremium *float64

	// Surcharge Premiums by State
	TotalSurchargePremium          *float64
	TotalSurpTaxandStampFee        *float64
	TotalSurchargePremiumKy        *float64
	TotalInsuranceSurchargePremium *float64
	AutoInsuranceSurchargePremium  *float64
	GLInsuranceSurchargePremium    *float64
	MTCInsuranceSurchargePremium   *float64
	AutoLGPTSurchargePremium       *float64
	GLLGPTSurchargePremium         *float64
	MTCLGPTSurchargePremium        *float64

	// Tax and Stamping Fees
	ALPolicySurplusLinesTax  *float64
	ALPolicyStampingFee      *float64
	APDPolicySurplusLinesTax *float64
	APDPolicyStampingFee     *float64
	GLPolicySurplusLinesTax  *float64
	GLPolicyStampingFee      *float64
	MTCPolicySurplusLinesTax *float64
	MTCPolicyStampingFee     *float64

	// Policy Premiums (Unmodified)
	TotalPolicyPremiumUnmodified *float64

	// Error
	Error string
}

type PricingOutputBuilder struct {
	pricingOutput *PricingOutput
}

func NewPricingOutputBuilder() *PricingOutputBuilder {
	return &PricingOutputBuilder{
		pricingOutput: &PricingOutput{},
	}
}

func (b *PricingOutputBuilder) WithPricingOutputMetadata(metadata *PricingOutputMetadata) *PricingOutputBuilder {
	b.pricingOutput.PricingOutputMetadata = *metadata

	return b
}

func (b *PricingOutputBuilder) WithError(err error) *PricingOutputBuilder {
	b.pricingOutput.Error = err.Error()

	return b
}

func (b *PricingOutputBuilder) WithModelOutput(modelOutput *model_output.ModelOutput) *PricingOutputBuilder {
	b.pricingOutput.FleetPowerUnitCount = modelOutput.GetFleetPowerUnitCount()
	b.pricingOutput.TIV = modelOutput.GetTIV()

	b.pricingOutput.ByPuSurchargePremium = modelOutput.GetByPuSurchargePremium()
	b.pricingOutput.TotalSurchargePremiumMi = modelOutput.GetTotalSurchargePremiumMi()
	b.pricingOutput.TotalByCovSurchargePremium = modelOutput.GetTotalByCovSurchargePremium()

	b.pricingOutput.NegotiatedRateFlag = modelOutput.GetNegotiatedRateFlag()
	b.pricingOutput.Rule15ExemptionFlag = modelOutput.GetRule15ExemptionFlag()
	b.pricingOutput.Rule15ExemptionThreshold = modelOutput.GetRule15ExemptionThreshold()
	b.pricingOutput.Rule15ExemptionPremium = modelOutput.GetRule15ExemptionPremium()

	b.pricingOutput.TotalPolicyPremium = modelOutput.GetTotalPolicyPremium()
	b.pricingOutput.LiabPolicyPremium = modelOutput.GetLiabPolicyPremium()
	b.pricingOutput.LiabPolicyPremiumPpu = modelOutput.GetLiabPolicyPremiumPpu()
	b.pricingOutput.PhysPolicyPremium = modelOutput.GetPhysPolicyPremium()
	b.pricingOutput.PhysPolicyPremiumPtiv = modelOutput.GetPhysPolicyPremiumPtiv()
	b.pricingOutput.GlPolicyPremium = modelOutput.GetGlPolicyPremium()
	b.pricingOutput.MtcPolicyPremium = modelOutput.GetMtcPolicyPremium()
	b.pricingOutput.MtcPolicyPremiumPpu = modelOutput.GetMtcPolicyPremiumPpu()
	b.pricingOutput.FlatPolicyPremium = modelOutput.GetFlatPolicyPremium()
	b.pricingOutput.NonFlatLiabPolicyPremium = modelOutput.GetNonFlatLiabPolicyPremium()
	b.pricingOutput.NonFlatLiabPolicyPremiumPpu = modelOutput.GetNonFlatLiabPolicyPremiumPpu()
	b.pricingOutput.NonFlatPhysPolicyPremium = modelOutput.GetNonFlatPhysPolicyPremium()
	b.pricingOutput.NonFlatPhysPolicyPremiumPtiv = modelOutput.GetNonFlatPhysPolicyPremiumPtiv()
	b.pricingOutput.NegotiatedNonFlatLiabPolicyPremium = modelOutput.GetNegotiatedNonFlatLiabPolicyPremium()
	b.pricingOutput.NegotiatedNonFlatPhysPolicyPremium = modelOutput.GetNegotiatedNonFlatPhysPolicyPremium()
	b.pricingOutput.TraditionalNonFlatLiabPolicyPremium = modelOutput.GetTraditionalNonFlatLiabPolicyPremium()
	b.pricingOutput.TraditionalNonFlatPhysPolicyPremium = modelOutput.GetTraditionalNonFlatPhysPolicyPremium()

	b.pricingOutput.CollisionPremium = modelOutput.GetCollisionPremium()
	b.pricingOutput.ComprehensivePremium = modelOutput.GetComprehensivePremium()

	b.pricingOutput.TotalSurchargePremium = modelOutput.GetTotalSurchargePremium()
	b.pricingOutput.TotalSurpTaxandStampFee = modelOutput.GetTotalSurpTaxandStampFee()
	b.pricingOutput.TotalSurchargePremiumKy = modelOutput.GetTotalSurchargePremiumKy()
	b.pricingOutput.TotalInsuranceSurchargePremium = modelOutput.GetTotalInsuranceSurchargePremium()
	b.pricingOutput.AutoInsuranceSurchargePremium = modelOutput.GetAutoInsuranceSurchargePremium()
	b.pricingOutput.GLInsuranceSurchargePremium = modelOutput.GetGLInsuranceSurchargePremium()
	b.pricingOutput.MTCInsuranceSurchargePremium = modelOutput.GetMTCInsuranceSurchargePremium()
	b.pricingOutput.AutoLGPTSurchargePremium = modelOutput.GetAutoLGPTSurchargePremium()
	b.pricingOutput.GLLGPTSurchargePremium = modelOutput.GetGLLGPTSurchargePremium()
	b.pricingOutput.MTCLGPTSurchargePremium = modelOutput.GetMTCLGPTSurchargePremium()

	// Tax and Stamping Fees
	b.pricingOutput.ALPolicySurplusLinesTax = modelOutput.GetALPolicySurplusLinesTax()
	b.pricingOutput.ALPolicyStampingFee = modelOutput.GetALPolicyStampingFee()
	b.pricingOutput.APDPolicySurplusLinesTax = modelOutput.GetAPDPolicySurplusLinesTax()
	b.pricingOutput.APDPolicyStampingFee = modelOutput.GetAPDPolicyStampingFee()
	b.pricingOutput.GLPolicySurplusLinesTax = modelOutput.GetGLPolicySurplusLinesTax()
	b.pricingOutput.GLPolicyStampingFee = modelOutput.GetGLPolicyStampingFee()
	b.pricingOutput.MTCPolicySurplusLinesTax = modelOutput.GetMTCPolicySurplusLinesTax()
	b.pricingOutput.MTCPolicyStampingFee = modelOutput.GetMTCPolicyStampingFee()

	b.pricingOutput.TotalPolicyPremiumUnmodified = modelOutput.GetTotalPolicyPremiumUnmodified()

	return b
}

func (b *PricingOutputBuilder) Build() *PricingOutput {
	return b.pricingOutput
}
