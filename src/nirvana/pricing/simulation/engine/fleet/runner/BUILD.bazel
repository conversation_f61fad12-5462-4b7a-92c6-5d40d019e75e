load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "runner",
    srcs = [
        "fx.go",
        "model_input.go",
        "new.go",
        "runner.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/pricing/simulation/engine/fleet/runner",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/external_data_management/clients_management",
        "//nirvana/external_data_management/context_management",
        "//nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/pricing/simulation/db",
        "//nirvana/pricing/simulation/engine/common",
        "//nirvana/pricing/simulation/engine/common/files",
        "//nirvana/pricing/simulation/engine/fleet/modifiers",
        "//nirvana/pricing/simulation/engine/fleet/types",
        "//nirvana/pricing/simulation/models",
        "//nirvana/quoting/pricing_client_wrapper",
        "//nirvana/rating/adaptors/fleet_adaptor",
        "//nirvana/rating/adaptors/fleet_adaptor/common",
        "//nirvana/rating/rtypes",
        "//nirvana/rating/utils",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "runner_test",
    srcs = ["runner_test.go"],
    embed = [":runner"],
    deps = [
        "//nirvana/common-go/us_states",
        "//nirvana/rating/rtypes",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
    ],
)
