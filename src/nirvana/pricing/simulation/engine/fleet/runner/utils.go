package runner

import (
	"fmt"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/pricing/simulation/engine/fleet/types"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
	"nirvanatech.com/nirvana/rating/rtypes"
	rating_utils "nirvanatech.com/nirvana/rating/utils"
)

func buildPricingOutputFromPricingRun(
	metadata *types.PricingOutputMetadata,
	modelOutput *model_output.ModelOutput,
	err error,
) *types.PricingOutput {
	builder := types.NewPricingOutputBuilder().WithPricingOutputMetadata(metadata)
	if err != nil {
		return builder.WithError(err).Build()
	}

	if err := validatePricingOutputMetadata(metadata); err != nil {
		return builder.WithError(err).Build()
	}

	if modelOutput == nil {
		return builder.WithError(errors.New("unexpected undefined model output")).Build()
	}

	return builder.WithModelOutput(modelOutput).Build()
}

func validatePricingOutputMetadata(metadata *types.PricingOutputMetadata) error {
	if metadata.ModelKey == "" {
		return errors.New("unexpected undefined model key")
	}

	if metadata.PackageType == "" {
		return errors.New("unexpected undefined package type")
	}

	if metadata.ArtifactFileKey == "" {
		return errors.New("unexpected undefined artifact file key")
	}

	return nil
}

func buildModelRunConfig(
	submission *application.SubmissionObject,
	modelKey rtypes.ModelKey,
) (*rating_utils.ModelRunConfig, error) {
	if submission == nil {
		return nil, errors.New("submission was found nil")
	}

	modelPinConfig := submission.ModelPinConfig
	if submission.ModelPinConfig == nil {
		return nil, errors.New("model pin config was found nil")
	}

	rmlConfig := modelPinConfig.RateML

	rmlConfig.USState = modelKey.State()
	rmlConfig.Provider = modelKey.Provider()
	rmlConfig.Version = modelKey.Version()

	applicationConfig := submission.ModelPinConfig.Application

	ratingTierRecordDates, err := rating_utils.NewRecordDatesWithDefaultsV1(
		submission.ModelPinConfig.Data.SentryInputsDumpDate,
		submission.ModelPinConfig.Data.SentryInputsDate,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create new rating record dates")
	}

	return &rating_utils.ModelRunConfig{
		UseDriverMVRs:    true,
		RateMLConfig:     rmlConfig,
		RateMLFlags:      rmlConfig.Flags,
		ApplicationFlags: applicationConfig.Flags,
		Bugs:             rmlConfig.Bugs,
		Hyperparams: rating_utils.ModelRunHyperparameters{
			RatingTierRecordDates: *ratingTierRecordDates,
		},
		PackageType: submission.PackageType,
	}, nil
}

func getArtifactFileKey(
	dataContextID, submissionID uuid.UUID,
	modelKey rtypes.ModelKey,
) string {
	currentTime := time.Now().Format(time.RFC3339)

	return fmt.Sprintf(
		"SIMULATION-DataContextID:%s-SubmissionID:%s-Model:%s-%s.json",
		dataContextID.String(),
		submissionID.String(),
		modelKey.String(),
		currentTime,
	)
}

func getSimulationPricingRunOutputFileKey(
	simulationRunId uuid.UUID,
	submissionID uuid.UUID,
	modelKey rtypes.ModelKey,
) string {
	return fmt.Sprintf(
		"%s/output_%s_%s.json",
		simulationRunId.String(),
		submissionID.String(),
		modelKey.String(),
	)
}
