package endorsementapp

import (
	"errors"
)

var (
	ErrParseIBInternalID   = errors.New("invalid IB internal ID")
	ErrParseAgencyID       = errors.New("invalid agency ID")
	ErrParseSubmissionID   = errors.New("invalid submission ID")
	ErrInvalidBody         = errors.New("invalid request body")
	ErrInvalidRequestState = errors.New("invalid endorsement request state")

	ErrGetProducerMarketer          = errors.New("cannot fetch producer and marketer")
	ErrGetUnderwriterID             = errors.New("cannot fetch underwriter ID")
	ErrGetPackageAndPremium         = errors.New("cannot fetch package and premium details")
	ErrGetRequest                   = errors.New("cannot fetch endorsement request")
	ErrGetRequestList               = errors.New("cannot fetch endorsement request list")
	ErrGetQuotingPricingContextList = errors.New("cannot fetch quoting pricing context list")
	ErrInsertRequest                = errors.New("cannot insert endorsement request")
	ErrGetMailingAddressChange      = errors.New("cannot fetch mailing address change")
	ErrGetTerminalAddressChange     = errors.New("cannot fetch terminal address change")
	ErrInsertReview                 = errors.New("cannot insert endorsement review")
	ErrExtractAutoPolicy            = errors.New("cannot extract auto liability policy")
	ErrUpdateRequest                = errors.New("cannot update endorsement request")
	ErrSubmitUWReview               = errors.New("cannot submit for UW review")
	ErrListInsuranceBundles         = errors.New("cannot list insurance bundles")
	ErrGetChargeDistribution        = errors.New("cannot get charge distribution")
	ErrFilterAddress                = errors.New("cannot filter address change from endorsement request")
	ErrTransformCoverageChanges     = errors.New("cannot transform coverage changes")
	ErrGetDOTNumber                 = errors.New("DOT number not found")
	ErrMVRPullFailed                = errors.New("MVR pull failed for one or more added drivers")
	ErrGenerateQuote                = errors.New("cannot generate quote")
	ErrGetDriverChanges             = errors.New("cannot fetch driver changes")
)
