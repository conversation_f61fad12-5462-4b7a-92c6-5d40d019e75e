package state_machine

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/business-auto/converters"
	"nirvanatech.com/nirvana/business-auto/model"
	ba_proto "nirvanatech.com/nirvana/business-auto/model/proto"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/type_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	ib_model "nirvanatech.com/nirvana/insurance-bundle/model"
	charges "nirvanatech.com/nirvana/insurance-bundle/model/charges"
	ib_service "nirvanatech.com/nirvana/insurance-bundle/service"
	insurance_core "nirvanatech.com/nirvana/insurance-core/proto"
	insuredmodel "nirvanatech.com/nirvana/insured/model"
	policyutils "nirvanatech.com/nirvana/policy/business_auto"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
	"nirvanatech.com/nirvana/policy_common/policy/shared"
)

// validateAndExtractFEIN validates that FEIN is present when required (non-draft mode)
// and returns the FEIN value or empty string if allowed.
func validateAndExtractFEIN(app *model.BusinessAutoApp, isDraft bool) (string, error) {
	if !isDraft && app.CompanyInfo.FEIN == nil {
		return "", errors.New("FEIN is required for non-draft insurance bundle creation")
	}
	return pointer_utils.StringValOr(app.CompanyInfo.FEIN, ""), nil
}

// getProtoAddressFromCompanyInfo safely creates a proto.Address from BusinessAutoApp company info,
// handling nil address cases and setting default values for Nation and CountyCode.
func getProtoAddressFromCompanyInfo(app *model.BusinessAutoApp) (*proto.Address, error) {
	if app.CompanyInfo.Address == nil {
		return nil, errors.New("address is required for insurance bundle creation")
	}

	return &proto.Address{
		Nation:     pointer_utils.ToPointer(us_states.DefaultCountry),
		State:      pointer_utils.ToPointer(app.CompanyInfo.Address.State),
		City:       pointer_utils.ToPointer(app.CompanyInfo.Address.City),
		Street:     pointer_utils.ToPointer(app.CompanyInfo.Address.Street),
		ZipCode:    pointer_utils.ToPointer(app.CompanyInfo.Address.Zip),
		CountyCode: nil, // Business Auto doesn't collect county code from users
	}, nil
}

func createBusinessAutoIB(
	ctx context.Context,
	ibService ib_service.InsuranceBundleManagerClient,
	formsWrapper forms.FormWrapper,
	pricingWrapper quoting.PricingWrapper,
	app *model.BusinessAutoApp,
	compilation compilation.FormsCompilation,
) error {
	ibRequest, err := CreateIBUpsertRequest(ctx, app, compilation, formsWrapper, pricingWrapper, false)
	if err != nil {
		return errors.Wrapf(err, "failed to create Business Auto IB upsert request for application %s", app.ID.String())
	}

	upsertResponse, err := ibService.UpsertInsuranceBundle(ctx, ibRequest)
	if err != nil {
		return errors.Wrapf(err, "failed to upsert Business Auto insurance bundle for application %s", app.ID.String())
	}

	log.Info(ctx, "Successfully created Business Auto Insurance Bundle",
		log.String("appID", app.ID.String()),
		log.String("ibInternalId", upsertResponse.InsuranceBundleInternalId),
	)

	return nil
}

func CreateIBUpsertRequest(
	ctx context.Context,
	app *model.BusinessAutoApp,
	signaturePacket compilation.FormsCompilation,
	formWrapper forms.FormWrapper,
	pricingWrapper quoting.PricingWrapper,
	isDraft bool, // This is used to determine if the IB is being created as a draft or not.
) (*ib_service.UpsertInsuranceBundleRequest, error) {
	policies, err := getPolicies(ctx, pricingWrapper, app, signaturePacket, formWrapper, isDraft)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get policies for Business Auto application %s", app.ID.String())
	}

	limits, err := getLimits(*app.CoveragesInfo)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get limits for Business Auto application %s", app.ID.String())
	}

	primaryInsured, err := getInsuredInfo(ctx, app, isDraft)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get insured info for Business Auto application %s", app.ID.String())
	}

	insuranceBundle := &ib_model.InsuranceBundle{
		Version:        0,
		ProgramType:    insurance_core.ProgramType_ProgramType_BusinessAuto,
		DefaultCarrier: insurance_core.InsuranceCarrier_InsuranceCarrier_MSTransverse,
		DefaultSeller: &insurance_core.SellerInfo{
			AgencyID: app.AgencyID.String(),
		},
		DefaultEffectiveDuration: &proto.Interval{
			Start: timestamppb.New(app.EffectiveDurationStart),
			End:   timestamppb.New(app.EffectiveDurationEnd),
		},
		Metadata: &ib_model.InsuranceBundleMetadata{
			RootApplicationId: app.ID.String(),
			// NOTE: IB validates a need for a bindable submission ID.
			// Business Auto doesn't have a bindable submission ID, so we're using a nil UUID.
			RootBindableSubmissionId: uuid.Nil.String(),
		},
		FormInfo: getFormInfo(ctx, signaturePacket, isDraft),
		Segments: []*ib_model.InsuranceBundleSegment{
			{
				Id: uuid.New().String(),
				Interval: &proto.Interval{
					Start: timestamppb.New(app.EffectiveDurationStart),
					End:   timestamppb.New(app.EffectiveDurationEnd),
				},
				PrimaryInsured: primaryInsured,
				Policies:       policies,
				CoverageCriteria: &ib_model.CoverageCriteria{
					Limits:              limits,
					Deductibles:         getDeductibles(*app.CoveragesInfo),
					CombinedDeductibles: nil, // NOTE: Business Auto doesn't have combined deductibles at the moment.
				},
			},
		},
		CarrierAdmittedType: insurance_core.CarrierAdmittedType_CarrierAdmittedType_NonAdmitted, // TODO: Verify this
	}

	contactInfo := insuredmodel.ContactInfo{}
	if app.TelematicsInfo != nil {
		contactInfo = insuredmodel.ContactInfo{
			Email: app.TelematicsInfo.Email,
			Phone: "", // Not captured today, hence empty.
		}
	}

	fein, err := validateAndExtractFEIN(app, isDraft)
	if err != nil {
		return nil, err
	}
	address, err := getProtoAddressFromCompanyInfo(app)
	if err != nil {
		return nil, err
	}
	insured := &insuredmodel.Insured{
		// Using the actual address from the CompanyInfo
		Address: address,
		Name: &insurance_core.InsuredName{
			BusinessName: app.CompanyInfo.Name,
		},
		ContactInfo: &contactInfo,
		ExternalIdentifier: &insurance_core.InsuredIdentifier{
			Type:  insurance_core.InsuredIdentifierType_InsuredIdentifierType_FEIN,
			Value: []string{fein},
		},
	}

	return &ib_service.UpsertInsuranceBundleRequest{
		InsuranceBundle: insuranceBundle,
		Insured:         insured,
	}, nil
}

func getPolicies(
	ctx context.Context,
	pricingWrapper quoting.PricingWrapper,
	app *model.BusinessAutoApp,
	signaturePacket compilation.FormsCompilation,
	formWrapper forms.FormWrapper,
	isDraft bool,
) (map[string]*ib_model.Policy, error) {
	policies := make(map[string]*ib_model.Policy)
	policyPacketIdsPtr := signaturePacket.Metadata().CoverageIdsMap
	if policyPacketIdsPtr == nil {
		return nil, errors.New("signature packet metadata does not contain policy packet IDs")
	}
	policyPacketIds := *policyPacketIdsPtr
	if len(policyPacketIds) == 0 {
		return nil, errors.New("no policy packet IDs found in the signature packet metadata")
	}

	var apdCoverage model.PrimaryCoverage
	var hasApdCoverage bool
	for _, coverage := range app.CoveragesInfo.PrimaryCoverages {
		if coverage.ID == app_enums.CoverageAutoPhysicalDamage {
			apdCoverage = coverage
			hasApdCoverage = true
			break
		}
	}

	for _, coverage := range app.CoveragesInfo.PrimaryCoverages {
		switch coverage.ID {
		case app_enums.CoverageAutoPhysicalDamage:
			log.Info(ctx, "Skipping coverage auto physical damage", log.String("appID", app.ID.String()))
			continue
		case app_enums.CoverageAutoLiability:
			policyNumberImpl, err := policyutils.GeneratePolicyNumber(
				app_enums.CoverageAutoLiability,
				app.EffectiveDurationStart,
				string(app.ShortID),
			)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to generate policy number for Business Auto application %s", app.ID.String())
			}
			policyNumber := policyNumberImpl.String()

			// Get pricing context using the selected pricing context ID from the application
			var pricingContext *quoting.PricingContext
			if app.SelectedQuotingPricingContextID != nil {
				pricingContext, err = pricingWrapper.GetQuotingPricingContextById(ctx, app.SelectedQuotingPricingContextID.String())
				if err != nil {
					log.Error(ctx, "failed to get quoting pricing context",
						log.String("pricingContextID", app.SelectedQuotingPricingContextID.String()),
						log.Err(err))
					return nil, errors.Wrapf(err, "failed to get pricing context for application %s", app.ID.String())
				}
			} else {
				log.Warn(ctx, "no pricing context ID found for application", log.String("appID", app.ID.String()))
			}
			charges := &charges.ChargeList{}
			if pricingContext != nil && pricingContext.Charges != nil && pricingContext.Charges.PolicyCharges != nil {
				charges = pricingContext.Charges.PolicyCharges[policyNumber]
			}

			policyPacketID := policyPacketIds[coverage.ID]
			if policyPacketID == nil {
				return nil, errors.Errorf("policy packet ID not found for coverage %s", coverage.ID)
			}
			formComp, err := formWrapper.GetFormCompilationById(ctx, *policyPacketID)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to get form compilation for policy packet ID %s", *policyPacketID)
			}

			alCoverage := coverage

			coverages := getCoverages(alCoverage)
			if hasApdCoverage {
				coverages = getCoverages(alCoverage, apdCoverage)
			}

			policies[policyNumber] = &ib_model.Policy{
				PolicyNumber: policyNumber,
				State:        ib_model.PolicyState_PolicyState_Created,
				// NOTE: AL Policy contains both AL and APD coverages.
				Coverages: coverages,
				EffectiveInterval: &proto.Interval{
					Start: timestamppb.New(app.EffectiveDurationStart),
					End:   timestamppb.New(app.EffectiveDurationEnd),
				},
				Metadata: &ib_model.PolicyMetadata{
					ApplicationId: app.ID.String(),
					// NOTE: IB validates a need for a bindable submission ID.
					// Business Auto doesn't have a bindable submission ID, so we're using a nil UUID.
					BindableSubmissionId: uuid.Nil.String(),
				},
				ProgramData:       getProgramData(ctx, app),
				WrittenExposure:   nil,
				ChargeAdjustments: nil,
				FormInfo:          getFormInfo(ctx, *formComp, isDraft),
				Charges:           charges,
				Clauses: &insurance_core.ClauseList{
					Clauses: []*insurance_core.Clause{},
				},
			}
		default:
			return nil, errors.Errorf("unknown coverage type: %s", coverage.ID)
		}
	}

	return policies, nil
}

func getFormInfo(
	ctx context.Context,
	formComp compilation.FormsCompilation,
	isDraft bool, // This is used to determine if the IB is being created as a draft or not.
) *insurance_core.FormInfo {
	formsListString := shared.GetFormCodeList(ctx, formComp)

	var formCompilationType insurance_core.FormCompilationType
	var documentHandleID string
	switch *formComp.Type() {
	// nolint:exhaustive
	case compilation.CompilationTypeSignaturePacket:
		formCompilationType = insurance_core.FormCompilationType_FormCompilationTypeSignaturePacket
		if isDraft {
			// If the compilation is not frozen, we don't have a document handle ID.
			// We execute this code for draft IB in which case Signature packet state
			// is initialized. At this point we don't have a document handle ID.
			documentHandleID = ""
		} else {
			documentHandleID = formComp.DocumentHandleId().String()
		}
	case compilation.CompilationTypePolicy:
		formCompilationType = insurance_core.FormCompilationType_FormCompilationTypePolicy
		// Policy compilations are filled after bind, hence we do not have document handle id
		// when generating IB
		documentHandleID = ""
	}
	return &insurance_core.FormInfo{
		CoreForm: &insurance_core.FormCore{
			FormCompilationId:   formComp.Id().String(),
			FormCompilationType: formCompilationType,
			DocumentHandleId:    documentHandleID,
			FormCodes:           formsListString,
		},
		AdditionalForms: nil,
	}
}

func getInsuredInfo(ctx context.Context, app *model.BusinessAutoApp, isDraft bool) (*insurance_core.Insured, error) {
	fein, err := validateAndExtractFEIN(app, isDraft)
	if err != nil {
		return nil, err
	}
	address, err := getProtoAddressFromCompanyInfo(app)
	if err != nil {
		return nil, err
	}
	return &insurance_core.Insured{
		// Using the actual address from the CompanyInfo
		Address: address,
		Type:    insurance_core.InsuredType_InsuredType_PrimaryInsured,
		Name: &insurance_core.InsuredName{
			BusinessName: app.CompanyInfo.Name,
		},
		ExternalIdentifier: &insurance_core.InsuredIdentifier{
			Type:  insurance_core.InsuredIdentifierType_InsuredIdentifierType_FEIN,
			Value: []string{fein},
		},
	}, nil
}

func getProgramData(_ context.Context, app *model.BusinessAutoApp) *ib_model.ProgramData {
	companyInfo := &ba_proto.CompanyInfo{
		NoOfPowerUnits:                   pointer_utils.Int32ValOr(app.CompanyInfo.NoOfPowerUnits, 0),
		NoOfEmployees:                    pointer_utils.Int32ValOr(app.CompanyInfo.NoOfEmployees, 0),
		PrimaryIndustryClassification:    converters.ConvertIndustryType(app.CompanyInfo.PrimaryIndustryClassification),
		SecondaryIndustryClassifications: converters.ConvertIndustryTypeSlice(app.CompanyInfo.SecondaryIndustryClassifications),
	}

	vehicles := make([]*ba_proto.VehicleData, len(*app.VehiclesInfo))
	for i, vehicle := range *app.VehiclesInfo {
		vehicles[i] = &ba_proto.VehicleData{
			Vin:                              vehicle.VIN,
			Year:                             vehicle.Year,
			Make:                             vehicle.Make,
			Model:                            vehicle.Model,
			VehicleType:                      insurance_core.VehicleType(vehicle.VehicleType),
			WeightClass:                      converters.ConvertWeightClass(vehicle.WeightClass),
			SpecialtyVehicleType:             converters.ConvertSpecialtyVehicleType(vehicle.SpecialtyVehicleType),
			VehicleUse:                       converters.ConvertVehicleUse(vehicle.VehicleUse),
			BusinessUse:                      converters.ConvertBusinessUse(vehicle.BusinessUse),
			TrailerType:                      converters.ConvertTrailerType(vehicle.TrailerType),
			StateUsage:                       converters.ConvertStateUsage(vehicle.StateUsage),
			RadiusClassification:             converters.ConvertRadiusClassification(vehicle.RadiusClassification),
			StatedValue:                      type_utils.ConvertPointer(vehicle.StatedValue, func(val float64) float32 { return float32(val) }),
			ApdDeductible:                    vehicle.APDDeductible,
			PrincipalGaragingLocationZipCode: vehicle.PrincipalGaragingLocationZipCode,
			IsGlassLinedTankTruckOrTrailer:   vehicle.IsGlassLinedTankTruckOrTrailer,
			IsRefrigeratedTruckOrTrailer:     vehicle.IsRefrigeratedTruckOrTrailer,
			IsDoubleTrailer:                  vehicle.IsDoubleTrailer,
			HasAntiLockBrakes:                vehicle.HasAntiLockBrakes,
		}
	}

	filingsInfo := &ba_proto.FilingsInfo{}
	if app.FilingsInfo != nil {
		filingsInfo = &ba_proto.FilingsInfo{
			HasMultiStateFilings:  app.FilingsInfo.HasMultiStateFilings,
			HasSingleStateFilings: app.FilingsInfo.HasSingleStateFilings,
			HasFMCSAFilings:       app.FilingsInfo.HasFMCSAFilings,
			HasDOTFilings:         app.FilingsInfo.HasDOTFilings,
		}
	}

	return &ib_model.ProgramData{
		Data: &ib_model.ProgramData_BusinessAutoData{
			BusinessAutoData: &ba_proto.ProgramData{
				CompanyInfo:           companyInfo,
				Vehicles:              vehicles,
				FilingsInfo:           filingsInfo,
				UnderwritingOverrides: convertUnderwritingOverrides(app.UnderwritingOverrides),
			},
		},
	}
}

func getCoverages(coverages ...model.PrimaryCoverage) []*ib_model.Coverage {
	retval := make([]*ib_model.Coverage, 0, len(coverages))
	for _, coverage := range coverages {
		retval = append(retval, &ib_model.Coverage{
			Id:          coverage.ID.String(),
			DisplayName: app_enums.GetCoverageLabel(coverage.ID),
			SubCoverages: func() []*ib_model.SubCoverage {
				subCoverages := make([]*ib_model.SubCoverage, len(coverage.SubCoverageIDs))
				for i, subCovID := range coverage.SubCoverageIDs {
					subCoverages[i] = &ib_model.SubCoverage{
						Id:          subCovID.String(),
						DisplayName: app_enums.GetCoverageLabel(subCovID),
					}
				}
				return subCoverages
			}(),
		})
	}
	return retval
}

func getLimits(coveragesInfo model.CoveragesInfo) ([]*ib_model.Limit, error) {
	limits := make([]*ib_model.Limit, 0, len(coveragesInfo.Limits))

	// Create a map of sub coverage IDs to primary coverage IDs
	subCoverageToPrimaryCoverage := make(map[app_enums.Coverage]app_enums.Coverage)
	for _, primaryCov := range coveragesInfo.PrimaryCoverages {
		for _, subCovID := range primaryCov.SubCoverageIDs {
			subCoverageToPrimaryCoverage[subCovID] = primaryCov.ID
		}
	}

	// Iterate over the limits and create the limits for the IB
	for _, limit := range coveragesInfo.Limits {
		// Convert the sub coverage IDs to strings
		subCoverageIds := slice_utils.Map(limit.SubCoverageIDs, func(subCovID app_enums.Coverage) string {
			return subCovID.String()
		})

		// If there are no sub coverage IDs, we skip the limit
		if len(limit.SubCoverageIDs) == 0 {
			continue
		}

		// Get the primary coverage ID, we use the subCoverageID itself if there is only one sub coverage ID as primary coverage ID
		var primaryCoverageID app_enums.Coverage
		if len(limit.SubCoverageIDs) == 1 {
			primaryCoverageID = limit.SubCoverageIDs[0]
		}

		// If there are multiple sub coverage IDs and the grouping is combined, we get the primary coverage ID from the
		// first sub coverage ID
		if len(limit.SubCoverageIDs) > 1 && limit.Grouping == ib_model.LimitGrouping_LimitGrouping_Combined {
			if primaryCov, exists := subCoverageToPrimaryCoverage[limit.SubCoverageIDs[0]]; exists {
				primaryCoverageID = primaryCov
			}
		}

		// Create the exposure entities for the limit
		// Currently we only support vehicle exposure entities in Biz Auto
		var exposureEntities []*ib_model.ExposureEntity
		if limit.VIN != nil {
			exposureEntities = append(exposureEntities, &ib_model.ExposureEntity{
				Id:   *limit.VIN,
				Type: ib_model.ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
			})
		}

		// Create the limit for the IB
		// If there's a VIN, make the ID unique by appending the VIN
		limitID := primaryCoverageID.String()
		if limit.VIN != nil {
			limitID = primaryCoverageID.String() + "_Vehicle_" + *limit.VIN
		}

		limits = append(limits, &ib_model.Limit{
			Id:               limitID,
			DisplayName:      app_enums.GetCoverageLabel(primaryCoverageID),
			SubCoverageIds:   subCoverageIds,
			Amount:           float64(limit.Amount),
			Grouping:         limit.Grouping,
			ExposureEntities: exposureEntities,
		})
	}

	return limits, nil
}

func getDeductibles(coveragesInfo model.CoveragesInfo) []*ib_model.Deductible {
	deductibles := make([]*ib_model.Deductible, 0, len(coveragesInfo.Deductibles))
	for _, deductible := range coveragesInfo.Deductibles {
		// Convert the sub coverage IDs to strings
		subCoverageIds := slice_utils.Map(deductible.SubCoverageIDs, func(subCovID app_enums.Coverage) string {
			return subCovID.String()
		})

		// Create the exposure entities for the deductible
		// Currently we only support vehicle exposure entities in Biz Auto
		var exposureEntities []*ib_model.ExposureEntity
		if deductible.VIN != nil {
			exposureEntities = append(exposureEntities, &ib_model.ExposureEntity{
				Id:   *deductible.VIN,
				Type: ib_model.ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
			})
		}

		deductibles = append(deductibles, &ib_model.Deductible{
			SubCoverageIds:   subCoverageIds,
			Amount:           float64(deductible.Amount),
			ExposureEntities: exposureEntities,
		})
	}

	return deductibles
}

func convertUnderwritingOverrides(overrides *model.UnderwritingOverrides) *ba_proto.UnderwritingOverrides {
	if overrides == nil {
		return nil
	}

	coveragesWithLossFreeCredits := slice_utils.Map(
		overrides.CoveragesWithLossFreeCredits,
		func(coverage app_enums.Coverage) string {
			return coverage.String()
		},
	)

	lossFreeCreditPercentage := type_utils.ConvertPointer(
		overrides.LossFreeCreditPercentage,
		func(val float64) float32 { return float32(val) },
	)

	return &ba_proto.UnderwritingOverrides{
		CoveragesWithLossFreeCredits: coveragesWithLossFreeCredits,
		LossFreeCreditPercentage:     lossFreeCreditPercentage,
		DriverFactor:                 convertDriverFactor(overrides.DriverFactor),
		ScheduleMods:                 convertScheduleMod(overrides.ScheduleMods),
		ExperienceMod:                convertExperienceMod(overrides.ExperienceMod),
		QualityRatingGrade:           type_utils.ConvertPointer(overrides.QualityRatingGrade, converters.ConvertQualityRatingGrade),
	}
}

func convertDriverFactor(driverFactor *model.DriverFactor) *ba_proto.DriverFactor {
	if driverFactor == nil || driverFactor.Factor == nil {
		return nil
	}

	return &ba_proto.DriverFactor{
		Factor: type_utils.ConvertPointer(driverFactor.Factor, func(val float64) float32 { return float32(val) }),
	}
}

func convertScheduleMod(scheduleMod *model.ScheduleMod) *ba_proto.ScheduleMod {
	if scheduleMod == nil || scheduleMod.Mods == nil || len(scheduleMod.Mods) == 0 {
		return nil
	}

	// Filter out nil modifiers first, then transform
	validMods := make(map[app_enums.Coverage]*float64)
	for coverage, modifier := range scheduleMod.Mods {
		if modifier != nil {
			validMods[coverage] = modifier
		}
	}

	if len(validMods) == 0 {
		return nil
	}

	mods := map_utils.Transform(validMods, func(coverage app_enums.Coverage, modifier *float64) float32 {
		return float32(*modifier)
	})

	result := make(map[string]float32, len(mods))
	for coverage, value := range mods {
		result[coverage.String()] = value
	}

	return &ba_proto.ScheduleMod{
		Mods: result,
	}
}

func convertExperienceMod(experienceMod *model.ExperienceMod) *ba_proto.ExperienceMod {
	if experienceMod == nil || experienceMod.Mods == nil || len(experienceMod.Mods) == 0 {
		return nil
	}

	// Filter out nil modifiers first, then transform
	validMods := make(map[app_enums.Coverage]*float64)
	for coverage, modifier := range experienceMod.Mods {
		if modifier != nil {
			validMods[coverage] = modifier
		}
	}

	if len(validMods) == 0 {
		return nil
	}

	mods := map_utils.Transform(validMods, func(coverage app_enums.Coverage, modifier *float64) float32 {
		return float32(*modifier)
	})

	result := make(map[string]float32, len(mods))
	for coverage, value := range mods {
		result[coverage.String()] = value
	}

	return &ba_proto.ExperienceMod{
		Mods: result,
	}
}
