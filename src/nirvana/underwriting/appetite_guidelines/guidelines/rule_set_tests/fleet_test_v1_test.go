package rule_set_tests

import (
	"testing"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/appetite_guidelines_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

// TestRuleService_LoadAndExecute_RuleSetV1 This test validates each rule path in the embedded rule_set.sql using
// minimal input facts. It uses the appetite_guidelines_fixture to seed
// the DB with the rule set and executes against version 1.
func TestRuleService_LoadAndExecute_RuleSetV1(t *testing.T) {
	t.Parallel()

	var env struct {
		fx.In

		// Load Internal/Fronting Guidelines RuleSet into DB
		*appetite_guidelines_fixture.Fixture
		Svc *guidelines.RuleService
	}

	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := t.Context()

	type internalCase struct {
		name     string
		input    *models.InternalInputFact
		expected []models.DecisionEntity
	}

	internalCases := []internalCase{
		{
			name:     "AutoLiability_LossesBurnRate_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleLossesBurnRate, Variant: ptr(models.VariantAutoLiability), Decision: models.DecisionPending}},
		},
		{
			name: "AutoLiability_LossesBurnRate_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.AlLossesBurnRate = null.Float32From(20000)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleLossesBurnRate, Variant: ptr(models.VariantAutoLiability), Decision: models.DecisionNone}},
		},
		{
			name: "AutoLiability_LossesBurnRate_Decline",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.AlLossesBurnRate = null.Float32From(20001)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleLossesBurnRate, Variant: ptr(models.VariantAutoLiability), Decision: models.DecisionDecline}},
		},
		{
			name:     "Utilization_HalfYearly_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUtilization, Variant: ptr(models.VariantHalfYearly), Decision: models.DecisionPending}},
		},
		{
			name: "Utilization_HalfYearly_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.HalfYearlyUtilization = null.Float32From(159999)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUtilization, Variant: ptr(models.VariantHalfYearly), Decision: models.DecisionNone}},
		},
		{
			name: "Utilization_HalfYearly_FurtherReview",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.HalfYearlyUtilization = null.Float32From(160000)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUtilization, Variant: ptr(models.VariantHalfYearly), Decision: models.DecisionFurtherReview}},
		},
		{
			name:     "Utilization_Quarterly_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUtilization, Variant: ptr(models.VariantQuarterly), Decision: models.DecisionPending}},
		},
		{
			name: "Utilization_Quarterly_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.QuarterlyUtilization = null.Float32From(159999)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUtilization, Variant: ptr(models.VariantQuarterly), Decision: models.DecisionNone}},
		},
		{
			name: "Utilization_Quarterly_FurtherReview",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.QuarterlyUtilization = null.Float32From(160000)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUtilization, Variant: ptr(models.VariantQuarterly), Decision: models.DecisionFurtherReview}},
		},
		{
			name:     "IsTspSupported_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUnsupportedTSP, Decision: models.DecisionPending}},
		},
		{
			name: "IsTspSupported_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.IsTspSupported = null.BoolFrom(true)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUnsupportedTSP, Decision: models.DecisionNone}},
		},
		{
			name: "IsTspSupported_Decline",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.IsTspSupported = null.BoolFrom(false)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleUnsupportedTSP, Decision: models.DecisionDecline}},
		},
		{
			name:     "DriverTurnover_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleDriverTurnover, Decision: models.DecisionPending}},
		},
		{
			name: "DriverTurnover_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.DriverTurnover = null.Float32From(80)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleDriverTurnover, Decision: models.DecisionNone}},
		},
		{
			name: "DriverTurnover_Decline",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.DriverTurnover = null.Float32From(80.1)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleDriverTurnover, Decision: models.DecisionDecline}},
		},
		{
			name:     "FleetSize_Driver_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantDriver), Decision: models.DecisionPending}},
		},
		{
			name: "FleetSize_Driver_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.DriverCount = null.Int32From(10)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantDriver), Decision: models.DecisionNone}},
		},
		{
			name: "FleetSize_Driver_FurtherReview_1",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.DriverCount = null.Int32From(2)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantDriver), Decision: models.DecisionFurtherReview}},
		},
		{
			name: "FleetSize_Driver_FurtherReview_2",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.DriverCount = null.Int32From(6)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantDriver), Decision: models.DecisionFurtherReview}},
		},
		{
			name: "FleetSize_Driver_Decline",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.DriverCount = null.Int32From(5)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantDriver), Decision: models.DecisionDecline}},
		},
		{
			name:     "FleetSize_Vehicle_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantVehicle), Decision: models.DecisionPending}},
		},
		{
			name: "FleetSize_Vehicle_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.VehicleCount = null.Int32From(10)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantVehicle), Decision: models.DecisionNone}},
		},
		{
			name: "FleetSize_Vehicle_FurtherReview_1",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.VehicleCount = null.Int32From(2)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantVehicle), Decision: models.DecisionFurtherReview}},
		},
		{
			name: "FleetSize_Vehicle_FurtherReview_2",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.VehicleCount = null.Int32From(6)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantVehicle), Decision: models.DecisionFurtherReview}},
		},
		{
			name: "FleetSize_Vehicle_Decline",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.VehicleCount = null.Int32From(5)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleFleetSize, Variant: ptr(models.VariantVehicle), Decision: models.DecisionDecline}},
		},
		{
			name:     "HazardZones_DistancePercentage_NJ_Pending",
			input:    func() *models.InternalInputFact { f := models.NewInternalInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDistancePercentageNJ), Decision: models.DecisionPending}},
		},
		{
			name: "HazardZones_DistancePercentage_NJ_None",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.HazardZoneDistancePercentageNJ = null.Float32From(25)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDistancePercentageNJ), Decision: models.DecisionNone}},
		},
		{
			name: "HazardZones_DistancePercentage_NJ_Decline",
			input: func() *models.InternalInputFact {
				f := models.NewInternalInputFact()
				f.HazardZoneDistancePercentageNJ = null.Float32From(25.1)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDistancePercentageNJ), Decision: models.DecisionDecline}},
		},
	}

	for _, tc := range internalCases {
		tc := tc
		t.Run("InternalGuideline/"+tc.name, func(t *testing.T) {
			out, err := env.Svc.LoadAndExecute(ctx, models.CategoryInternalGuideline, models.ProgramFleetAdmitted, tc.input, 1)
			require.NoError(t, err)
			exp := tc.expected[0]
			got := findDecision(out.Decisions, exp.RuleName, exp.Variant)
			require.NotNilf(t, got, "expected decision not found: rule=%s variant=%v", exp.RuleName, exp.Variant)
			require.Equal(t, exp.Decision, got.Decision)
			if exp.Variant == nil {
				require.Nil(t, got.Variant)
			} else {
				require.NotNil(t, got.Variant)
				require.Equal(t, *exp.Variant, *got.Variant)
			}
		})
	}

	type frontingCase struct {
		name     string
		input    *models.FrontingInputFact
		expected []models.DecisionEntity
	}

	frontingCases := []frontingCase{
		{
			name:     "YearsInBusiness_Pending",
			input:    func() *models.FrontingInputFact { f := models.NewFrontingInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleYearsInBusiness, Decision: models.DecisionPending}},
		},
		{
			name: "YearsInBusiness_None",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.TotalMonths = null.IntFrom(24)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleYearsInBusiness, Decision: models.DecisionNone}},
		},
		{
			name: "YearsInBusiness_Decline",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.TotalMonths = null.IntFrom(23)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleYearsInBusiness, Decision: models.DecisionDecline}},
		},
		{
			name:     "DotRating_Pending",
			input:    func() *models.FrontingInputFact { f := models.NewFrontingInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleDotRating, Decision: models.DecisionPending}},
		},
		{
			name: "DotRating_None_1",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.DotRating.Rating = null.StringFrom("Satisfactory")
				f.DotRating.IsDateWithinTwoYears = null.BoolFrom(true)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleDotRating, Decision: models.DecisionNone}},
		},
		{
			name: "DotRating_None_2",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.DotRating.Rating = null.StringFrom("ConditionalRating")
				f.DotRating.IsDateWithinTwoYears = null.BoolFrom(false)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleDotRating, Decision: models.DecisionNone}},
		},
		{
			name: "DotRating_RecentConditionalRating",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.DotRating.Rating = null.StringFrom("ConditionalRating")
				f.DotRating.IsDateWithinTwoYears = null.BoolFrom(true)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleDotRating, Decision: models.DecisionDecline}},
		},
		{
			name:     "TelematicsRiskScore_Pending",
			input:    func() *models.FrontingInputFact { f := models.NewFrontingInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleTelematicsRiskScore, Decision: models.DecisionPending}},
		},
		{
			name: "TelematicsRiskScore_None",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.TrsMarketCategory = null.StringFrom("Accept")
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleTelematicsRiskScore, Decision: models.DecisionNone}},
		},
		{
			name: "TelematicsRiskScore_Decline",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.TrsMarketCategory = null.StringFrom("Decline")
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleTelematicsRiskScore, Decision: models.DecisionDecline}},
		},
		{
			name:     "HazardZones_DistancePercentage_Pending",
			input:    func() *models.FrontingInputFact { f := models.NewFrontingInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDistancePercentage), Decision: models.DecisionPending}},
		},
		{
			name: "HazardZones_DistancePercentage_None",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.HazardZoneDistancePercentage = null.Float32From(25)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDistancePercentage), Decision: models.DecisionNone}},
		},
		{
			name: "HazardZones_DistancePercentage_Decline",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.HazardZoneDistancePercentage = null.Float32From(25.1)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDistancePercentage), Decision: models.DecisionDecline}},
		},
		{
			name:     "HazardZones_DurationPercentage_Pending",
			input:    func() *models.FrontingInputFact { f := models.NewFrontingInputFact(); return f }(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDurationPercentage), Decision: models.DecisionPending}},
		},
		{
			name: "HazardZones_DurationPercentage_None",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.HazardZoneDurationPercentage = null.Float32From(75)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDurationPercentage), Decision: models.DecisionNone}},
		},
		{
			name: "HazardZones_DurationPercentage_Decline",
			input: func() *models.FrontingInputFact {
				f := models.NewFrontingInputFact()
				f.HazardZoneDurationPercentage = null.Float32From(75.1)
				return f
			}(),
			expected: []models.DecisionEntity{{RuleName: models.RuleHazardZones, Variant: ptr(models.VariantDurationPercentage), Decision: models.DecisionDecline}},
		},
	}

	for _, tc := range frontingCases {
		tc := tc
		t.Run("FrontingGuideline/"+tc.name, func(t *testing.T) {
			out, err := env.Svc.LoadAndExecute(ctx, models.CategoryFrontingGuideline, models.ProgramFleetAdmitted, tc.input, 1)
			require.NoError(t, err)
			exp := tc.expected[0]
			got := findDecision(out.Decisions, exp.RuleName, exp.Variant)
			require.NotNilf(t, got, "expected decision not found: rule=%s variant=%v", exp.RuleName, exp.Variant)
			require.Equal(t, exp.Decision, got.Decision)
			if exp.Variant == nil {
				require.Nil(t, got.Variant)
			} else {
				require.NotNil(t, got.Variant)
				require.Equal(t, *exp.Variant, *got.Variant)
			}
		})
	}
}

func ptr[T any](v T) *T { return pointer_utils.ToPointer(v) }

func findDecision(decisions []models.DecisionEntity, rule models.Rule, variant *models.Variant) *models.DecisionEntity {
	for i := range decisions {
		d := &decisions[i]
		if d.RuleName != rule {
			continue
		}
		if variant == nil {
			if d.Variant == nil {
				return d
			}
			continue
		}
		if d.Variant != nil && *d.Variant == *variant {
			return d
		}
	}
	return nil
}
