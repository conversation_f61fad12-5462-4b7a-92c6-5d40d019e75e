load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "rule_set_tests_test",
    srcs = ["fleet_test_v1_test.go"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/infra/fx/testfixtures/appetite_guidelines_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/underwriting/appetite_guidelines/guidelines",
        "//nirvana/underwriting/appetite_guidelines/models",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)
