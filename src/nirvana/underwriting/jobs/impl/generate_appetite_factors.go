package impl

import (
	"context"
	"strconv"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/volatiletech/null/v8"
	"nirvanatech.com/nirvana/common-go/rule_engine"

	appetite_factors_db "nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"

	"nirvanatech.com/nirvana/underwriting/appetite_factors/appetite_score_rubric"
	"nirvanatech.com/nirvana/underwriting/appetite_factors/input"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	rubric "nirvanatech.com/nirvana/common-go/application-util/appetite_factors/recommended_action_rubric"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/logic_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	messages "nirvanatech.com/nirvana/external_client/salesforce/jobs"
	sfdcenums "nirvanatech.com/nirvana/external_client/salesforce/jobs/enums"
	"nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/quoting/jobs/impl"
	telematics_utils "nirvanatech.com/nirvana/quoting/utils"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/underwriting/app_review/utils"
	"nirvanatech.com/nirvana/underwriting/jobs"
	"nirvanatech.com/nirvana/underwriting/jobs/impl/appetite_factors"
	"nirvanatech.com/nirvana/underwriting/jobs/impl/appetite_factors/fleet"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
	"nirvanatech.com/nirvana/underwriting/rule-engine/authorities"
)

var appetiteFactorsJobMetricName = "appetite_factors_job"

func newGenerateAppetiteFactors(deps *Deps) (*jtypes.Job[*jobs.GenerateAppetiteFactorsArgs], error) {
	return jtypes.NewJob(
		jobs.GenerateAppetiteFactors,
		[]jtypes.TaskCreator[*jobs.GenerateAppetiteFactorsArgs]{
			func() jtypes.Task[*jobs.GenerateAppetiteFactorsArgs] { return &generateAppetiteFactorsTask{deps: deps} },
		},
		jobs.GenerateAppetiteScoreUnmarshalFn,
	)
}

func (g *generateAppetiteFactorsTask) emitJobMetric(ctx context.Context, success bool) {
	metricName := appetiteFactorsJobMetricName + dot + counter
	metricsErr := g.deps.MetricsClient.Inc(metricName, 1, 1, statsd.Tag{successTagKey, strconv.FormatBool(success)})
	if metricsErr != nil {
		log.Error(ctx, "Failed to emit metrics for GenerateAppetiteFactors job", log.Err(metricsErr))
	}
}

func (g *generateAppetiteFactorsTask) emitAppetiteGuidelinesFailedMetric(ctx context.Context) {
	metricName := appetiteFactorsJobMetricName + dot + "appetite_guidelines_failed" + dot + counter
	metricsErr := g.deps.MetricsClient.Inc(metricName, 1, 1)
	if metricsErr != nil {
		log.Error(ctx, "Failed to emit appetite guidelines failed metrics", log.Err(metricsErr))
	}
}

type generateAppetiteFactorsTask struct {
	deps *Deps
	job_utils.DefaultRetryable[*jobs.GenerateAppetiteFactorsArgs]
	job_utils.NoopUndoTask[*jobs.GenerateAppetiteFactorsArgs]
}

var _ jtypes.Task[*jobs.GenerateAppetiteFactorsArgs] = (*generateAppetiteFactorsTask)(nil)

func (g *generateAppetiteFactorsTask) ID() string {
	return "generateAppetiteFactorsTask"
}

func (g *generateAppetiteFactorsTask) Run(ctx jtypes.Context, message *jobs.GenerateAppetiteFactorsArgs) (err error) {
	ctx = ctx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(ctx, log.String("applicationReviewID", message.AppReviewId))
	})
	log.Info(ctx, "Running GenerateAppetiteFactors Job")

	if err = g.run(ctx, message.AppReviewId, message.IsBackfillAttempt, message.BackfillSequenceId); err != nil {
		log.Error(ctx, "GenerateAppetiteFactors job failed", log.Err(err))
		g.emitJobMetric(ctx, false)
		return err
	}

	g.emitJobMetric(ctx, true)
	return nil
}

func (g *generateAppetiteFactorsTask) run(
	ctx jtypes.Context,
	appReviewId string,
	isBackFillAttempt bool,
	backfillSequenceId *string,
) error {
	appReview, err := fleet.ValidateAndGetApplicationReview(ctx, g.deps.ApplicationReviewWrapper, appReviewId, isBackFillAttempt, backfillSequenceId)
	if errors.Is(err, fleet.ErrTerminalState) {
		log.Info(ctx, "cannot proceed with generating app review", log.Stringer("state", appReview.State))
		return nil
	}
	if err != nil {
		return errors.Wrapf(err, "failed to get validate and get app review")
	}

	appetiteScoreVersion := fleet.GetAppetiteScoreVersion(appReview.RecommendedActionInfo)
	rawInput := input.Construct(ctx, appReview, g.deps.AppReviewManager)
	appetiteFactors, appetiteScore, err := g.getAppetiteFactorsAndScore(ctx, appetiteScoreVersion,
		rawInput, appReviewId)
	if err != nil {
		return errors.Wrap(err, "failed to get appetite factors and score")
	}

	status, err := telematics_utils.GenerateTelematicsDataStatus(ctx, g.deps.TSPConnectionManager,
		g.deps.TelematicsPipelineManager, g.deps.ApplicationWrapper, appReview.Application.TSPConnHandleId,
		appReview.Application.TSPEnum, appReview.ApplicationID)
	if err != nil {
		return errors.Wrap(err, "unable to generate telematics data status")
	}
	if status == nil {
		return errors.Wrap(err, "telematics data status cannot be nil")
	}

	var (
		currentSafetyScore           *appetite_factor.SafetyScore
		isRiskScoreElementNotPresent bool
	)
	if *status == enums.TelematicsDataStatusDataAvailable {
		if appReview.Application.TSPConnHandleId == nil {
			return errors.New("TSPConnHandleId is nil while safety score is requested")
		}
		currentSafetyScore, err = fleet.GetSafetyScore(ctx, g.deps.ApplicationReviewWrapper, g.deps.ApplicationWrapper,
			g.deps.FeatureStore, *appReview.Application.TSPConnHandleId, appReview.Id)
		if err != nil {
			if !errors.Is(err, fleet.ErrNoRiskScoreElementFound) {
				return errors.Wrapf(err, "unable to get safety score")
			}
			isRiskScoreElementNotPresent = true
		}
	}

	var (
		isTspPremier     *bool
		tspKind          *telematics.TSP
		vinVisibilityVal *underwriting.ApplicationReviewVinVisibility
		hazardZone       rubric.HazardZone
	)

	if appReview.Application.TSPConnHandleId != nil {
		premierStatus, tsp, err := fleet.IsTSPPremier(ctx, appReview, g.deps.TSPConnectionManager)
		if err != nil {
			return errors.Wrap(err, "unable to determine if tsp is premier")
		}
		isTspPremier = &premierStatus
		tspKind = tsp

		// If the current safety score is nil, then the vin visibility values should be nil as well even
		// if the underlying values are not nil.
		if currentSafetyScore != nil {
			vinVisibilityVal, err = utils.GenerateApplicationReviewVinVisibility(
				ctx,
				appReview.Id,
				g.deps.ApplicationReviewWrapper,
				g.deps.FeatureStore,
				g.deps.FetcherClientFactory,
				g.deps.ProcessorClientFactory,
				g.deps.ReadFromStoreInterceptorFactory,
				g.deps.WriteToStoreInterceptorFactory,
				g.deps.VehiclesServiceClient,
				3,
			)
			if err != nil {
				return errors.Wrap(err, "failed to generate vin visibility")
			}
		}
		hazardZone = fleet.GetHazardZones(ctx, g.deps.FeatureStore, *appReview.Application.TSPConnHandleId)
	}

	var isVinVisibilityCompleted bool
	vinVisibilityChecklist := appReview.Overrides.VinVisibility
	if vinVisibilityChecklist != nil {
		isVinVisibilityCompleted = vinVisibilityChecklist.IsChecklistCompleted()
	}

	dotRatingRe := rule_engine.NullString()
	if rawInput.DOTRating != nil {
		dotRatingRe = rule_engine.ToString(*rawInput.DOTRating)
	}

	dotRatingEffDateNullable := null.TimeFromPtr(rawInput.DOTRatingEffDate)
	numOfPUsNullable := null.Int32FromPtr(rawInput.NumOfPUs)

	isCRatingRecent := fleet.IsConditionalRatingRecent(dotRatingRe, dotRatingEffDateNullable,
		appReview.EffectiveDate)

	isLossesBurnRateGreaterThan20K, err := fleet.IsLossesBurnRateGreaterThanThreshold(ctx, g.deps.AppReviewManager,
		appReviewId)
	if err != nil {
		return errors.Wrap(err, "unable to check if losses burn rate is greater than threshold")
	}

	appetiteGuidelinesInput, err := fleet.ExecuteAppetiteGuidelines(ctx, g.deps.GuidelinesManager, appReview.Id)
	if err != nil {
		log.Error(ctx,
			"Failed to execute appetite guidelines", log.Err(err), log.String("appReviewId", appReview.Id))
		g.emitAppetiteGuidelinesFailedMetric(ctx)
	}

	recActionInput := &fleet.RecommendedActionGeneratorInput{
		LogicResolverFactory:           g.deps.AppetiteFactorsLogicResolverFactory,
		AppReview:                      appReview,
		AppetiteScore:                  *appetiteScore,
		CurrentSafetyScore:             currentSafetyScore,
		TspKind:                        tspKind,
		IsTspPremier:                   isTspPremier,
		NumOfPU:                        numOfPUsNullable,
		IsCRatingRecent:                isCRatingRecent,
		VinVisibilityVal:               vinVisibilityVal,
		IsChecklistCompleted:           isVinVisibilityCompleted,
		HazardZone:                     hazardZone,
		IsRiskScoreElementNotPresent:   isRiskScoreElementNotPresent,
		IsLossesBurnRateGreaterThan20K: isLossesBurnRateGreaterThan20K,
		AppetiteGuidelinesInput:        appetiteGuidelinesInput,
	}

	traditionalRecommendedAction, _, _, _, err := fleet.GetTraditionalRecommendedAction(ctx, recActionInput)
	if err != nil || traditionalRecommendedAction == nil {
		return errors.Wrap(err, "unable to get recommended action")
	}

	recommendedAction, reason, supposedRecommendedAction, version, err := fleet.GetRecommendedAction(ctx,
		recActionInput)
	if err != nil {
		return errors.Wrap(err, "unable to get recommended action")
	}

	if recommendedAction == nil {
		return errors.New("recommended action cannot be nil")
	}

	previousAppetiteFactorsSet, err := g.deps.ApplicationReviewWrapper.GetLatestAppetiteFactors(ctx, appReviewId)
	previousFactorsFound := true
	if err != nil {
		if !errors.Is(err, uw.ErrAppetiteFactorsNotFound) {
			return err
		}
		previousFactorsFound = false
	}

	var telematicsInfo *appetite_factor.TelematicsInfo

	// Initialize telematicsInfo only if either currentSafetyScore or isTspPremier is not nil.
	if currentSafetyScore != nil || isTspPremier != nil {
		telematicsInfo = new(appetite_factor.TelematicsInfo)
		if currentSafetyScore != nil {
			telematicsInfo.SafetyScore = currentSafetyScore
		}
		if isTspPremier != nil {
			telematicsInfo.IsTspPremier = isTspPremier
		}
	}
	// Persist the vin visibility values if they are not nil
	if vinVisibilityVal != nil {
		if telematicsInfo == nil {
			telematicsInfo = new(appetite_factor.TelematicsInfo)
		}
		telematicsInfo.VinVisibility = &appetite_factor.VinVisibility{
			EquipmentListVinCount:                   vinVisibilityVal.EquipmentListVinCount,
			NonVisibleAgentSubmittedVinList:         vinVisibilityVal.NonVisibleAgentSubmittedVinList,
			NonVisibleAgentSubmittedVinListCount:    vinVisibilityVal.NonVisibleAgentSubmittedVinListCount,
			VisibleAndOverlappingVinCount:           vinVisibilityVal.VisibleAndOverlappingVinCount,
			VisibleAndOverlappingVinCountPercentage: vinVisibilityVal.VisibleAndOverlappingVinCountPercentage,
			VisibleVinsNotInAgentSubmittedList:      vinVisibilityVal.VisibleVinsNotInAgentSubmittedList,
			VisibleVinsNotInAgentSubmittedVinCount:  vinVisibilityVal.VisibleVinsNotInAgentSubmittedVinCount,
		}
	}
	if vinVisibilityChecklist != nil {
		if telematicsInfo == nil {
			telematicsInfo = new(appetite_factor.TelematicsInfo)
		}
		telematicsInfo.VinVisibilityChecklist = generateVinVisibilityChecklist(vinVisibilityChecklist)
	}

	createdAt := g.deps.Clock.Now()
	if previousFactorsFound {
		hasDataUpdated, err := appetite_factors.UpdateCurrentFactorsWithPreviousValues(
			previousAppetiteFactorsSet.AppetiteFactors, appetiteFactors, previousAppetiteFactorsSet.Version, version)
		if err != nil {
			return errors.Wrapf(err, "unable to update current factors with previous values")
		}
		if !hasDataUpdated && telematicsInfo.Equals(previousAppetiteFactorsSet.TelematicsInfo) &&
			logic_utils.CompareValuePtr(previousAppetiteFactorsSet.AppetiteScore, appetiteScore) &&
			logic_utils.CompareValuePtr(previousAppetiteFactorsSet.RecommendedAction, recommendedAction) &&
			logic_utils.CompareValuePtr(previousAppetiteFactorsSet.SupposedRecommendedAction, supposedRecommendedAction) &&
			logic_utils.CompareValuePtr(previousAppetiteFactorsSet.RecommendedActionReason, reason) {
			log.Info(ctx, "appetite factors remain unchanged, exiting")
			return nil
		}
	}

	backfillMetadata := &appetite_factor.BackfillMetadata{
		IsBackfill: isBackFillAttempt,
		SequenceId: backfillSequenceId,
	}

	dbRepresentation := uw.AppetiteFactorsDBRepresentation{
		AppReviewID:               appReviewId,
		AppetiteFactors:           appetiteFactors,
		AppetiteScore:             appetiteScore,
		RecommendedAction:         recommendedAction,
		SupposedRecommendedAction: supposedRecommendedAction,
		RecommendedActionReason:   reason,
		CreatedAt:                 createdAt,
		RunID:                     uuid.New().String(),
		NotificationInfo:          uw.NotificationInfo{Acknowledged: false},
		TelematicsInfo:            telematicsInfo,
		Version:                   version,
		ProgramType:               policyenums.ProgramTypeFleet,
		BackfillMetadata:          backfillMetadata,
	}

	if err := g.deps.ApplicationReviewWrapper.InsertAppetiteFactors(ctx, &dbRepresentation); err != nil {
		return errors.Wrapf(err, "failed to insert appetite factors for app review %s", appReviewId)
	}

	// TODO: add authority job as a task to this job
	// Trigger Authority generation
	if err := authorities.ExecuteRuleEngine(
		ctx, g.deps.RuleEngine, g.deps.ApplicationReviewWrapper, g.deps.AppReviewWidgetManager,
		g.deps.ApplicationWrapper, g.deps.TSPConnectionManager, g.deps.UWSafetyFetcher, g.deps.AppReviewManager,
		g.deps.FormsWrapper, g.deps.MetricsClient, g.deps.EventsHandler, g.deps.RiskFactorsClient, appReviewId, appReview.QuoteInfo.SelectedIndicationID,
		authorities.SourceGenerateAppetiteFactors,
	); err != nil {
		log.Error(ctx, "Failed to generate and persist authorities for app review", log.Err(err))
	}

	source := jobs.TriggerSourceGenerateAppetiteFactorsJob
	if err := jobs.TriggerGenerateExperimentsJob(
		ctx, g.deps.Jobber, appReviewId, source, false); err != nil {
		g.triggerPagerDutyForGenerateExperimentJobFailure(ctx, err, appReview)
	}

	payload := &messages.UpdateSalesforceOpportunityArgs{
		Args: wrapper.SalesforceEventUpdateApplicationArgs{
			ApplicationID:     appReview.ApplicationID,
			EventName:         sfdcenums.UnderwriterApplicationReviewRecommendedAction,
			RecommendedAction: recommendedAction,
		},
	}

	if err = impl.DispatchSalesforceUpdateEvent(ctx, g.deps.EventClient, payload); err != nil {
		// just log the error
		log.Error(ctx, "Failed to trigger salesforce update event")
	}

	return nil
}

func generateVinVisibilityChecklist(vinVisibilityVal *uw.VinVisibility) *appetite_factor.VinVisibilityChecklist {
	if vinVisibilityVal == nil {
		return nil
	}
	var tasks []appetite_factor.VinVisibilityChecklistTask
	for _, task := range vinVisibilityVal.Tasks {
		tasks = append(tasks, appetite_factor.VinVisibilityChecklistTask{
			Id:          task.Id,
			IsCompleted: task.IsCompleted,
			Description: task.Description,
			Value:       task.TaskValue(),
		})
	}
	return &appetite_factor.VinVisibilityChecklist{
		IsCheckListCompleted: vinVisibilityVal.IsChecklistCompleted(),
		Tasks:                tasks,
	}
}

func (g *generateAppetiteFactorsTask) Retry(ctx jtypes.Context, message *jobs.GenerateAppetiteFactorsArgs) error {
	return g.Run(ctx, message)
}

func (g *generateAppetiteFactorsTask) triggerPagerDutyForGenerateExperimentJobFailure(
	ctx context.Context, err error, appReview *uw.ApplicationReview,
) {
	// just log the error event and generate a PD for it
	log.Error(ctx, "Failed to add GenerateExperiment job", log.Err(err))

	pdDetails := map[string]any{
		"Error":                 err,
		"Application ID":        appReview.ApplicationID,
		"Application Review ID": appReview.Id,
		// easy to identify on an event of on-call ticket
		"Source": jobs.TriggerSourceGenerateAppetiteFactorsJob,
	}

	pdTriggerErr := jobs.TriggerPagerDutyForGenerateExperimentJobFailure(
		ctx, g.deps.InsuranceEngPDClient, appReview.Application.AgencyID, appReview.Id, jobs.ClientUnderwriter,
		pdDetails, err,
	)

	if pdTriggerErr != nil {
		log.Error(ctx, "Failed to trigger GenerateExperimentJob PD for id", log.String("appReviewId", appReview.Id),
			log.Any("pdDetails", pdDetails), log.Err(pdTriggerErr))
	}
}

func (g *generateAppetiteFactorsTask) getAppetiteFactorsAndScore(
	ctx context.Context,
	version appetite_factors_db.Version,
	rawInput *input.Input,
	reviewId string,
) ([]appetite_factor.AppetiteFactor, *appetite_factor.AppetiteScore, error) {
	resolver, err := g.deps.AppetiteFactorsLogicResolverFactory.GetLogicResolver(policyenums.ProgramTypeFleet)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get logic resolver")
	}

	factorsCalculator, err := resolver.ResolveAppetiteFactorsAndScoreLogic(version)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to resolve appetite factors logic")
	}
	req := &appetite_score_rubric.AppetiteFactorsAndScoreRequest{
		RuleEngine:  g.deps.RuleEngine,
		RawInput:    rawInput,
		AppReviewID: reviewId,
	}
	appetiteFactors, appetiteScore, err := factorsCalculator.CalculateAppetiteFactorsAndScore(ctx, req)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to calculate appetite factors")
	}

	if appetiteScore == nil {
		return nil, nil, errors.New("appetite score cannot be nil")
	}

	if len(appetiteFactors) != 12 {
		return nil, nil, errors.Newf("appetite factors length should be 12, got %+v", appetiteFactors)
	}

	return appetiteFactors, appetiteScore, nil
}
