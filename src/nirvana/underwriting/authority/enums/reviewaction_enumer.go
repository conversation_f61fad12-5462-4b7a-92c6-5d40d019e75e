// Code generated by "enumer -type=ReviewAction -json -text -yaml -sql"; DO NOT EDIT.

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
)

const _ReviewActionName = "ReviewActionInvalidReviewActionApproveReviewActionReject"

var _ReviewActionIndex = [...]uint8{0, 19, 38, 56}

const _ReviewActionLowerName = "reviewactioninvalidreviewactionapprovereviewactionreject"

func (i ReviewAction) String() string {
	if i < 0 || i >= ReviewAction(len(_ReviewActionIndex)-1) {
		return fmt.Sprintf("ReviewAction(%d)", i)
	}
	return _ReviewActionName[_ReviewActionIndex[i]:_ReviewActionIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ReviewActionNoOp() {
	var x [1]struct{}
	_ = x[ReviewActionInvalid-(0)]
	_ = x[ReviewActionApprove-(1)]
	_ = x[ReviewActionReject-(2)]
}

var _ReviewActionValues = []ReviewAction{ReviewActionInvalid, ReviewActionApprove, ReviewActionReject}

var _ReviewActionNameToValueMap = map[string]ReviewAction{
	_ReviewActionName[0:19]:       ReviewActionInvalid,
	_ReviewActionLowerName[0:19]:  ReviewActionInvalid,
	_ReviewActionName[19:38]:      ReviewActionApprove,
	_ReviewActionLowerName[19:38]: ReviewActionApprove,
	_ReviewActionName[38:56]:      ReviewActionReject,
	_ReviewActionLowerName[38:56]: ReviewActionReject,
}

var _ReviewActionNames = []string{
	_ReviewActionName[0:19],
	_ReviewActionName[19:38],
	_ReviewActionName[38:56],
}

// ReviewActionString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ReviewActionString(s string) (ReviewAction, error) {
	if val, ok := _ReviewActionNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ReviewActionNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ReviewAction values", s)
}

// ReviewActionValues returns all values of the enum
func ReviewActionValues() []ReviewAction {
	return _ReviewActionValues
}

// ReviewActionStrings returns a slice of all String values of the enum
func ReviewActionStrings() []string {
	strs := make([]string, len(_ReviewActionNames))
	copy(strs, _ReviewActionNames)
	return strs
}

// IsAReviewAction returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ReviewAction) IsAReviewAction() bool {
	for _, v := range _ReviewActionValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for ReviewAction
func (i ReviewAction) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for ReviewAction
func (i *ReviewAction) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("ReviewAction should be a string, got %s", data)
	}

	var err error
	*i, err = ReviewActionString(s)
	return err
}

// MarshalText implements the encoding.TextMarshaler interface for ReviewAction
func (i ReviewAction) MarshalText() ([]byte, error) {
	return []byte(i.String()), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for ReviewAction
func (i *ReviewAction) UnmarshalText(text []byte) error {
	var err error
	*i, err = ReviewActionString(string(text))
	return err
}

// MarshalYAML implements a YAML Marshaler for ReviewAction
func (i ReviewAction) MarshalYAML() (interface{}, error) {
	return i.String(), nil
}

// UnmarshalYAML implements a YAML Unmarshaler for ReviewAction
func (i *ReviewAction) UnmarshalYAML(unmarshal func(interface{}) error) error {
	var s string
	if err := unmarshal(&s); err != nil {
		return err
	}

	var err error
	*i, err = ReviewActionString(s)
	return err
}

func (i ReviewAction) Value() (driver.Value, error) {
	return i.String(), nil
}

func (i *ReviewAction) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	var str string
	switch v := value.(type) {
	case []byte:
		str = string(v)
	case string:
		str = v
	case fmt.Stringer:
		str = v.String()
	default:
		return fmt.Errorf("invalid value of ReviewAction: %[1]T(%[1]v)", value)
	}

	val, err := ReviewActionString(str)
	if err != nil {
		return err
	}

	*i = val
	return nil
}
