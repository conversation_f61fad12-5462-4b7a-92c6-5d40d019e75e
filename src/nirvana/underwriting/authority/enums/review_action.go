package enums

//go:generate go run github.com/dmarkham/enumer -type=ReviewAction -json -text -yaml -sql

// ReviewAction represents the action taken during review
type ReviewAction int

const (
	// ReviewActionInvalid is the default invalid value
	ReviewActionInvalid ReviewAction = iota
	// ReviewActionApprove indicates approval of the request
	ReviewActionApprove
	// ReviewActionReject indicates rejection of the request
	ReviewActionReject
)
