package authority

import (
	"go.uber.org/fx"
	authorities_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"

	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/underwriting/authority/handlers"
	"nirvanatech.com/nirvana/underwriting/state_machine"
)

// deps holds the external dependencies for the manager
type deps struct {
	fx.In

	DBWrapper                 authorities_wrapper.AuthorityRequestWrapper
	ApplicationReviewWrapper  uw.ApplicationReviewWrapper
	StateMachineReviewWrapper state_machine.AppReviewWrapper
}

// Register the module with the global registry so tests can find it
var _ = fxregistry.Register(
	fx.Provide(
		NewManagerProvider,
	),
)

// NewManagerProvider provides the Manager interface and registers handlers
func NewManagerProvider(d deps) Manager {
	manager := NewManager(d)

	// Register the decline handler
	declineHandler := NewDeclineHandler(d.StateMachineReviewWrapper)
	manager.RegisterHandler(enums.RequestTypeDecline, declineHandler)

	return manager
}

// NewDeclineHandler creates a decline handler with the app review wrapper
func NewDeclineHandler(
	appReviewWrapper state_machine.AppReviewWrapper,
) RequestHandler {
	return handlers.NewDeclineRequestHandler(appReviewWrapper)
}
