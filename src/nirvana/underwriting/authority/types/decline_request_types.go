package types

import (
	"encoding/json"

	"github.com/volatiletech/null/v8"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
)

// DeclineReason represents a single decline reason with code and description
type DeclineReason struct {
	Code        string `json:"code" validate:"required"`
	Description string `json:"description" validate:"required,min=10"`
}

// DeclineRequestPayload represents the payload for decline requests
// It contains decline reasons, target quote price, and an explanation for no quote
type DeclineRequestPayload struct {
	DeclineReasons     []DeclineReason `json:"decline_reasons" validate:"required,min=1,dive"`
	TargetQuotePrice   float64         `json:"target_quote_price" validate:"required,min=0"`
	NoQuoteExplanation string          `json:"no_quote_explanation" validate:"required,min=50"`
}

// ToJSON converts the payload to null.JSON for database storage
func (p *DeclineRequestPayload) ToJSON() (null.JSON, error) {
	data, err := json.Marshal(p)
	if err != nil {
		return null.JSON{}, err
	}
	return null.JSONFrom(data), nil
}

// GetRequestType implements the RequestPayload interface
func (p *DeclineRequestPayload) GetRequestType() enums.RequestType {
	return enums.RequestTypeDecline
}
