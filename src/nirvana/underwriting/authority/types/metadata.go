package types

import (
	"errors"
	"fmt"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	authority_enums "nirvanatech.com/nirvana/underwriting/authority/enums"
)

// Metadata type identifier constants
const (
	MetadataTypeStateTransition = "state_transition"
	MetadataTypeApproval        = "approval"
	MetadataTypeRejection       = "rejection"
	MetadataTypeCancellation    = "cancellation"
)

// Cancellation reason constants
const (
	CancellationReasonSuperseded = "superseded_by_new_request"
)

// StateTransitionMetadata represents metadata for general state transitions
type StateTransitionMetadata struct {
	Reason string    `json:"reason" validate:"required"`
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

// GetMetadataType returns the metadata type identifier
func (m *StateTransitionMetadata) GetMetadataType() string { return MetadataTypeStateTransition }

// Validate checks if the metadata is valid
func (m *StateTransitionMetadata) Validate() error {
	if m.Reason == "" {
		return errors.New("reason is required")
	}
	if m.UserID == uuid.Nil {
		return errors.New("user_id is required")
	}
	return nil
}

// ApprovalMetadata represents metadata for approval state transitions
type ApprovalMetadata struct {
	ReviewerID uuid.UUID                    `json:"reviewer_id" validate:"required"`
	Action     authority_enums.ReviewAction `json:"action" validate:"required"`
	Notes      string                       `json:"notes"`
	FinalState enums.RequestState           `json:"final_state" validate:"required"`
}

// GetMetadataType returns the metadata type identifier
func (m *ApprovalMetadata) GetMetadataType() string { return MetadataTypeApproval }

// Validate checks if the metadata is valid
func (m *ApprovalMetadata) Validate() error {
	if m.ReviewerID == uuid.Nil {
		return errors.New("reviewer_id is required")
	}
	if !m.Action.IsAReviewAction() {
		return fmt.Errorf("invalid action: %v", m.Action)
	}
	if !m.FinalState.IsARequestState() {
		return fmt.Errorf("invalid final_state: %v", m.FinalState)
	}
	return nil
}

// RejectionMetadata represents metadata for rejection state transitions
type RejectionMetadata struct {
	ReviewerID uuid.UUID                    `json:"reviewer_id" validate:"required"`
	Action     authority_enums.ReviewAction `json:"action" validate:"required"`
	Notes      string                       `json:"notes"`
	FinalState enums.RequestState           `json:"final_state" validate:"required"`
}

// GetMetadataType returns the metadata type identifier
func (m *RejectionMetadata) GetMetadataType() string { return MetadataTypeRejection }

// Validate checks if the metadata is valid
func (m *RejectionMetadata) Validate() error {
	if m.ReviewerID == uuid.Nil {
		return errors.New("reviewer_id is required")
	}
	if !m.Action.IsAReviewAction() {
		return fmt.Errorf("invalid action: %v", m.Action)
	}
	if !m.FinalState.IsARequestState() {
		return fmt.Errorf("invalid final_state: %v", m.FinalState)
	}
	return nil
}

// CancellationMetadata represents metadata for cancellation state transitions
type CancellationMetadata struct {
	Reason string    `json:"reason" validate:"required"`
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

// GetMetadataType returns the metadata type identifier
func (m *CancellationMetadata) GetMetadataType() string { return MetadataTypeCancellation }

// Validate checks if the metadata is valid
func (m *CancellationMetadata) Validate() error {
	if m.Reason == "" {
		return errors.New("reason is required")
	}
	if m.UserID == uuid.Nil {
		return errors.New("user_id is required")
	}
	return nil
}

// NewStateTransitionMetadata creates a new StateTransitionMetadata instance
func NewStateTransitionMetadata(reason string, userID uuid.UUID) *StateTransitionMetadata {
	return &StateTransitionMetadata{
		Reason: reason,
		UserID: userID,
	}
}

// NewApprovalMetadata creates a new ApprovalMetadata instance
func NewApprovalMetadata(reviewerID uuid.UUID, action authority_enums.ReviewAction, notes string, finalState enums.RequestState) *ApprovalMetadata {
	return &ApprovalMetadata{
		ReviewerID: reviewerID,
		Action:     action,
		Notes:      notes,
		FinalState: finalState,
	}
}

// NewRejectionMetadata creates a new RejectionMetadata instance
func NewRejectionMetadata(reviewerID uuid.UUID, action authority_enums.ReviewAction, notes string, finalState enums.RequestState) *RejectionMetadata {
	return &RejectionMetadata{
		ReviewerID: reviewerID,
		Action:     action,
		Notes:      notes,
		FinalState: finalState,
	}
}

// NewCancellationMetadata creates a new CancellationMetadata instance
func NewCancellationMetadata(reason string, userID uuid.UUID) *CancellationMetadata {
	return &CancellationMetadata{
		Reason: reason,
		UserID: userID,
	}
}
