load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "types",
    srcs = [
        "decline_request_types.go",
        "metadata.go",
        "review_data.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/authority/types",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/underwriting/authority/enums",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
    ],
)
