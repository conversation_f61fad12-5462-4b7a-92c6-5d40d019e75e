load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "authority",
    srcs = [
        "authority_extractor.go",
        "fx.go",
        "interfaces.go",
        "manager.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/authority",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/underwriting/authority/enums",
        "//nirvana/underwriting/authority/handlers",
        "//nirvana/underwriting/authority/types",
        "//nirvana/underwriting/state_machine",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "authority_test",
    srcs = ["manager_integration_test.go"],
    deps = [
        ":authority",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/test_utils/builders",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/application_fixture",
        "//nirvana/infra/fx/testfixtures/application_review_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/underwriting/authority/enums",
        "//nirvana/underwriting/authority/types",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
