// Package authority provides functionality for managing authority requests and their processing
package authority

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	authority_enums "nirvanatech.com/nirvana/underwriting/authority/enums"
	"nirvanatech.com/nirvana/underwriting/authority/types"
)

// AuthorityInfoExtractor extracts authority information from application reviews
type AuthorityInfoExtractor interface {
	// Extract retrieves authority requirements for a given application review
	Extract(ctx context.Context, applicationReviewID uuid.UUID, action authority_enums.ReviewAction) (*types.AuthorityInfo, error)
}

// FleetAuthorityInfoExtractor implements AuthorityInfoExtractor for fleet applications
type FleetAuthorityInfoExtractor struct {
	appReviewWrapper uw.ApplicationReviewWrapper
}

// NewFleetAuthorityInfoExtractor creates a new fleet authority info extractor
func NewFleetAuthorityInfoExtractor(appReviewWrapper uw.ApplicationReviewWrapper) *FleetAuthorityInfoExtractor {
	return &FleetAuthorityInfoExtractor{
		appReviewWrapper: appReviewWrapper,
	}
}

// Extract retrieves authority information from the fleet application review
func (e *FleetAuthorityInfoExtractor) Extract(
	ctx context.Context,
	applicationReviewID uuid.UUID,
	action authority_enums.ReviewAction,
) (*types.AuthorityInfo, error) {
	// Initialize default authority info
	authorityInfo := &types.AuthorityInfo{
		MinLevelRequired:    0.0,
		MinLevelPerRiskType: make(map[string]float64),
		RiskFactors:         make([]string, 0),
	}

	// Fetch the application review
	appReview, err := e.appReviewWrapper.GetReview(ctx, applicationReviewID.String())
	if err != nil {
		if errors.Is(err, uw.ErrAppReviewNotFound) || errors.Is(err, sql.ErrNoRows) {
			return authorityInfo, nil
		}
		return nil, errors.Wrap(err, "failed to get application review")
	}

	// Check if authority info exists
	if appReview.AuthorityInfo == nil {
		return authorityInfo, nil
	}

	// Extract the general minimum authority level
	authorityInfo.MinLevelRequired = appReview.AuthorityInfo.Fact.MinAuthorityLevel

	// Extract risk-specific minimum levels
	for riskType, minLevel := range appReview.AuthorityInfo.Fact.MinimumLevelPerRiskType {
		riskTypeStr := riskType.String()
		authorityInfo.MinLevelPerRiskType[riskTypeStr] = minLevel

		if minLevel > 0 {
			authorityInfo.RiskFactors = append(authorityInfo.RiskFactors, riskTypeStr)
		}
	}

	return authorityInfo, nil
}
