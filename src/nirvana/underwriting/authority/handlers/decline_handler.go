package handlers

import (
	"context"
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"

	authorities_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/underwriting/app_review/actions"
	"nirvanatech.com/nirvana/underwriting/app_review/actions/reasons"
	"nirvanatech.com/nirvana/underwriting/authority/types"
	"nirvanatech.com/nirvana/underwriting/state_machine"
)

// DeclineRequestHandler handles decline request operations
type DeclineRequestHandler struct {
	appReviewWrapper state_machine.AppReviewWrapper
	validator        *validator.Validate
}

// NewDeclineRequestHandler creates a new decline request handler
func NewDeclineRequestHandler(
	appReviewWrapper state_machine.AppReviewWrapper,
) *DeclineRequestHandler {
	return &DeclineRequestHandler{
		appReviewWrapper: appReviewWrapper,
		validator:        validator.New(),
	}
}

// ValidatePayload validates the decline request payload
func (h *DeclineRequestHandler) ValidatePayload(data interface{}) (interface{}, error) {
	payload, ok := data.(*types.DeclineRequestPayload)
	if !ok {
		return nil, errors.New("payload must be of type *types.DeclineRequestPayload")
	}

	// Verify the payload is for the correct request type
	if payload.GetRequestType() != enums.RequestTypeDecline {
		return nil, errors.Newf("payload request type mismatch: expected %s, got %s",
			enums.RequestTypeDecline, payload.GetRequestType())
	}

	if err := h.validator.Struct(payload); err != nil {
		return nil, errors.Wrap(err, "decline request validation failed")
	}

	return payload, nil
}

// Execute performs the decline action for an approved request
func (h *DeclineRequestHandler) Execute(ctx context.Context, request *authorities_wrapper.AuthorityRequest) error {
	// Parse the payload
	var payload types.DeclineRequestPayload
	if err := json.Unmarshal(request.RequestData.JSON, &payload); err != nil {
		return errors.Wrap(err, "failed to parse decline request payload")
	}

	// Convert our payload to the format expected by AppReviewWrapper
	declineReasons := h.convertToDeclineReasons(&payload)

	// Create supplement args (empty for now, can be extended later)
	supplementArgs := actions.DeclineActionSupplementArgs{}

	// Execute the decline through the app review wrapper
	err := h.appReviewWrapper.DeclineAppReview(
		ctx,
		request.ApplicationReviewID,
		declineReasons,
		supplementArgs,
	)
	if err != nil {
		return errors.Wrap(err, "failed to execute decline application")
	}

	return nil
}

// convertToDeclineReasons converts our payload to the format expected by the app review wrapper
func (h *DeclineRequestHandler) convertToDeclineReasons(payload *types.DeclineRequestPayload) uw.DeclineReasonsStruct {
	// Convert each decline reason to the expected format
	reasonsList := make([]reasons.ReasonStruct, 0, len(payload.DeclineReasons))
	for _, reason := range payload.DeclineReasons {
		reasonsList = append(reasonsList, reasons.ReasonStruct{
			ReasonCode: reason.Code,
			ReasonText: reason.Description,
		})
	}

	// Create the decline reasons structure with the no quote explanation as comments
	return uw.DeclineReasonsStruct{
		Reasons:  reasonsList,
		Comments: payload.NoQuoteExplanation,
	}
}

// GetType returns the request type this handler manages
func (h *DeclineRequestHandler) GetType() enums.RequestType {
	return enums.RequestTypeDecline
}

// CanCreate checks if the user can create a decline request
func (h *DeclineRequestHandler) CanCreate(ctx context.Context, applicationReviewID uuid.UUID) error {
	// Get user from context
	user := authz.UserFromContext(ctx)
	if user.ID == uuid.Nil {
		return errors.New("user not authenticated")
	}

	// Check if user has underwriter access
	if !user.HasUnderwriterAccess() {
		return errors.New("user does not have underwriter access")
	}

	// Check the application review state
	state, err := h.appReviewWrapper.State(ctx, applicationReviewID)
	if err != nil {
		return errors.Wrap(err, "failed to get application review state")
	}

	if state == nil {
		return errors.New("application review not found")
	}

	// Check if the review is in a non-terminal state where it can still be declined
	nonTerminalStates := uw.GetNonTerminalApplicationReviewStateMap()

	if !nonTerminalStates[*state] {
		return errors.Newf("application review cannot be declined in current state: %s (only non-terminal states are allowed)", *state)
	}

	return nil
}

// CanReview checks if the user can review decline requests
func (h *DeclineRequestHandler) CanReview(ctx context.Context) error {
	// Get user from context
	user := authz.UserFromContext(ctx)
	if user.ID == uuid.Nil {
		return errors.New("user not authenticated")
	}

	// Check if user has manager role or higher authority
	if !user.IsUnderwriterManager() && !user.IsSeniorUnderwriter() && !user.IsSuperuser() {
		return errors.New("user does not have manager privileges")
	}

	return nil
}
