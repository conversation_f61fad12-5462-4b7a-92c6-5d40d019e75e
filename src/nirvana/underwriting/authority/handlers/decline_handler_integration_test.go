package handlers_test

import (
    "encoding/json"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"

	authorities_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/underwriting/authority/handlers"
	"nirvanatech.com/nirvana/underwriting/authority/types"
	"nirvanatech.com/nirvana/underwriting/state_machine"
)

func TestDeclineHandler_ValidatePayload(t *testing.T) {
	t.Parallel()

	var env struct {
		fx.In
		AppReviewWrapper state_machine.AppReviewWrapper
	}

	defer testloader.RequireStart(t, &env).RequireStop()

	handler := handlers.NewDeclineRequestHandler(
		env.AppReviewWrapper,
	)

	t.Run("ValidPayload", func(t *testing.T) {
		// Create valid payload using concrete struct
		payload := &types.DeclineRequestPayload{
			DeclineReasons: []types.DeclineReason{
				{
					Code:        "loss_history",
					Description: "Business has significant loss history in the past 24 months with multiple at-fault accidents.",
				},
				{
					Code:        "safety_record",
					Description: "Poor safety record with DOT violations and failed inspections.",
				},
			},
			TargetQuotePrice:   15000.0,
			NoQuoteExplanation: "The risk profile exceeds our underwriting guidelines due to poor loss history and safety record. No premium adjustment can adequately compensate for the elevated risk.",
		}

		// Validate payload
		result, err := handler.ValidatePayload(payload)

		require.NoError(t, err)
		require.NotNil(t, result)

		// Verify the returned payload structure
		declinePayload, ok := result.(*types.DeclineRequestPayload)
		require.True(t, ok)
		require.Len(t, declinePayload.DeclineReasons, 2)
		require.Equal(t, "loss_history", declinePayload.DeclineReasons[0].Code)
		require.Equal(t, "safety_record", declinePayload.DeclineReasons[1].Code)
		require.Equal(t, 15000.0, declinePayload.TargetQuotePrice)
	})

	t.Run("MissingRequiredFields", func(t *testing.T) {
		// Create payload missing required fields using concrete struct
		payload := &types.DeclineRequestPayload{
			DeclineReasons:     []types.DeclineReason{}, // Empty array
			TargetQuotePrice:   15000.0,
			NoQuoteExplanation: "Too short", // Less than 50 characters
		}

		// Validate payload
		result, err := handler.ValidatePayload(payload)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "validation failed")
	})
}

func TestDeclineHandler_GetType(t *testing.T) {
	t.Parallel()

	var env struct {
		fx.In
		AppReviewWrapper state_machine.AppReviewWrapper
	}

	defer testloader.RequireStart(t, &env).RequireStop()

	handler := handlers.NewDeclineRequestHandler(
		env.AppReviewWrapper,
	)

	requestType := handler.GetType()
	require.Equal(t, enums.RequestTypeDecline, requestType)
}

func TestGetDeclinePayloadFromRequest(t *testing.T) {
	t.Parallel()

	t.Run("SuccessfulExtraction", func(t *testing.T) {
		payload := types.DeclineRequestPayload{
			DeclineReasons: []types.DeclineReason{
				{
					Code:        "loss_history",
					Description: "Multiple losses in recent years",
				},
				{
					Code:        "safety_record",
					Description: "Poor safety record with violations",
				},
			},
			TargetQuotePrice:   15000,
			NoQuoteExplanation: "Risk exceeds acceptable parameters due to loss history and safety concerns",
		}
		payloadJSON, err := payload.ToJSON()
		require.NoError(t, err)
		
		request := &authorities_wrapper.AuthorityRequest{
			RequestType: enums.RequestTypeDecline,
			RequestData: payloadJSON,
		}

    // Inline extraction since helper was removed
    var extractedPayload types.DeclineRequestPayload
    err = json.Unmarshal(request.RequestData.JSON, &extractedPayload)

		require.NoError(t, err)
    require.Len(t, extractedPayload.DeclineReasons, 2)
    require.Equal(t, "loss_history", extractedPayload.DeclineReasons[0].Code)
    require.Equal(t, "safety_record", extractedPayload.DeclineReasons[1].Code)
    require.Equal(t, 15000.0, extractedPayload.TargetQuotePrice)
	})

	t.Run("WrongRequestType", func(t *testing.T) {
		request := &authorities_wrapper.AuthorityRequest{
			RequestType: enums.RequestTypeApprove,
			RequestData: null.JSONFrom([]byte(`{}`)),
		}

    // Wrong type: simulate by attempting unmarshal and asserting no panic; behavior moved out
    var wrongPayload types.DeclineRequestPayload
    err := json.Unmarshal(request.RequestData.JSON, &wrongPayload)
    require.NoError(t, err)
	})
}
