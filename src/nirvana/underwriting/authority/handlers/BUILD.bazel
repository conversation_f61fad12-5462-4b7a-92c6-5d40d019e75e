load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "handlers",
    srcs = ["decline_handler.go"],
    importpath = "nirvanatech.com/nirvana/underwriting/authority/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/authz",
        "//nirvana/underwriting/app_review/actions",
        "//nirvana/underwriting/app_review/actions/reasons",
        "//nirvana/underwriting/authority/types",
        "//nirvana/underwriting/state_machine",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_go_playground_validator_v10//:validator",
        "@com_github_google_uuid//:uuid",
    ],
)

go_test(
    name = "handlers_test",
    srcs = ["decline_handler_integration_test.go"],
    deps = [
        ":handlers",
        "//nirvana/db-api/db_wrappers/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/infra/fx/testloader",
        "//nirvana/underwriting/authority/types",
        "//nirvana/underwriting/state_machine",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)
