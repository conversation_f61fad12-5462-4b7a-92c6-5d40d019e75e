package fleet

import (
	"strings"

	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policyEnums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd"
	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd/model"
)

const (
	formsMatrixALSheetId  = "1lHO1A_dZtUL1CQ3w7quGtPpVOZXyEolZLNCUB-dW84E"
	formsMatrixGLSheetId  = "1EKlDghC1P6SRC8zTJEW3MIjlJne3DM1cQxRfCuGRvcU"
	formsMatrixMTCSheetId = "1TH7AIKB-xslxYa-5VCD73y4J5JuJd5VWKgEV3hPnnG8"
	packageCommonCol      = 6
	packageAPDCol         = 7
	quoteDisplayCol       = 1
	isDynamicCol          = 4
)

// GetFormMatrixSheetID returns the sheet IDs of the sheets that store the form matrices for fleet policies.
// AL/APD Matrix Link -> https://docs.google.com/spreadsheets/d/1lHO1A_dZtUL1CQ3w7quGtPpVOZXyEolZLNCUB-dW84E/edit?usp=sharing
// GL Matrix Link -> https://docs.google.com/spreadsheets/d/1EKlDghC1P6SRC8zTJEW3MIjlJne3DM1cQxRfCuGRvcU/edit?usp=sharing
// MTC Matrix Link -> https://docs.google.com/spreadsheets/d/1TH7AIKB-xslxYa-5VCD73y4J5JuJd5VWKgEV3hPnnG8/edit?usp=sharing
// Based on those matrices, the following files are generated:
//  1. forms_database.go
//  2. core_forms.go
//  3. manual_forms.go
//  4. state_forms.go
//  5. package_forms.go
//  6. signed_quote_forms.go
func GetFormMatrixSheetID(coverage app_enums.Coverage) string {
	switch coverage {
	case app_enums.CoverageAutoLiability:
		return formsMatrixALSheetId
	case app_enums.CoverageGeneralLiability:
		return formsMatrixGLSheetId
	case app_enums.CoverageMotorTruckCargo:
		return formsMatrixMTCSheetId
	}
	return ""
}

func GetMatrixRowVars(coverage app_enums.Coverage, row []string) (*model.MatrixRowVars, error) {
	alPackageType, apdPackageType, glPackageType, mtcPackageType := model.None, model.None, model.None, model.None
	offset := 0
	var coverages []app_enums.Coverage
	var err error

	switch coverage {
	case app_enums.CoverageAutoLiability:
		offset = 1
		if alPackageType, err = cmd.GetPackageType(row[packageCommonCol]); err != nil {
			return nil, err
		}
		coverages = cmd.AddCoverageIfApplicable(coverages, app_enums.CoverageAutoLiability, alPackageType)
		if apdPackageType, err = cmd.GetPackageType(row[packageAPDCol]); err != nil {
			return nil, err
		}
		coverages = cmd.AddCoverageIfApplicable(coverages, app_enums.CoverageAutoPhysicalDamage, apdPackageType)
	case app_enums.CoverageGeneralLiability:
		if glPackageType, err = cmd.GetPackageType(row[packageCommonCol]); err != nil {
			return nil, err
		}
		coverages = cmd.AddCoverageIfApplicable(coverages, app_enums.CoverageGeneralLiability, glPackageType)
	case app_enums.CoverageMotorTruckCargo:
		if mtcPackageType, err = cmd.GetPackageType(row[packageCommonCol]); err != nil {
			return nil, err
		}
		coverages = cmd.AddCoverageIfApplicable(coverages, app_enums.CoverageMotorTruckCargo, mtcPackageType)
	}

	formVariable := strings.NewReplacer(" ", "", "‐", "", "-", "", "&", "").Replace(row[quoteDisplayCol])

	return &model.MatrixRowVars{
		ALPackages:      alPackageType,
		APDPackages:     apdPackageType,
		GLPackages:      glPackageType,
		MTCPackages:     mtcPackageType,
		ColOffset:       offset,
		Coverages:       coverages,
		SupportedStates: getSupportedStates(),
		// All fleet forms are dynamic because of a requirement in MI which requires all forms to be dynamic.
		IsDynamic:        isDynamicForm(row[isDynamicCol]),
		FormVariableName: formVariable,
	}, nil
}

func isDynamicForm(isDynamic string) bool {
	return isDynamic == "Dynamic"
}

func getSupportedStates() map[us_states.USState]bool {
	return cmd.GetSupportedStates(policyEnums.ProgramTypeFleet)
}
