package core

import (
	"encoding/json"
	"strconv"

	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/policy_common/constants"
)

// TODO: update to support both int and string types
type Constants struct {
	OccurrenceLimit                             string `json:"OccurrenceLimit"`
	RentedToYouLimit                            string `json:"RentedToYouLimit"`
	MedicalExpenseLimit                         string `json:"MedicalExpenseLimit"`
	PersonalAdvertisingInjuryLimit              string `json:"PersonalAdvertisingInjuryLimit"`
	GeneralAggregateLimit                       string `json:"GeneralAggregateLimit"`
	CompletedOperationsAggregateLimit           string `json:"CompletedOperationsAggregateLimit"`
	RetroactiveDate                             string `json:"RetroactiveDate"`
	BusinessDescription                         string `json:"BusinessDescription"`
	BusinessDescriptionGL                       string `json:"BusinessDescriptionGL"`
	BusinessDescriptionMTC                      string `json:"BusinessDescriptionMTC"`
	IncludedPremium                             string `json:"IncludedPremium"`
	SingleLimitBIAndPD                          string `json:"SingleLimitBIAndPD"`
	BodilyInjuryAndPropertyDamage               string `json:"BodilyInjuryAndPropertyDamage"`
	LimitBodilyInjuryByAccident                 string `json:"LimitBodilyInjuryByAccident"`
	LimitBodilyInjuryByDiseaseAggregate         string `json:"LimitBodilyInjuryByDiseaseAggregate"`
	LimitBodilyInjuryByDiseaseEachEmployee      string `json:"LimitBodilyInjuryByDiseaseEachEmployee"`
	DescriptionOfOperations                     string `json:"DescriptionOfOperations"`
	AdditionalInsuredEntityName                 string `json:"AdditionalInsuredEntityName"`
	Classification                              string `json:"Classification"`
	Exposure                                    string `json:"Exposure"`
	CodeNumber                                  string `json:"CodeNumber"`
	PDComprehensive                             string `json:"PDComprehensive"`
	PDCollision                                 string `json:"PDCollision"`
	GLPremiumBase                               string `json:"GLPremiumBase"`
	TerrorismCoverage                           string `json:"TerrorismCoverage"`
	LossPayeeSchedule                           string `json:"LossPayeeSchedule"`
	CoveredAutoNumberAdmitted                   string `json:"CoveredAutoNumberAdmitted"`
	ReferToVehicleSchedule                      string `json:"ReferToVehicleSchedule"`
	PropertyAtTerminalsDays                     string `json:"PropertyAtTerminalsDays"`
	NAICCodeFLI                                 string `json:"NAICCodeFLI"`
	NAICCodeMST                                 string `json:"NAICCodeMST"`
	OccurrenceLimitSingleAuto                   string `json:"OccurrenceLimitSingleAuto"`
	EarnedChargesLimit                          string `json:"EarnedChargesLimit"`
	MCS90PhoneNumber                            string `json:"MCS90PhoneNumber"`
	SFPStates                                   string `json:"SFPStates"`
	DesignationOrDescriptionOfCoveredAutosShort string `json:"DesignationOrDescriptionOfCoveredAutosShort"`
	PAAutoDecWording                            string `json:"PAAutoDecWording"`
	MSTContactNumber                            string `json:"MSTContactNumber"`
	DebrisRemoval                               string `json:"DebrisRemoval"`
	FalsePretense                               string `json:"FalsePretense"`
	RewardsArrestConviction                     string `json:"RewardsArrestConviction"`
	RewardsReturnOfProperty                     string `json:"RewardsReturnOfProperty"`
	CargoHandlingEquipment                      string `json:"CargoHandlingEquipment"`
	ContractualPenalties                        string `json:"ContractualPenalties"`
	FireDeptServiceCharges                      string `json:"FireDeptServiceCharges"`
	FireExtinguishingSystemExpense              string `json:"FireExtinguishingSystemExpense"`
	FuelCharges                                 string `json:"FuelCharges"`
	NewlyAcquiredTerminals                      string `json:"NewlyAcquiredTerminals"`
	PollutantCleanUpAndRemovalLimit             string `json:"PollutantCleanUpAndRemovalLimit"`
	PreservationOfProperty                      string `json:"PreservationOfProperty"`
	ElectronicEquipment                         string `json:"ElectronicEquipment"`
	CargoLossMitigationExpenses                 string `json:"CargoLossMitigationExpenses"`
	RemovalExpenses                             string `json:"RemovalExpenses"`
	ReloadExpenses                              string `json:"ReloadExpenses"`
	TrafficAndSecurityExpense                   string `json:"TrafficAndSecurityExpense"`
	LineOfBuisness                              string `json:"LineOfBuisness"`
	SpecialProvisions                           string `json:"SpecialProvisions"`
	LineOfBusinessAL                            string `json:"LineOfBusinessAL"`
	LineOfBusinessGL                            string `json:"LineOfBusinessGL"`
	LineOfBusinessMTC                           string `json:"LineOfBusinessMTC"`
	TXSLTaxRate                                 string `json:"TXSLTaxRate"`
	SiriusPointWebAddress                       string `json:"SiriusPointWebAddress"`
	ReferToTerminalSchedule                     string `json:"ReferToTerminalSchedule"`
	BlanketApplies                              string `json:"BlanketApplies"`
	CameraProviderSamsara                       string `json:"CameraProviderSamsara"`
	CameraProviderMotive                        string `json:"CameraProviderMotive"`
	NoCoverage                                  string `json:"NoCoverage"`
	CheckboxChecked                             string `json:"CheckboxChecked"`
	CheckboxUnchecked                           string `json:"CheckboxUnchecked"`
	InsuranceProducer                           string `json:"InsuranceProducer"`
	InsuranceProducerMailingAddress             string `json:"InsuranceProducerMailingAddress"`
}

// GetConstants returns the constants that can be used to print on the forms
// Note: Once added, never delete any constants from here without verifying that they are not used in the forms
func GetConstants() (map[string]interface{}, error) {
	constantsStruct := Constants{
		OccurrenceLimit:                             str_utils.NumberToLocaleString(constants.OccurrenceLimit, 0),
		RentedToYouLimit:                            str_utils.NumberToLocaleString(constants.RentedToYouLimit, 0),
		MedicalExpenseLimit:                         str_utils.NumberToLocaleString(constants.MedicalExpenseLimit, 0),
		PersonalAdvertisingInjuryLimit:              str_utils.NumberToLocaleString(constants.PersonalAdvertisingInjuryLimit, 0),
		GeneralAggregateLimit:                       str_utils.NumberToLocaleString(constants.GeneralAggregateLimit, 0),
		CompletedOperationsAggregateLimit:           str_utils.NumberToLocaleString(constants.CompletedOperationsAggregateLimit, 0),
		RetroactiveDate:                             constants.RetroactiveDate,
		BusinessDescription:                         constants.BusinessDescription,
		BusinessDescriptionGL:                       constants.BusinessDescriptionGL,
		BusinessDescriptionMTC:                      constants.BusinessDescriptionMTC,
		IncludedPremium:                             constants.IncludedPremium,
		SingleLimitBIAndPD:                          str_utils.NumberToLocaleString(constants.SingleLimitBIAndPD, 0),
		BodilyInjuryAndPropertyDamage:               str_utils.NumberToLocaleString(constants.BodilyInjuryAndPropertyDamage, 0),
		LimitBodilyInjuryByAccident:                 str_utils.NumberToLocaleString(constants.LimitBodilyInjuryByAccident, 0),
		LimitBodilyInjuryByDiseaseAggregate:         str_utils.NumberToLocaleString(constants.LimitBodilyInjuryByDiseaseAggregate, 0),
		LimitBodilyInjuryByDiseaseEachEmployee:      str_utils.NumberToLocaleString(constants.LimitBodilyInjuryByDiseaseEachEmployee, 0),
		DescriptionOfOperations:                     constants.DescriptionOfOperations,
		AdditionalInsuredEntityName:                 constants.AdditionalInsuredEntityName,
		Classification:                              constants.Classification,
		Exposure:                                    constants.Exposure,
		CodeNumber:                                  strconv.Itoa(constants.CodeNumber),
		PDComprehensive:                             constants.PDComprehensive,
		PDCollision:                                 constants.PDCollision,
		GLPremiumBase:                               constants.GLPremiumBase,
		TerrorismCoverage:                           constants.TerrorismCoverage,
		LossPayeeSchedule:                           constants.LossPayeeSchedule,
		CoveredAutoNumberAdmitted:                   constants.CoveredAutoNumberAdmitted,
		ReferToVehicleSchedule:                      constants.ReferToVehicleSchedule,
		PropertyAtTerminalsDays:                     strconv.Itoa(constants.PropertyAtTerminalsDays),
		NAICCodeFLI:                                 constants.NAICCodeFLI,
		NAICCodeMST:                                 constants.NAICCodeMST,
		OccurrenceLimitSingleAuto:                   str_utils.NumberToLocaleString(constants.OccurrenceLimitSingleAuto, 0),
		EarnedChargesLimit:                          str_utils.NumberToLocaleString(constants.EarnedChargesLimit, 0),
		MCS90PhoneNumber:                            constants.MCS90PhoneNumber,
		SFPStates:                                   constants.SFPStates,
		DesignationOrDescriptionOfCoveredAutosShort: constants.DesignationOrDescriptionOfCoveredAutosShort,
		PAAutoDecWording:                            constants.PAAutoDecWording,
		MSTContactNumber:                            constants.MSTContactNumber,
		DebrisRemoval:                               str_utils.NumberToLocaleString(constants.DebrisRemoval, 0),
		FalsePretense:                               str_utils.NumberToLocaleString(constants.FalsePretense, 0),
		RewardsArrestConviction:                     str_utils.NumberToLocaleString(constants.RewardsArrestConviction, 0),
		RewardsReturnOfProperty:                     str_utils.NumberToLocaleString(constants.RewardsReturnOfProperty, 0),
		CargoHandlingEquipment:                      str_utils.NumberToLocaleString(constants.CargoHandlingEquipment, 0),
		ContractualPenalties:                        str_utils.NumberToLocaleString(constants.ContractualPenalties, 0),
		FireDeptServiceCharges:                      str_utils.NumberToLocaleString(constants.FireDeptServiceCharges, 0),
		FireExtinguishingSystemExpense:              str_utils.NumberToLocaleString(constants.FireExtinguishingSystemExpense, 0),
		FuelCharges:                                 str_utils.NumberToLocaleString(constants.FuelCharges, 0),
		NewlyAcquiredTerminals:                      str_utils.NumberToLocaleString(constants.NewlyAcquiredTerminals, 0),
		PollutantCleanUpAndRemovalLimit:             str_utils.NumberToLocaleString(constants.PollutantCleanUpandRemovalLimit, 0),
		PreservationOfProperty:                      str_utils.NumberToLocaleString(constants.PreservationOfProperty, 0),
		ElectronicEquipment:                         str_utils.NumberToLocaleString(constants.ElectronicEquipment, 0),
		CargoLossMitigationExpenses:                 str_utils.NumberToLocaleString(constants.CargoLossMitigationExpenses, 0),
		RemovalExpenses:                             str_utils.NumberToLocaleString(constants.RemovalExpenses, 0),
		ReloadExpenses:                              str_utils.NumberToLocaleString(constants.ReloadExpenses, 0),
		TrafficAndSecurityExpense:                   str_utils.NumberToLocaleString(constants.TrafficAndSecurityExpense, 0),
		LineOfBuisness:                              constants.LineOfBuisness,
		SpecialProvisions:                           constants.SpecialProvisions,
		LineOfBusinessAL:                            constants.LineOfBusinessAL,
		LineOfBusinessGL:                            constants.LineOfBusinessGL,
		LineOfBusinessMTC:                           constants.LineOfBusinessMTC,
		TXSLTaxRate:                                 constants.TXSLTaxRate,
		SiriusPointWebAddress:                       constants.CompanyWebAddress,
		ReferToTerminalSchedule:                     constants.ReferToTerminalSchedule,
		BlanketApplies:                              constants.BlanketApplies,
		CameraProviderSamsara:                       constants.CameraProviderSamsara,
		CameraProviderMotive:                        constants.CameraProviderMotive,
		NoCoverage:                                  constants.NoCoverage,
		CheckboxChecked:                             constants.CheckboxChecked,
		CheckboxUnchecked:                           constants.CheckboxUnchecked,
		InsuranceProducer:                           constants.InsuranceProducerNirvana.String(),
		InsuranceProducerMailingAddress:             constants.InsuranceProducerNirvana.AgentMailingAddress(),
	}

	data, err := json.Marshal(constantsStruct)
	if err != nil {
		return nil, err
	}
	var constantsMap map[string]interface{}
	err = json.Unmarshal(data, &constantsMap)
	if err != nil {
		return nil, err
	}

	return constantsMap, nil
}
