components:
  parameters:
    ApplicationReviewID:
      in: path
      name: applicationReviewID
      required: true
      schema:
        type: string
        example: a81bc81b-dead-4e5d-abff-90865d1e13b1
    ApplicationID:
      in: path
      name: applicationID
      required: true
      schema:
        type: string
        example: a81bc81b-dead-4e5d-abff-90865d1e13b1
    DocumentId:
      in: query
      name: documentId
      schema:
        type: string
        example: a81bc81b-dead-4e5d-abff-90865d1e13b1
    PolicySn:
      in: query
      name: policySn
      schema:
        type: integer
        format: int32
        example: 1
    ClaimSn:
      in: query
      name: claimSn
      schema:
        type: integer
        format: int32
        example: 2
  schemas:
    applicationReviewIDs:
      type: string
      example: a81bc81b-dead-4e5d-abff-90865d1e13b1
    ApplicationReviewSummary:
      type: object
      required:
        - appReviewID
        - applicationShortID
        - dotNumber
        - companyName
        - telematicsConnectionState
        - state
        - underwriterName
        - underwriterEmail
        - effectiveDate
        - createdAt
        - updatedAt
        - accountGrade
        - unresolvedProblems
        - unreviewedProblems
        - assignees
        - recommendedAction
        - puCount
        - isRiskWorksheetEnabled
        - mtcVersion
      properties:
        appReviewID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        applicationShortID:
          type: string
          minLength: 7
          maxLength: 7
          example: ABC1234
        dotNumber:
          type: integer
          format: int64
          example: 1002125
        companyName:
          type: string
          example: All Trucking Inc
        state:
          $ref: "#/components/schemas/ApplicationReviewState"
        telematicsConnectionState:
          $ref: "#/components/schemas/TelematicsConnectionState"
        tspName:
          type: string
          example: Samsara
        tspConnectionHandleId:
          type: string
          example: 67f980da-fc62-407f-8354-3e45ac899514
        underwriterName:
          type: string
          example: Taylor G.
        underwriterEmail:
          type: string
          format: email
          example: <EMAIL>
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        createdAt:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
        updatedAt:
          type: string
          format: date-time
          example: 2021-03-02T12:32:11.312321-05:00
        isInternal:
          type: boolean
        accountGrade:
          $ref: "#/components/schemas/ApplicationReviewAccountGradeProperty"
        unresolvedProblems:
          type: array
          items:
            $ref: "#/components/schemas/Problems"
        unreviewedProblems:
          type: array
          items:
            $ref: "#/components/schemas/Problems"
        assignees:
          $ref: "#/components/schemas/ApplicationReviewAssignees"
        tabDetails:
          $ref: "#/components/schemas/ApplicationReviewTabDetails"
        numDeclineCriteria:
          type: integer
        recommendedAction:
          $ref: "#/components/schemas/RecommendedAction"
        clearanceStatus:
          $ref: "../common/spec.yaml#/components/schemas/ApplicationClearanceStatus"
        puCount:
          type: integer
          example: 50
        endStateReasons:
          $ref: "#/components/schemas/EndStateReasons"
        isRiskWorksheetEnabled:
          type: boolean
          example: true
        mtcVersion:
          $ref: "../common/spec.yaml#/components/schemas/MTCVersion"
    AppReviewForList:
      type: object
      required:
        - appReviewID
        - companyName
        - effectiveDate
        - recommendedAction
        - puCount
        - assignees
      properties:
        appReviewID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        companyName:
          type: string
          example: All Trucking Inc
        tspName:
          type: string
          example: Samsara
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        isInternal:
          type: boolean
        assignees:
          $ref: "#/components/schemas/ApplicationReviewAssignees"
        tabDetails:
          $ref: "#/components/schemas/ApplicationReviewTabDetails"
        recommendedAction:
          $ref: "#/components/schemas/RecommendedAction"
        clearanceStatus:
          $ref: "../common/spec.yaml#/components/schemas/ApplicationClearanceStatus"
        puCount:
          type: integer
          example: 50
        vinVisibilityPercentage:
          type: number
          format: float
        isVinVisibilityChecklistComplete:
          type: boolean
        daysConnected:
          type: integer
        agencyName:
          type: string
        currentStatus:
          $ref: "#/components/schemas/ApplicationReviewCurrentStatus"

    NBasicScore:
      type: string
      enum: [ Undefined, NBasicScoreA, NBasicScoreB, NBasicScoreC, NBasicScoreD, NBasicScoreE, NBasicScoreF ]
    Underwriters:
      type: object
      required:
        - seniorUnderwriters
      properties:
        seniorUnderwriters:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewUser"
    ApplicationReviewAssignees:
      type: object
      properties:
        underwriter:
          $ref: "#/components/schemas/ApplicationReviewAssignee"
    ApplicationReviewAssigneesForm:
      type: object
      properties:
        underwriterID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
    ApplicationReviewAssignee:
      type: object
      properties:
        current:
          $ref: "#/components/schemas/ApplicationReviewUser"
        options:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewUser"
    ApplicationReviewUser:
      type: object
      required:
        - id
        - name
        - email
      properties:
        id:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        name:
          type: string
          example: Taylor G.
        email:
          type: string
          format: email
          example: <EMAIL>
        iconUrl:
          type: string
          example: https://dywmdoipgmhli.cloudfront.net/assets/underwriter-user-icons/671259f04c9aceb0a955cd9d73e9d6a4.jpg
    ApplicationReviewDetail:
      type: object
      required:
        - summary
        - aggregateMerit
        - aggregateCredit
        - aggregateCreditByCoverage
        - panelWiseReviewInfo
      properties:
        # TODO: Add more properties
        summary:
          $ref: "#/components/schemas/ApplicationReviewSummary"
        aggregateMerit:
          type: integer
          format: int32
          example: -2
        aggregateCredit:
          type: number
          example: 2.3
        aggregateCreditByCoverage:
          $ref: "#/components/schemas/ApplicationReviewAggregateCreditByCoverage"
        aggregateCreditLimitsByCoverage:
          $ref: "#/components/schemas/ApplicationReviewAggregateCreditLimitsByCoverage"
        panelWiseReviewInfo:
          $ref: "#/components/schemas/ApplicationReviewPanelWiseInfo"
        telematicsDataButtonStatus:
          $ref: "#/components/schemas/TelematicsDataButtonStatus"

    ApplicationReviewUserPermissions:
      type: object
      required:
        - permissions
        - status
        - minLevelRequired
        - userActions
      properties:
        permissions:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewUserPermission"
        status:
          type: boolean
        minLevelRequired:
          type: integer
          format: int32
        userActions:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewUserAction"
    ApplicationReviewUserAction:
      type: object
      required:
        - actionType
        - status
        - riskTypes
      properties:
        actionType:
          $ref: "#/components/schemas/ApplicationUserActionType"
        status:
          type: boolean
        riskTypes:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewUserPermission"
    ApplicationUserActionType:
      type: string
      enum: [ UserActionApproval,UserActionDecline,UserActionClose,UserActionViewReleaseDocuments,UserActionReopenReview ]
    ApplicationReviewUserPermission:
      type: object
      required:
        - type
        - status
        - minLevelRequired
      properties:
        type:
          $ref: "#/components/schemas/ApplicationReviewUserPermissionType"
        status:
          type: boolean
        minLevelRequired:
          type: integer
          format: int32
        value:
          type: string
          example: "1"

    ApplicationReviewUserPermissionType:
      type: string
      enum: [ ApprovalPermission, AuthorityPermission, GWP , ScheduleMod, AppetiteScore, NegotiatedRates, AlDeductible, YIB, CurrentTermGrowth, TotalClaims,
              SafetyScores, NonStandardDrivers, RateChange, MileageProjection, AddedCoverageLines, LossPerUnitPerYear, TelematicsConnectionPremier,BasicAlerts,
              DotRating, HeavyHaul,AppetiteFactorRecommendation, ALPerUnitPremium,
              RecommendedActionStronglyQuote, RecommendedActionQuote, IsMinimumMileageGuaranteed, AlRate, IsRenewalApp ]
    EndStateReasons:
      type: object
      properties:
        state:
          $ref: '#/components/schemas/ApplicationEndState'
        reason:
          type: string
        subReason:
          type: string
        winCarrier:
          type: string
        notes:
          type: string
        comments:
          type: string
        policyIdentifier:
          type: string

    ApplicationReviewTabDetails:
      type: object
      required:
        - tabName
        - tabStatus
      properties:
        tabName:
          $ref: "#/components/schemas/ApplicationReviewTabName"
        tabStatus:
          $ref: "#/components/schemas/ApplicationReviewTabStatus"
    ApplicationReviewTabName:
      type: string
      enum: [ "Incomplete","Ready for Review","Stale","Approved","Declined","Internal","Closed" ]
    ApplicationReviewTabStatus:
      type: string
      enum: [ "Approved","Ready for Review","Needs Attention","Declined","Incomplete","Stale","Closed" ]
    TelematicsDataButtonStatus:
      type: string
      enum: [ "Enabled","Disabled","NotConnected" ]
    ApplicationReviewAggregateCreditByCoverage:
      type: object
      properties:
        autoLiability:
          type: number
          example: 2.3
        autoPhysicalDamage:
          type: number
          example: 2.3
        generalLiability:
          type: number
          example: 2.3
        motorTruckCargo:
          type: number
          example: 2.3
    ApplicationReviewAggregateCreditLimitsByCoverage:
      type: object
      properties:
        autoLiability:
          $ref: "#/components/schemas/ApplicationReviewAggregateCreditLimitsByCoverageData"
        autoPhysicalDamage:
          $ref: "#/components/schemas/ApplicationReviewAggregateCreditLimitsByCoverageData"
        motorTruckCargo:
          $ref: "#/components/schemas/ApplicationReviewAggregateCreditLimitsByCoverageData"
    ApplicationReviewAggregateCreditLimitsByCoverageData:
      type: object
      properties:
        maximum:
          type: number
        minimum:
          type: number
    UpdateApplicationReviewRequest:
      type: object
      properties:
        operations:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
        equipments:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
        drivers:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
        safety:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
        financials:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
        losses:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
        packages:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
        overview:
          $ref: "#/components/schemas/ApplicationReviewPanelInfoPatch"
    TelematicsConnectionState:
      type: string
      enum:
        [
          TelematicsConnectionStateNotInitiated,
          TelematicsConnectionStateRequestSent,
          TelematicsConnectionStateConnected,
        ]
    ApplicationReviewState:
      type: string
      enum:
        [
          ApplicationReviewStatePending,
          ApplicationReviewStateReadyForReview,
          ApplicationReviewStateApproved,
          ApplicationReviewStateDeclined,
          ApplicationReviewStateStale,
          ApplicationReviewStateClosed,
          ApplicationReviewStateRefreshingPremiums
        ]
    ApplicationEndState:
      type: string
      enum:
        [
          Declined,
          Closed,
          Bound,
          Quoted
        ]
    ApplicationState:
      type: string
      enum:
        [
          AppStateUnsubmitted,
          AppStateDeclined,
          AppStatePanic,
          AppStateIndicationGenerated,
          AppStateUnderUWReview,
          AppStateQuoteGenerated,
          AppStatePolicyCreated,
          AppStateApproved,
          AppStateClosed
        ]
    ApplicationReviewPanelWiseInfo:
      type: object
      required:
        - operations
        - equipments
        - drivers
        - safety
        - financials
        - losses
        - packages
      properties:
        operations:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
        equipments:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
        drivers:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
        safety:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
        financials:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
        losses:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
        packages:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
        overview:
          $ref: "#/components/schemas/ApplicationReviewPanelInfo"
    ApplicationReviewPanelInfo:
      type: object
      required:
        - isReviewed
        - merit
        - credit
        - creditByCoverage
        - comments
      properties:
        isReviewed:
          type: boolean
        merit:
          type: integer
          format: int32
          example: -2
        credit:
          type: number
          example: 2.3
        creditByCoverage:
          $ref: "#/components/schemas/ApplicationReviewAggregateCreditByCoverage"
        comments:
          type: string
          example: turpis egestas integer eget aliquet
    ApplicationReviewCloseReasons:
      type: object
      properties:
        reasons:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewCloseReasonObject"
        winCarriers:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationWinCarriersObject"
    ApplicationReviewCloseReasonObject:
      type: object
      required:
        - reason
        - reasonId
        - subReason
        - subReasonId
      properties:
        reason:
          type: string
          example: "Reason 1"
        reasonId:
          type: string
          example: "1"
        subReason:
          type: string
          example: "Sub Reason 1"
        subReasonId:
          type: string
          example: "1"
    ApplicationWinCarriersObject:
      type: object
      required:
        - Id
        - CarrierName
      properties:
        Id:
          type: number
          example: 1
        CarrierName:
          type: string
          example: "NIRVANA TEST AGENCY"
    ApplicationReviewDeclineReasons:
      type: object
      properties:
        reasons:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewDeclineReasonObject"
    ApplicationReviewDeclineReasonObject:
      type: object
      required:
        - reason
        - reasonId
        - subReason
        - subReasonId
        - externalNote
      properties:
        reason:
          type: string
          example: "Reason 1"
        reasonId:
          type: string
          example: "1"
        subReason:
          type: string
          example: "Sub Reason 1"
        subReasonId:
          type: string
          example: "1"
        externalNote:
          type: string
          example: "External Note 1"
        reasonCategory:
          type: string
          enum:
            - Guideline
            - NonGuideline
          example: "Guideline"
    ApplicationReviewDeclineReasonsForm:
      type: object
      required:
        - reasonsArray
        - comments
      properties:
        reasonsArray:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewDeclineReasonObject"
        comments:
          type: string
          example: "Comments 1"
        recommendedActionFeedback:
          type: string
          example: bad account, dont recommended
        isApplicationAgainstUwGuidelines:
          type: boolean
          example: true
    ApplicationIdBoundReasons:
      type: object
      properties:
        reasons:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationIdBoundReasonObject"
    ApplicationIdBoundReasonObject:
      type: object
      required:
        - reason
        - reasonId
        - primaryWinNote
        - secondaryWinNote
        - isActive
        - isRenewal
      properties:
        reason:
          type: string
          example: "Reason 1"
        reasonId:
          type: string
          example: "1"
        primaryWinNote:
          type: string
          example: "Reason Primary Win note"
        secondaryWinNote:
          type: string
          example: "Reason Secondary Win note"
        isActive:
          type: boolean
          example: true
        isRenewal:
          type: boolean
          example: true
    ApplicationReviewCloseReasonsForm:
      type: object
      required:
        - reasonsArray
        - comments
      properties:
        reasonsArray:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewCloseReasonObject"
        comments:
          type: string
          example: "Comments 1"
        winCarrier:
          type: integer
          format: int32
          example: 1
        recommendedActionFeedback:
          type: string
          example: could not quote this account
        intentionToQuote:
          type: boolean
    ApplicationReviewPanelInfoPatch:
      type: object
      properties:
        isReviewed:
          type: boolean
        credit:
          type: number
          example: 2.3
        comments:
          type: string
          example: turpis egestas integer eget aliquet
    ApplicationReviewWidgetSummary:
      type: object
      required:
        - applicationId
        - accountName
        - producerName
        - marketerName
        - marketerEmail
        - effectiveDate
        - agencyName
        - address
        - unitCount
        - dotNumber
        - accountGrade
        - usState
        - additionalInfo
        - pullMvrs
        - isALIncumbent
        - isRenewal
        - fetchAttractScore
      properties:
        applicationId:
          type: string
          example: 9eb370b5-85d9-4544-884a-894806200ac0
        accountName:
          type: string
          example: All Trucking Inc
        producerName:
          type: string
          example: John Doe
        marketerName:
          type: string
          example: John Doe
        marketerEmail:
          type: string
          example: '<EMAIL>'
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        originalEffectiveDate:
          type: string
          format: date
          example: 2021-06-01
        agencyName:
          type: string
          example: Insurance Agents Inc
        address:
          type: string
          example: 813 1st St SE Hickory, North Carolina(NC), 28602
        unitCount:
          type: integer
          example: 50
        dotNumber:
          type: integer
          format: int64
          example: 1002125
        accountGrade:
          $ref: "#/components/schemas/ApplicationReviewAccountGradeProperty"
        usState:
          type: string
          example: NC
        additionalInfo:
          type: string
          example: The effective date should be for 6/7 I also have two drivers that have DC licenses that I couldn't put through. Should I just email them over?
          description: Additional information about the application added by the agents.
        pullMvrs:
          $ref: '../common/spec.yaml#/components/schemas/MVRFlag'
        isALIncumbent:
          type: boolean
          example: true
        isRenewal:
          type: boolean
          example: false
        tspDetails:
          $ref: "#/components/schemas/TspDetails"
        agentSelectedIndicationOption:
          $ref: "#/components/schemas/ApplicationReviewQuote"
        fetchAttractScore:
          type: boolean
          example: false
        cameraSubsidyDetails:
          $ref: "#/components/schemas/CameraSubsidyDetails"
        isEligibleForCameraProgram:
          type: boolean
          example: false
        areCamerasRequired:
          type: boolean
          example: true
        fronter:
          type: string
          example: Falls Lake National Insurance Company
        assignedBD:
          type: string
          example: Ashley Estrada
        salesforceLink:
            type: string
            example: https://nirvanatech.lightning.force.com/lightning/r/Opportunity/006VO00001B7MFBYA3/view
        totalPolicyPremiumUnmodified:
          type: integer
          format: int32
          example: 12300
    TspDetails:
      type: object
      required:
        - tsp
        - isException
      properties:
        tsp:
          type: string
          example: TSPSamsara
        isException:
          type: boolean
          example: false
    CameraSubsidyDetails:
      type: object
      required:
        - numberOfCameras
      properties:
        numberOfCameras:
          type: integer
          example: 12
        subsidyAmount:
          type: number
          format: double
          example: 12.2324

    ApplicationReviewSummaryForm:
      type: object
      properties:
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        numberOfVehicleCameras:
          type: integer
          example: 12
        subsidyAmount:
          type: number
          format: double
          example: 12.2324
    ApplicationReviewDepositAmount:
      type: object
      properties:
        monthlyPaymentDeposit:
          type: number
          format: float
          example: 12300.5
    ApplicationReviewAccountGrade:
      type: object
      required:
        - grade
      properties:
        grade:
          $ref: "#/components/schemas/ApplicationReviewAccountGradeProperty"
    ApplicationReviewAccountGradeProperty:
      type: string
      enum:
        - ""
        - A
        - B
        - C
        - D
        - E
        - F
      example: A
    ApplicationReviewNotes:
      type: object
      required:
        - notes
      properties:
        notes:
          type: string
          example: Approving this review because ...
    ApplicationReviewWidgetMeta:
      type: object
      properties:
        merit:
          type: integer
          format: int32
          example: -2
        credit:
          type: number
          example: 2.3
        autoLiability:
          $ref: "#/components/schemas/ApplicationReviewWidgetCoverageMeta"
        autoPhysicalDamage:
          $ref: "#/components/schemas/ApplicationReviewWidgetCoverageMeta"
        generalLiability:
          $ref: "#/components/schemas/ApplicationReviewWidgetCoverageMeta"
        motorTruckCargo:
          $ref: "#/components/schemas/ApplicationReviewWidgetCoverageMeta"
        flags:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewWidgetMetaFlag"
    ApplicationReviewWidgetCoverageMeta:
      type: object
      properties:
        merit:
          type: integer
          format: int32
          example: -2
        credit:
          type: number
          example: 2.3
    ApplicationReviewWidgetMetaFlag:
      type: object
      properties:
        weight:
          type: integer
          format: int32
          example: 2
        title:
          type: string
          example: "Flag 1"
        description:
          type: string
          example: "Flag 1 description"
      required:
        - weight
        - title
    ApplicationReviewDocuments:
      type: object
      properties:
        files:
          type: array
          items:
            $ref: "../common/spec.yaml#/components/schemas/FileMetadata"
      required:
        - files
    ApplicationReviewWidgetBase:
      type: object
      properties:
        meta:
          $ref: "#/components/schemas/ApplicationReviewWidgetMeta"
    ApplicationReviewOperationsYearsInBusiness:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          required:
            - value
            - years
            - months
          properties:
            value:
              type: integer
              example: 3
            years:
              type: integer
              example: 3
            months:
              type: integer
              example: 3
            widgetStatus:
              $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetStatus"
    ApplicationReviewOperationsYearsInBusinessForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewOperationsProjectedInformation:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          required:
            - units
            - mileage
            - powerUnits
            - version
          properties:
            units:
              type: integer
              example: 50
            powerUnits:
              $ref: "#/components/schemas/ApplicationReviewProjectedInformationPowerUnits"
            mileage:
              $ref: "#/components/schemas/ApplicationReviewProjectedInformationMileage"
            version:
              $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetVersion"
            vinVisibility:
              $ref: "#/components/schemas/ApplicationReviewVinVisibility"
            isMinimumMileageGuaranteed:
              type: boolean
    ApplicationReviewOperationsProjectedInformationForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: object
              properties:
                mileage:
                  type: integer
                  example: 40000
                units:
                  type: integer
                  example: 40000
                reason:
                  $ref: "#/components/schemas/MileageEstimateReason"
                isMinimumMileageGuaranteed:
                  type: boolean
    ApplicationReviewProjectedInformationMileage:
      type: object
      required:
        - value
        - telematics
      properties:
        value:
          type: integer
          example: 40000
        telematics:
          type: integer
          example: 40000
        override:
          type: integer
          example: 42000
        reasons:
          $ref: "#/components/schemas/MileageEstimateReason"
        mileageEstimate:
          $ref: "#/components/schemas/MileageEstimate"
    MileageEstimate:
      type: object
      required:
        - status
      properties:
        status:
          $ref: "#/components/schemas/FeatureStatus"
        data:
          $ref: "#/components/schemas/MileageEstimateData"
    MileageEstimateData:
      type: object
      required:
        - trackedEquipment
        - estimatedMileage
        - mileageRange
        - mileageEstimateFeature
      properties:
        trackedEquipment:
          $ref: "#/components/schemas/TrackedEquipment"
        estimatedMileage:
          type: number
          example: 400.00
        mileageRange:
          $ref: "#/components/schemas/MileageRange"
        mileageEstimateFeature:
          $ref: "../common/spec.yaml#/components/schemas/MileageEstimateFeature"
    MileageRange:
      type: object
      required:
        - lowerBoundMileage
        - upperBoundMileage
      properties:
        lowerBoundMileage:
          type: number
          example: 400.00
        upperBoundMileage:
          type: number
          example: 400.00
    TrackedEquipment:
      type: object
      required:
        - telematicsEquipmentsCount
        - agentsEquipmentsCount
      properties:
        telematicsEquipmentsCount:
          type: integer
          example: 40000
        agentsEquipmentsCount:
          type: integer
          example: 40000
    FeatureStatus:
      type: string
      enum:
        - "Success"
        - "Processing"
        - "NoDataAvailable"
    MileageEstimateReasons:
      type: array
      items:
        $ref: "#/components/schemas/MileageEstimateReason"
    MileageEstimateReason:
      type: object
      required:
        - category
      properties:
        reason:
          $ref: "#/components/schemas/MileageEstimateReasonsData"
        category:
          $ref: "#/components/schemas/MileageEstimateReasonsCategory"
        additionalDetails:
          type: string
    MileageEstimateReasonsData:
      type: string
      enum:
        - "Conservative"
        - "Seasonal Business"
        - "IFTAs"
        - "Declining fleet"
        - "Agent is conservative"
        - "New contract"
        - "Other"
        - "Growing fleet"
        - "Negotiation"
    MileageEstimateReasonsCategory:
      type: string
      enum:
        - "Higher"
        - "Lower"
        - "Same"
        - "NoDataAvailable"
    ApplicationReviewProjectedInformationPowerUnits:
      type: object
      required:
        - value
      properties:
        value:
          type: integer
          example: 40000
        override:
          type: integer
          example: 42000
    ApplicationReviewOperationsGaragingLocation:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          required:
            - locations
          properties:
            locations:
              type: object
              properties:
                value:
                  type: array
                  items:
                    $ref: "#/components/schemas/ApplicationReviewOperationsGaragingLocationItem"
                override:
                  type: array
                  items:
                    $ref: "#/components/schemas/ApplicationReviewOperationsGaragingLocationItem"
              required:
                - value
    ApplicationReviewOperationsGaragingLocationForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: object
              properties:
                locations:
                  type: array
                  items:
                    $ref: "#/components/schemas/ApplicationReviewOperationsGaragingLocationFormItem"
              required:
                - locations
    ApplicationReviewOperationsGaragingLocationItem:
      type: object
      properties:
        address:
          type: string
        type:
          type: string
          enum:
            - garaging
            - parking
        geojsonFeature:
          type: object
    ApplicationReviewOperationsGaragingLocationFormItem:
      type: object
      properties:
        address:
          type: string
        type:
          type: string
          enum:
            - garaging
            - parking
        coordinates:
          type: object
          properties:
            latitude:
              type: number
              format: double
            longitude:
              type: number
              format: double
    ApplicationReviewOperationsTerminalLocations:
      allOf:
        - type: object
          required:
            - locations
          properties:
            locations:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewOperationsTerminalLocationItem"
    ZipCodeDetails:
      type: object
      properties:
        city:
          type: string
        state:
          type: string
    SelectApplicationReviewOperationsTerminalLocationForm:
      type: object
      properties:
        selectedLocation:
          $ref: "#/components/schemas/ApplicationReviewOperationsTerminalLocationItem"
    UpdateApplicationReviewOperationsTerminalLocations:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: object
              properties:
                value:
                  type: array
                  items:
                    $ref: "#/components/schemas/UpdateApplicationReviewOperationsTerminalLocationItem"
              required:
                - value
    UpdateApplicationReviewOperationsTerminalLocationItem:
      type: object
      required:
        - addressLineOne
        - usState
        - zipCode
        - type
        - isGated
        - isGuarded
      properties:
        addressLineOne:
          type: string
        addressLineTwo:
          type: string
        usState:
          type: string
        zipCode:
          type: string
        type:
          type: string
          enum:
            - Dock
            - DropLot
            - Office
            - Terminal
        isGated:
          type: boolean
        isGuarded:
          type: boolean
        isDeletedByUW:
          type: boolean
        isCreatedByUW:
          type: boolean
        cargoTerminalSchedule:
          $ref: '../common/spec.yaml#/components/schemas/CargoTerminalSchedule'
    ApplicationReviewOperationsTerminalLocationItem:
      type: object
      required:
        - address
        - addressLineOne
        - usState
        - zipCode
        - type
        - isGated
        - isGuarded
        - isSelectedForRating
        - isValidForRating
        - isDeletedByUW
        - isCreatedByUW
      properties:
        addressLineOne:
          type: string
        addressLineTwo:
          type: string
        address:
          type: string
        usState:
          type: string
        zipCode:
          type: string
        zipCodeDetail:
          $ref: "#/components/schemas/ZipCodeDetails"
        type:
          type: string
          enum:
            - Dock
            - DropLot
            - Office
            - Terminal
        isGated:
          type: boolean
        isGuarded:
          type: boolean
        isSelectedForRating:
          type: boolean
        isValidForRating:
          type: boolean
        isDeletedByUW:
          type: boolean
        isCreatedByUW:
          type: boolean
        cargoTerminalSchedule:
          $ref: "../common/spec.yaml#/components/schemas/CargoTerminalSchedule"
    ApplicationReviewOperationsCommodities:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              $ref: "#/components/schemas/ApplicationReviewOperationsCommoditiesData"
          required:
            - data
    ApplicationReviewOperationsCommoditiesForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              $ref: "#/components/schemas/ApplicationReviewOperationsCommoditiesData"
    ApplicationReviewOperationsCommoditiesData:
      type: object
      properties:
        primary:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewOperationsCommoditiesDataPrimaryItem"
        additional:
          type: object
          properties:
            commodities:
              type: string
            percentageOfHauls:
              type: number
              format: float
          required:
            - commodities
            - percentageOfHauls
      required:
        - primary
        - additional
    ApplicationReviewOperationsCommoditiesDataPrimaryItem:
      type: object
      properties:
        category:
          type: string
        categoryLabel:
          type: string
        commodity:
          type: string
        commodityLabel:
          type: string
        commodityEnum:
          type: string
        commodityEnumLabel:
          type: string
        commodityClass:
          type: string
          enum:
            - "A"
            - "B"
            - "C"
            - "D"
            - "E"
            - "F"
        percentageOfHauls:
          type: number
          format: float
        avgDollarValueHauled:
          type: integer
          format: int64
        maxDollarValueHauled:
          type: integer
          format: int64
      required:
        - category
        - categoryLabel
        - commodityLabel
        - commodityClass
        - percentageOfHauls
        - avgDollarValueHauled
        - maxDollarValueHauled
    ApplicationReviewOperationsCommoditiesSupportedOperations:
      $ref: "../common/spec.yaml#/components/schemas/SupportedOperationsRecordV2"
    ApplicationReviewOperationsFleetHistory:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            insuranceCurrent:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewOperationsFleetHistoryInsuranceCurrentItem"
            insuranceHistory:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewOperationsFleetHistoryInsuranceHistoryItem"
            insuranceLimits:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewOperationsFleetHistoryInsuranceLimitItem"
            powerUnitsTrend:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewOperationsFleetHistoryPowerUnitsTrendItem"
            carrierLoyaltyInsight:
              $ref: "#/components/schemas/ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummary"
            widgetStatus:
              $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetStatus"
          required:
            - insuranceCurrent
            - insuranceHistory
            - insuranceLimits
            - powerUnitsTrend
    ApplicationReviewOperationsFleetHistoryInsuranceCurrentItem:
      type: object
      properties:
        insuranceType:
          type: string
        carrier:
          type: string
          example: ABC Insurance
        effectiveDate:
          type: string
          format: date
          example: 2005-11-01
        cancellationDate:
          type: string
          format: date
          example: 2006-11-01
      required:
        - carrier
        - effectiveDate
    ApplicationReviewOperationsFleetHistoryInsuranceHistoryItem:
      type: object
      properties:
        insuranceType:
          type: string
        carrier:
          type: string
          example: ABC Insurance
        startDate:
          type: string
          format: date
          example: 2005-11-01
        endDate:
          type: string
          format: date
          example: 2006-11-01
      required:
        - carrier
        - startDate
        - endDate
    ApplicationReviewOperationsFleetHistoryInsuranceLimitItem:
      type: object
      properties:
        coverageType:
          type: string
          example: BIPD/Excess
        shouldFlagCoverageType:
          type: boolean
          example: false
        carrier:
          type: string
          example: ABC Insurance
        insuranceLimit:
          type: number
          format: float
          example: 1000000
        effectiveDate:
          type: string
          format: date
          example: 2006-11-01
      required:
        - coverageType
        - shouldFlagCoverageType
        - carrier
    ApplicationReviewOperationsFleetHistoryPowerUnitsTrendItem:
      type: object
      properties:
        date:
          type: string
          format: date
          example: 2006-11-01
        unitCount:
          type: integer
          format: int32
          example: 20
      required:
        - date
        - unitCount
    ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItem:
      type: object
      properties:
        numberOfYears:
          type: integer
          format: int32
          example: 3
        durationType:
          type: string
          enum:
            - RECENT
            - OVERALL
            - OLD
        totalGapDays:
          type: integer
          format: int32
          example: 61
        carrierSwitches:
          type: integer
          format: int32
          example: 2
      required:
        - numberOfYears
        - durationType
        - totalGapDays
        - carrierSwitches
    ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummary:
      type: object
      properties:
        overallScore:
          type: string
          example: AVERAGE
        carrierLoyaltySummary:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItem"
    ApplicationReviewOperationsFleetHistoryForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewOperationsRadiusOfOperation:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            bucketedData:
              type: object
              properties:
                MileageRadiusBucketZeroToFifty:
                  $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationBucketedDataItem"
                MileageRadiusBucketFiftyToTwoHundred:
                  $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationBucketedDataItem"
                MileageRadiusBucketTwoHundredToFiveHundred:
                  $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationBucketedDataItem"
                MileageRadiusBucketFiveHundredPlus:
                  $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationBucketedDataItem"
                MileageRadiusBucketUnknown:
                  $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationBucketedDataItem"
              required:
                - MileageRadiusBucketZeroToFifty
                - MileageRadiusBucketFiftyToTwoHundred
                - MileageRadiusBucketTwoHundredToFiveHundred
                - MileageRadiusBucketFiveHundredPlus
                - MileageRadiusBucketUnknown
          required:
            - bucketedData
    ApplicationReviewOperationsRadiusOfOperationForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: object
              properties:
                bucketedData:
                  type: object
                  properties:
                    MileageRadiusBucketZeroToFifty:
                      $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem"
                    MileageRadiusBucketFiftyToTwoHundred:
                      $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem"
                    MileageRadiusBucketTwoHundredToFiveHundred:
                      $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem"
                    MileageRadiusBucketFiveHundredPlus:
                      $ref: "#/components/schemas/ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem"
                  required:
                    - MileageRadiusBucketZeroToFifty
                    - MileageRadiusBucketFiftyToTwoHundred
                    - MileageRadiusBucketTwoHundredToFiveHundred
                    - MileageRadiusBucketFiveHundredPlus
              required:
                - bucketedData
    ApplicationReviewOperationsRadiusOfOperationBucketedDataItem:
      type: object
      properties:
        value:
          type: integer
          format: int32
          minimum: 0
          maximum: 100
          example: 50
        telematics:
          type: integer
          format: int32
          minimum: 0
          maximum: 100
          example: 51
        override:
          type: integer
          format: int32
          minimum: 0
          maximum: 100
          example: 52
      required:
        - value
    ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem:
      type: object
      properties:
        override:
          type: integer
          format: int32
          minimum: 0
          maximum: 100
          example: 52
      required:
        - override
    ApplicationReviewOperationsOperatingClasses:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            equipmentInfo:
              $ref: "#/components/schemas/ApplicationReviewOperationsOperatingClassesEquipmentInfo"
            marginalOps:
              type: array
              items:
                type: string
            comment:
              type: string
          required:
            - equipmentInfo
    ApplicationReviewOperationsOperatingClassesForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewOperationsOperatingClassesEquipmentInfo:
      type: object
      properties:
        distribution:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewOperationsOperatingClassesEquipmentInfoDistributionItem"
        primaryOperatingClass:
          type: string
          example: OperatingClassDryVan
        primaryCommodity:
          type: string
          example: CommodityHauledElectronics
      required:
        - distribution
        - primaryOperatingClass
        - primaryCommodity
    ApplicationReviewOperationsOperatingClassesEquipmentInfoDistributionItem:
      type: object
      properties:
        operatingClass:
          type: string
          example: OperatingClassDryVan
        value:
          type: integer
          format: int32
          example: 30
      required:
        - operatingClass
        - value
    ApplicationReviewOperationsCustomers:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewOperationsCustomersDataItem"
          required:
            - data
    ApplicationReviewOperationsCustomersDataItem:
      type: object
      properties:
        name:
          type: string
        frequency:
          type: integer
          format: int32
          example: 20
      required:
        - name
        - frequency
    ApplicationReviewOperationsVehicleZones:
      type: object
      properties:
        vehicleZones:
          type: array
          items:
            $ref: "#/components/schemas/VehicleZoneRecord"
        defaultZones:
          type: object
          properties:
            startZone:
              $ref: '#/components/schemas/VehicleZone'
            endZone:
              $ref: '#/components/schemas/VehicleZone'
          required:
            - startZone
            - endZone
        options:
          type: object
          properties:
            startZones:
              type: array
              items:
                $ref: "#/components/schemas/VehicleZone"
            endZones:
              type: array
              items:
                $ref: "#/components/schemas/VehicleZone"
          required:
            - startZones
            - endZones
      required:
        - vehicleZones
        - defaultZones
        - options
    ApplicationReviewOperationsVehicleZonesForm:
      type: object
      properties:
        data:
          type: object
          properties:
            vehicleZones:
              type: array
              items:
                $ref: "#/components/schemas/VehicleZoneRecord"
          required:
            - vehicleZones
      required:
        - data
    ApplicationReviewOperationsHazardZones:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            hazardZoneDistance:
              type: object
              properties:
                miles:
                  type: number
                  format: float
                  example: 14
                percentage:
                  type: number
                  format: float
                  example: 14
            hazardZoneDuration:
              type: object
              properties:
                hours:
                  type: number
                  format: float
                  example: 14
                percentage:
                  type: number
                  format: float
                  example: 14
            hazardZoneStates:
              type: array
              items:
                type: string
                example: "CA"
                $ref: "#/components/schemas/ApplicationReviewOperationsHazardZoneState"
          required:
            - hazardZoneDistance
            - hazardZoneDuration
            - hazardZoneStates
    ApplicationReviewOperationsHazardZoneState:
      type: object
      properties:
        state:
          $ref: "../common/spec.yaml#/components/schemas/USState"
        miles:
          type: number
          format: float
          example: 14
        percentage:
          type: number
          format: float
          example: 14
    ApplicationReviewOperationsHazardZonesForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewEquipmentsUnits:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            units:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewEquipmentsUnitsItem"
            summary:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewEquipmentsSummaryItem"
            widgetStatus:
              $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetStatus"
          required:
            - units
            - summary
    ApplicationReviewEquipmentsUnitsItem:
      type: object
      properties:
        vin:
          type: string
          example: 1XPXDP9X5CD135417
        statedValue:
          type: integer
          format: int32
          example: 200000
        statedValueOverride:
          type: integer
          format: int32
          example: 200000
        make:
          type: string
          example: FREIGHTLINER
        model:
          type: string
          example: 2015
        modelYear:
          type: string
          example: 2015
        manufacturer:
          type: string
          example: DAIMLER TRUCKS NORTH AMERICA LLC
        trim:
          type: string
          example: 125" SLEEPERCAB
        vehicleType:
          type: string
          example: VehicleTypeTruck
        bodyClass:
          type: string
          example: TRUCK-TRACTOR
        isoVehicleTypeOverride:
          $ref: "#/components/schemas/ISOVehicleType"
        isoWeightGroupOverride:
          $ref: "#/components/schemas/ISOVehicleWeightGroup"
        vinProblem:
          $ref: "#/components/schemas/ApplicationReviewVinProblem"
        isSkipped:
          type: boolean
          example: false
        isDeletedByUW:
          type: boolean
          example: false
        isCreatedByUW:
          type: boolean
          example: false
      required:
        - vin
        - statedValue
        - isSkipped
        - isDeletedByUW
        - isCreatedByUW
    ApplicationReviewEquipmentsSummaryItem:
      type: object
      properties:
        unitType:
          type: string
        unitCount:
          type: integer
          format: int32
        telematicsUnitCount:
          type: integer
          format: int32
        averageYear:
          type: integer
          format: int32
        ownerOperatedPercent:
          type: number
          format: float
        tiv:
          type: number
          format: float
      required:
        - unitType
        - unitCount
        - tiv
    ApplicationReviewEquipmentsUnitsForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              $ref: "#/components/schemas/UpdateAppReviewEquipmentListFormData"
    UpdateAppReviewEquipmentListFormData:
      type: object
      properties:
        units:
          type: array
          items:
            $ref: "#/components/schemas/AppReviewEquipmentUpdateFormItem"
      required:
        - units
    ApplicationReviewEquipmentsOwnerOperators:
      type: object
      properties:
        total:
          type: integer
          format: int32
      required:
        - total
    ApplicationReviewEquipmentsAdditionalInfoUnits:
      type: object
      properties:
        totalOwnerOperators:
          type: integer
          format: int32
        percentageOfSubhaul:
          type: number
          format: float
          example: 0.32
      required:
        - totalOwnerOperators
    ApplicationReviewEquipmentsSafetyUsage:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewEquipmentsSafetyUsageItem"
          required:
            - data
    ApplicationReviewEquipmentsSafetyUsageItem:
      type: object
      properties:
        camera:
          type: string
        usage:
          type: number
          format: float
      required:
        - camera
        - usage
    ApplicationReviewDriversList:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            drivers:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewDriversListItem"
            summary:
              $ref: "#/components/schemas/ApplicationReviewDriversListSummary"
            widgetStatus:
              $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetStatus"
            effectiveDate:
              type: string
              format: date
              example: 2021-06-01
            experienceDataFromMVR:
              type: boolean
              example: false
          required:
            - drivers
            - summary
            - effectiveDate
            - experienceDataFromMVR
    ApplicationReviewDriversListItem:
      type: object
      properties:
        dlNumber:
          type: string
          example: 1XPXDP9X5CD135417
        name:
          type: string
          example: Greg Washington
        nameOverride:
          type: string
          example: John Doe
        firstName:
          type: string
          example: Greg
        firstNameOverride:
          type: string
          example: John
        lastName:
          type: string
          example: Washington
        lastNameOverride:
          type: string
          example: Doe
        usState:
          type: string
          example: Texas
        dateHired:
          type: string
          format: date
          example: 2005-11-01
        dateHiredOverride:
          type: string
          format: date
          example: 2005-11-01
        dateOfBirth:
          type: string
          format: date
          example: 1987-12-01
        dateOfBirthOverride:
          type: string
          format: date
          example: 1986-12-01
        experienceStartDate:
          type: string
          format: date
          example: 1999-10-01
        yearsOfExperienceAgentInput:
          type: string
        yearsOfExperienceMVR:
          type: string
        isExperienceMismatch:
          type: boolean
          example: false
        experienceStartDateOverride:
          type: string
          format: date
          example: 1999-10-01
        mvrScore:
          type: integer
          format: int32
          example: 38
        safetyScore:
          type: integer
          format: int32
          example: 94
        mvrStatus:
          type: string
          enum:
            - success
            - failure
            - uninitiated
            - in_progress
        attractScore:
          type: integer
          format: int32
          example: 999
        mvrProblem:
          $ref: "#/components/schemas/ApplicationReviewDriverMvrProblem"
        mvrDetails:
          $ref: "#/components/schemas/ApplicationReviewDriverMvrDetails"
        isMexicanDriver:
          type: boolean
        isDeletedByUW:
          type: boolean
          example: false
        isCreatedByUW:
          type: boolean
          example: true
      required:
        - dlNumber
        - usState
        - dateHired
        - mvrStatus
        - isMexicanDriver
        - isDeletedByUW
        - isCreatedByUW
    ApplicationReviewDriverMvrProblem:
      type: object
      properties:
        isResolved:
          type: boolean
        isReviewed:
          type: boolean
        error:
          type: string
    ApplicationReviewVinProblem:
      type: object
      properties:
        isResolved:
          type: boolean
        isReviewed:
          type: boolean
        systemReviewed:
          type: boolean
        fixes:
          $ref: "#/components/schemas/EquipmentListVinProblemFixes"
        error:
          type: string
    ApplicationReviewDriverMvrDetails:
      type: object
      properties:
        dateIssued:
          type: string
          format: date
          example: 2005-11-01
        dateExpires:
          type: string
          format: date
          example: 2005-11-01
        dateOfBirth:
          type: string
          format: date
          example: 1987-12-01
        dlState:
          type: string
          example: Texas
        violations:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewDriverMvrViolation"
        movingViolationCount:
          type: number
          format: float
          example: 1
    ApplicationReviewDriverMvrViolation:
      type: object
      properties:
        date:
          type: string
          format: date
          example: 2005-11-01
        code:
          type: string
          example: E50
        description:
          type: string
          example: "Driving under the influence of alcohol with BAC at or over .10"
        type:
          type: string
          example: Driving Under the Influence
        details:
          type: string
          example: Driving Under the Influence
        points:
          type: integer
          format: int32
          example: 38
        isMovingViolation:
          type: boolean
          example: true
    ApplicationReviewDriversListSummary:
      type: object
      properties:
        numberOfDrivers:
          type: integer
          format: int32
        averageTenure:
          type: number
          format: float
        tenureTurnover:
          type: number
          format: float
        percentageOfB1Drivers:
          type: number
          format: float
      required:
        - averageTenure
        - tenureTurnover
        - numberOfDrivers
    ApplicationReviewDriversListForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              $ref: "#/components/schemas/ApplicationReviewDriversListFormData"
    ApplicationReviewDriversListFormData:
      type: object
      properties:
        drivers:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewDriversListFormItem"
      required:
        - drivers
    ApplicationReviewDriversListFormItem:
      type: object
      properties:
        dlNumber:
          type: string
          example: 1XPXDP9X5CD135417
        usState:
          type: string
          example: TX
        dateHired:
          type: string
          format: date
          example: 2005-11-01
        experienceStartDate:
          type: string
          format: date
          example: 1999-10-01
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Doe
        dateOfBirth:
          type: string
          format: date
          example: 1987-12-01
      required:
        # DL number and state uniquely identifies a driver
        - dlNumber
        - usState
        - dateHired
    ApplicationReviewDriverUpdateRequest:
      type: object
      required:
        # DL number and state uniquely identifies a driver
        - dlNumber
        - usStateCode
      properties:
        dlNumber:
          type: string
          example: 1XPXDP9X5CD135417
        usStateCode:
          type: string
          example: TX
        isDeletedByUW:
          type: boolean
          example: false
    ApplicationReviewDriverCreateRequest:
      type: object
      required:
        # DL number and state uniquely identifies a driver
        - dlNumber
        - usStateCode
        - dateHired
      properties:
        dlNumber:
          type: string
          example: 1XPXDP9X5CD135417
        usStateCode:
          type: string
          example: TX
        dateHired:
          type: string
          format: date
          example: 2005-11-01
        experienceStartDate:
          type: string
          format: date
          example: 1999-10-01
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Doe
        dateOfBirth:
          type: string
          format: date
          example: 1987-12-01
    AppReviewEquipmentAddRequest:
      type: object
      required:
        - vin
        - statedValue
      properties:
        vin:
          type: string
          example: 1234
        statedValue:
          type: integer
          example: 12345
    AppReviewEquipmentUpdateFormItem:
      type: object
      required:
        - vin
        - statedValue
        - isDeletedByUW
      properties:
        vin:
          type: string
          example: 1234
        statedValue:
          type: integer
          format: int32
          example: 12345
        isDeletedByUW:
          type: boolean
          example: false
        fixes:
          $ref: "#/components/schemas/EquipmentListVinProblemFixes"
    EquipmentListVinProblemFixes:
      type: object
      properties:
        fixedMake:
          type: string
        fixedModel:
          type: string
        fixedModelYear:
          type: string
        fixedWeightClass:
          type: string
        fixedBodyClass:
          $ref: "#/components/schemas/VehicleBodyClass"
        fixedVehicleType:
          $ref: "#/components/schemas/VehicleType"
        isoVehicleTypeOverride:
          $ref: "#/components/schemas/ISOVehicleType"
        isoWeightGroupOverride:
          $ref: "#/components/schemas/ISOVehicleWeightGroup"
    ApplicationReviewSafetyCrashRecord:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          required:
            - crashRecordSummary
            - crashRecordHistory
          properties:
            crashRecordSummary:
              $ref: "../common/spec.yaml#/components/schemas/CrashRecordSummary"
            crashRecordHistory:
              $ref: "../common/spec.yaml#/components/schemas/CrashRecordHistory"
            isFurtherReviewRequired:
              type: boolean
              example: false

    ApplicationReviewSafetyCrashRecordForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewSafetyISSScoreTrend:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            trend:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewSafetyISSScoreTrendItem"
          required:
            - trend
    ApplicationReviewSafetyISSScoreTrendItem:
      type: object
      properties:
        timestamp:
          type: string
          format: date
        score:
          type: number
          format: float
      required:
        - timestamp
        - score
    # todo deprecate this soon once we fully migrate to ApplicationReviewSafetyScoreV2
    ApplicationReviewSafetyScore:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            score:
              type: number
              format: float
            threshold:
              type: number
              format: float
            trend:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewSafetyScoreTrendItem"
            status:
              $ref: "#/components/schemas/FeatureStatus"
            riskScore:
              $ref: "../common/spec.yaml#/components/schemas/RiskScore"
            version:
              $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetVersion"
            scoreSelectedForReview:
              type: number
              format: float
            scoreSelectedForRating: # deprecate me (@jitesh)
              type: number
              format: float
            isShortHaul:
              type: boolean
              example: false
            isSafetyScoreEditable:
              type: boolean
              example: false
          required:
            - score
            - threshold
            - trend
            - version
            - isSafetyScoreEditable
    ApplicationReviewSafetyScoreV2:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            status:
              $ref: "#/components/schemas/FeatureStatus"
            totalVinCount:
              type: integer
            riskScore:
              $ref: "../common/spec.yaml#/components/schemas/RiskScore"
            version:
              $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetVersion"
            isShortHaul:
              type: boolean
              example: false
            canEdit:
              type: boolean
              example: false
          required:
            - status
            - version
            - riskScore
            - totalVinCount
            - canEdit
    ApplicationReviewSafetyScoreTrendItem:
      type: object
      properties:
        timestamp:
          type: string
          format: date
        score:
          type: number
          format: float
        speeding:
          type: number
          format: float
        harshDriving:
          type: number
          format: float
      required:
        - timestamp
        - score
    # Deprecate after migration to the v2 endpoint for safety score update
    ApplicationReviewSafetyScoreForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            form:
              $ref: "#/components/schemas/ApplicationReviewSafetyScoreFormData"
    ApplicationReviewSafetyScoreFormV2:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            form:
              $ref: "#/components/schemas/UpdateSafetyScoreRequest"
    ApplicationReviewSafetyScoreFormData:
      type: object
      properties:
        scoreSelectedForRating:
          type: number
          format: float
        scoreSelectedForReview:
          $ref: "#/components/schemas/ApplicationReviewSafetyScoreSelectedForRating"
    UpdateSafetyScoreRequest:
      type: object
      required:
        - newScoreTimestamp
        - changeReason
      properties:
        newScoreTimestamp:
          type: string
          format: date-time
        changeReason:
          type: string
    ApplicationReviewSafetyBasicScoreThreshold:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              $ref: "../common/spec.yaml#/components/schemas/BasicScoreThresholds"
          required:
            - data
    ApplicationReviewSafetyBasicScoreThresholdForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewSafetyBasicScoreTrend:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewSafetyBasicScoreTrendItem"
          required:
            - data
    ApplicationReviewSafetyBasicScoreTrendItem:
      type: object
      properties:
        date:
          type: string
          format: date
        unsafeDriving:
          type: number
          format: float
        controlledSubstance:
          type: number
          format: float
        hosCompliance:
          type: number
          format: float
        hmCompliance:
          type: number
          format: float
        crashIndicator:
          type: number
          format: float
        driverFitness:
          type: number
          format: float
        vehicleMaintenance:
          type: number
          format: float
      required:
        - date
    ApplicationReviewSafetyBasicScoreTrendForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewSafetyISSScoreTrendForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewSafetyOOSViolations:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            startDate:
              type: string
              format: date
            endDate:
              type: string
              format: date
            data:
              $ref: "../common/spec.yaml#/components/schemas/OOSViolations"
          required:
            - startDate
            - endDate
            - data
    ApplicationReviewSafetyOOSViolationsForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewSafetySevereViolations:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            violations:
              $ref: "../common/spec.yaml#/components/schemas/SevereViolations"
          required:
            - violations
    ApplicationReviewSafetySevereViolationsForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewSafetyDotRating:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            safetyRating:
              type: string
              enum:
                - Satisfactory
                - Unsatisfactory
                - Conditional
                - Unrated
            effectiveDate:
              type: string
              format: date
    ApplicationReviewSafetyDotRatingForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewFinancialsData:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewLossSummary:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            parsedLossRunStatusInfo:
              $ref: "#/components/schemas/ApplicationReviewLossSummaryParsedLossRunStatusInfo"
            value:
              type: array
              items:
                type: object
                properties:
                  coverageType:
                    type: string
                  summary:
                    type: array
                    items:
                      $ref: "#/components/schemas/ApplicationReviewLossSummaryItemRecord"
                required:
                  - coverageType
                  - summary
            isPUCountOverrideApplied:
              type: boolean
              example: false
            fmcsaLastFetchDate:
              type: string
              format: date
              example: 2016-11-25
            isFurtherReviewRequired:
              type: boolean
              example: false
          required:
            - parsedLossRunStatusInfo
            - value
    ApplicationReviewLossSummaryItemRecord:
      type: object
      properties:
        policyPeriodStartDate:
          type: string
          format: date
          example: 2016-11-25
        policyPeriodEndDate:
          type: string
          format: date
          example: 2017-11-25
        numberOfPowerUnits:
          type: integer
          format: int32
          example: 28
        numberOfPowerUnitsOverride:
          type: integer
          format: int32
          example: 29
        lossIncurred:
          type: integer
          format: int32
          example: 23002
        lossIncurredOverride:
          type: integer
          format: int32
          example: 23003
        numberOfClaims:
          type: integer
          format: int32
          example: 38
        numberOfClaimsOverride:
          type: integer
          format: int32
          example: 39
        parsedLossIncurred:
          type: integer
          format: int32
          example: 23004
        parsedLossState:
          $ref: "#/components/schemas/ParsedLossState"
        parsedLossStateOverride:
          $ref: "#/components/schemas/ParsedLossState"
        parsedLossStateLNISource:
          type: array
          items:
            type: string
            example: Sentry
      required:
        - policyPeriodStartDate
        - policyPeriodEndDate
        - numberOfPowerUnits
        - lossIncurred
        - numberOfClaims
    ApplicationReviewLossSummaryForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: object
              properties:
                value:
                  type: array
                  items:
                    type: object
                    properties:
                      coverageType:
                        type: string
                      summary:
                        type: array
                        items:
                          $ref: "#/components/schemas/ApplicationReviewLossSummaryFormItemRecord"
                    required:
                      - coverageType
                      - summary
              required:
                - value
    ApplicationReviewLossSummaryFormItemRecord:
      type: object
      properties:
        policyPeriodStartDate:
          type: string
          format: date
          example: 2016-11-25
        policyPeriodEndDate:
          type: string
          format: date
          example: 2017-11-25
        numberOfPowerUnitsOverride:
          type: integer
          format: int32
          example: 29
        lossIncurredOverride:
          type: integer
          format: int32
          example: 23003
        numberOfClaimsOverride:
          type: integer
          format: int32
          example: 39
      required:
        - policyPeriodStartDate
        - policyPeriodEndDate
    ApplicationReviewLossSummaryV2Request:
      type: object
      properties:
        coverageParams:
          type: array
          items:
            $ref: "#/components/schemas/LossSummaryCoverageParams"
      required:
        - coverageParams
    LossSummaryCoverageParams:
      type: object
      properties:
        coverageType:
          $ref: "../common/spec.yaml#/components/schemas/CoverageType"
        preClaimDeductible:
          type: number
        requestedPremiumPerPU:
          type: number
      required:
        - coverageType
        - preClaimDeductible
        - requestedPremiumPerPU
    ApplicationReviewLossSummaryV2:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            parsingStatus:
              $ref: "#/components/schemas/ParsedLossStateV2"
            latestCoverageSummaries:
              type: array
              items:
                $ref: "#/components/schemas/LatestCoverageSummary"
          required:
            - parsingStatus
            - latestCoverageSummaries
    ApplicationReviewLossSummaryItemRecordV2:
      type: object
      properties:
        periodStartDate:
          type: string
          format: date
          example: 2016-11-25
        periodEndDate:
          type: string
          format: date
          example: 2017-11-25
        numberOfPowerUnits:
          $ref: "#/components/schemas/LossValueWithOverride"
        grossLoss:
          $ref: "#/components/schemas/LossValueWithOverride"
        numberOfClaims:
          $ref: "#/components/schemas/LossValueWithOverride"
        lossRatio:
          type: number
          format: float
          example: 80.2
        tags:
          type: array
          items:
            type: string
            enum:
              - FileOutOfDate
              - FileMissing
      required:
        - periodStartDate
        - periodEndDate
        - numberOfPowerUnits
        - grossLoss
        - numberOfClaims
        - lossRatio
    LossValueWithOverride:
      type: object
      properties:
        value:
          type: number
        valueSource:
          $ref: "#/components/schemas/LossRunValueSource"
        override:
          type: number
      required:
        - value
        - valueSource
    LatestCoverageSummary:
      type: object
      properties:
        coverageType:
          $ref: "../common/spec.yaml#/components/schemas/CoverageType"
        summary:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewLossSummaryItemRecordV2"
        lossRatioAggregates:
          $ref: "#/components/schemas/LossRatioAggregates"
        averages:
          $ref: "#/components/schemas/LossRunAverages"
      required:
        - coverageType
        - summary
        - lossRatioAggregates
        - averages
    LossRatioAggregates:
      type: array
      items:
        type: object
        properties:
          periodLabel:
            type: string
            example: "3 years (2021 - 2024)"
          valuePercent:
            type: number
            example: 59
        required:
          - periodLabel
          - valuePercent
    LossRunAverages:
      type: object
      properties:
        averageClaimSize:
          type: number
          format: float
          example: 222222.22
        averageBurnRate:
          type: number
          format: float
          example: 222222.22
        lossFrequency:
          $ref: "#/components/schemas/LossFrequency"
      required:
        - averageClaimSize
        - averageBurnRate
        - lossFrequency
    LossFrequency:
      type: object
      properties:
        perMillionMiles:
          type: number
          format: float
        perUnit:
          type: number
          format: float
      required:
        - perMillionMiles
        - perUnit
    ApplicationReviewLossSummaryV2Form:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              $ref: "#/components/schemas/ApplicationReviewLossSummaryV2FormData"
    ApplicationReviewLossSummaryV2FormData:
      type: object
      properties:
        coverageType:
          $ref: "../common/spec.yaml#/components/schemas/CoverageType"
        summary:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewLossSummaryV2FormItem"
      required:
        - coverageType
        - summary
    ApplicationReviewLossSummaryV2FormItem:
      type: object
      properties:
        periodStartDate:
          type: string
          format: date
          example: 2016-11-25
        periodEndDate:
          type: string
          format: date
          example: 2017-11-25
        numberOfPowerUnitsOverride:
          type: number
          example: 29
        grossLossOverride:
          type: number
          example: 23003
        numberOfClaimsOverride:
          type: number
          example: 39
      required:
        - periodStartDate
        - periodEndDate
    ApplicationReviewLargeLosses:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            parsedLossRunStatusInfo:
              $ref: "#/components/schemas/ApplicationReviewLossSummaryParsedLossRunStatusInfo"
            value:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewLargeLossesItem"
          required:
            - parsedLossRunStatusInfo
            - value
    ApplicationReviewLargeLossesForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            data:
              type: object
              properties:
                value:
                  type: array
                  items:
                    $ref: "#/components/schemas/ApplicationReviewLargeLossesItem"
              required:
                - value
    ApplicationReviewLargeLossesItem:
      type: object
      properties:
        date:
          type: string
          format: date
          example: 2016-11-25
        coverageType:
          type: string
          enum:
            - Auto Liability
            - Auto Physical Damage
        lossIncurred:
          type: integer
          format: int32
          example: 2435
        parsedLossIncurred:
          type: integer
          format: int32
          example: 2435
        description:
          type: string
          example: Texas
      required:
        - date
        - coverageType
        - lossIncurred
    ApplicationReviewLossAverages:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            autoLiability:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewLossAveragesItem"
            autoPhysicalDamage:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewLossAveragesItem"
            motorTruckCargo:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewLossAveragesItem"
            data:
              type: array
              items:
                $ref: "#/components/schemas/ApplicationReviewLossAveragesPerType"
          required:
            - autoLiability
            - autoPhysicalDamage
            - motorTruckCargo
            - data
    ApplicationReviewLossAveragesItem:
      type: object
      properties:
        violation:
          type: string
        value:
          type: string
      required:
        - violation
        - value
    ApplicationReviewLossAveragesPerType:
      type: object
      properties:
        averageType:
          type: string
        autoLiability:
          type: number
          format: float
          example: 0.5
        autoPhysicalDamage:
          type: number
          format: float
          example: 0.5
        motorTruckCargo:
          type: number
          format: float
          example: 0.5
      required:
        - averageType
    ApplicationReviewLossAveragesForm:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
    ApplicationReviewPackageType:
      type: object
      properties:
        packageType:
          $ref: "#/components/schemas/ApplicationReviewPackageTypeValue"
      required:
        - packageType
    ApplicationReviewPackageTypeValue:
      type: string
      enum:
        - Basic
        - Standard
        - Complete
    ApplicationReviewNegotiatedRates:
      type: object
      properties:
        isNegotiatedRatesApplicable:
          type: boolean
          description: Tells us if negotiated rates can be applied or not
        isNegotiatedRatesApplied:
          type: boolean
          description: Tells us if UW applied negotiated rates or not
        baseLimitPremium:
          type: integer
          format: int64
          description: Tells us if what is the base limit premium
        thresholdPremium:
          type: integer
          format: int64
          description: Tells us if what is the threshold premium. If base premium > threshold premium, NR is applicable
        rules:
          type: array
          items:
            $ref: '#/components/schemas/ApplicationReviewNegotiatedRatesRuleDetails'
        coverages:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        details:
          $ref: '#/components/schemas/ApplicationReviewNegotiatedRatesDetails'
      required:
        - isNegotiatedRatesApplicable
        - isNegotiatedRatesApplied
        - rules
        - coverages
        - details
    ApplicationReviewNegotiatedRatesRuleDetails:
      type: object
      properties:
        ruleType:
          $ref: '../common/spec.yaml#/components/schemas/NegotiatedRateRulesEnum'
        isApplicable:
          type: boolean
        ruleLabel:
          type: string
      required:
        - ruleType
        - isApplicable
        - ruleLabel
    ApplicationReviewNegotiatedRatesDetails:
      type: object
      properties:
        alNegotiatedRate:
          type: integer
          format: int64
        alTraditionalRate:
          type: integer
          format: int64
        apdNegotiatedRate:
          type: integer
          format: int64
        apdTraditionalRate:
          type: integer
          format: int64
        exemption:
          $ref: '../common/spec.yaml#/components/schemas/NegotiatedRateRulesEnum'
        caseDescription:
          type: string
      required:
        - alNegotiatedRate
        - exemption
        - caseDescription
    ApplicationReviewCoverages:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            autoLiability:
              $ref: "#/components/schemas/ApplicationReviewCoverageInfo"
            autoPhysicalDamage:
              $ref: "#/components/schemas/ApplicationReviewCoverageInfo"
            generalLiability:
              $ref: "#/components/schemas/ApplicationReviewCoverageInfo"
            motorTruckCargo:
              $ref: "#/components/schemas/ApplicationReviewCoverageInfo"
            combinedCoverages:
              type: array
              items:
                $ref: '../common/spec.yaml#/components/schemas/CombinedCoverages'
    ApplicationReviewCoverageInfo:
      type: object
      properties:
        state:
          type: string
          enum:
            - Approved
            - Declined
        collateral:
          type: integer
          format: int
          example: 0, 1000, 2500, 10000
        deductible:
          $ref: '../common/spec.yaml#/components/schemas/CoverageVariablesOptionNumeric'
        limit:
          $ref: '../common/spec.yaml#/components/schemas/CoverageVariablesOptionNumeric'
      required:
        - state
    ApplicationReviewQuote:
      type: object
      properties:
        totalPremium:
          type: integer
          format: int32
        subtotalPremium:
          type: integer
          format: int32
        preDiscountTotalPremium:
          type: integer
          format: int32
        autoPhysicalDamagePremium:
          $ref: "#/components/schemas/ApplicationReviewQuoteCoverageType"
        autoLiabilityPremium:
          $ref: "#/components/schemas/ApplicationReviewQuoteCoverageType"
        generalLiabilityPremium:
          $ref: "#/components/schemas/ApplicationReviewQuoteCoverageType"
        motorTruckCargoPremium:
          $ref: "#/components/schemas/ApplicationReviewQuoteCoverageType"
        premiumRange:
          $ref: "#/components/schemas/PremiumRange"
        combinedDeductibleCoverages:
          type: array
          items:
            type: string
            enum:
              - Auto Liability
              - Auto Physical Damage
              - General Liability
              - Motor Truck Cargo
        status:
          type: string
          enum:
            - running
            - success
            - failure
            - unknown
        safetyDiscount:
          type: integer
          format: int32
        flatCharges:
          type: integer
          format: int32
        totalSurchargePremium:
          type: integer
          format: int32
        packageType:
          $ref: "#/components/schemas/ApplicationReviewPackageTypeValue"
        safetyDiscountPercentage:
          type: integer
          format: int32
        errorMessage:
          type: string
      required:
        - totalPremium
        - status
        - packageType
    ApplicationReviewQuoteCoverageType:
      type: object
      properties:
        premium:
          type: integer
          format: int32
        premiumPerUnit:
          type: integer
          format: int32
        tivPercentage:
          type: number
          format: float
        deductible:
          type: integer
          format: int32
        limit:
          type: integer
          format: int32
        traditionalPremium:
          type: integer
          format: int32
        negotiatedPremium:
          type: integer
          format: int32
        premiumPerHundredMiles:
          type: number
          format: float
        totalTargetPremium:
          type: number
          format: float
        premiumRange:
          $ref: "#/components/schemas/PremiumRange"
        tivRange:
          $ref: "#/components/schemas/TivRange"
      required:
        - premium
    TivRange:
      type: object
      required:
        - min
        - max
      properties:
        min:
          type: number
          format: float
        max:
          type: number
          format: float
    PremiumRange:
      type: object
      required:
        - min
        - max
      properties:
        min:
          type: integer
          format: int32
        max:
          type: integer
          format: int32
    ApplicationVinProblems:
      type: object
      properties:
        problems:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationVinProblemRecord"
      required:
        - problems
    ApplicationVinProblemRecord:
      type: object
      properties:
        vin:
          type: string
        isResolved:
          type: boolean
        isReviewed:
          type: boolean
        shouldSkip:
          type: boolean
        fixedVin:
          type: string
        fixedMake:
          type: string
        fixedModel:
          type: string
        fixedModelYear:
          type: string
        fixedWeightClass:
          type: string
        fixedBodyClass:
          $ref: "#/components/schemas/VehicleBodyClass"
        fixedVehicleType:
          $ref: "#/components/schemas/VehicleType"
        isoVehicleTypeOverride:
          $ref: "#/components/schemas/ISOVehicleType"
        isoWeightGroupOverride:
          $ref: "#/components/schemas/ISOVehicleWeightGroup"
        errors:
          type: array
          items:
            type: string
        nhtsaDecodeError:
          type: string
        systemReviewed:
          type: boolean
      required:
        - vin
        - isResolved
        - isReviewed
        - errors

    VehicleBodyClass:
      type: string
      enum:
        - "Truck-Tractor"
        - "TRAILER"
    VehicleType:
      type: string
      enum:
        - VehicleTypeIncompleteVehicle
        - VehicleTypeMotorcycle
        - VehicleTypeTrailer
        - VehicleTypeTruck
        - VehicleTypeNil
    ISOVehicleType:
      type: string
      enum:
        - semi_trailer
        - tractor
        - truck
    ISOVehicleWeightGroup:
      type: string
      enum:
        - medium
        - heavy
        - xheavy
        - semi_trailer
        - light
    ApplicationMvrProblems:
      type: object
      properties:
        problems:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationMvrProblemRecord"
      required:
        - problems
    ApplicationMvrProblemRecord:
      type: object
      properties:
        dlNumber:
          type: string
        usState:
          type: string
          example: TX
        dateOfBirth:
          type: string
          format: date
          example: 2021-06-01
        name:
          type: string
        isResolved:
          type: boolean
        isReviewed:
          type: boolean
        shouldSkip:
          type: boolean
        fixedMovingViolationCount:
          type: integer
        error:
          type: string
      required:
        - dlNumber
        - usState
        - dateOfBirth
        - name
        - isResolved
        - isReviewed
        - error
    RollbackMetadata:
      type: object
      properties:
        comment:
          type: string
          example: ISS score is changed
      required:
        - comment
    Problems:
      type: object
      properties:
        id:
          type: string
        tag:
          type: string
      required:
        - id
        - tag
    ApplicationReviewBoardsInfo:
      type: object
      properties:
        version:
          type: string
        commitTimestamp:
          type: string
          format: date-time
    VehicleZone:
      type: object
      properties:
        id:
          type: integer
          format: int32
        description:
          type: string
      required:
        - id
    VehicleZoneRecord:
      type: object
      properties:
        startZone:
          $ref: "#/components/schemas/VehicleZone"
        endZone:
          $ref: "#/components/schemas/VehicleZone"
        percentageOfVehicles:
          type: integer
          format: int32
      required:
        - startZone
        - endZone
        - percentageOfVehicles
    TSPConnectionInfo:
      type: object
      required:
        - TelematicsDataStatus
        - CompanyInfo
      properties:
        TelematicsDataStatus:
          $ref: "../common/spec.yaml#/components/schemas/TelematicsDataStatus"
        TelematicsReminderEmailInfo:
          $ref: "#/components/schemas/TelematicsReminderEmailInfo"
        CompanyInfo:
          $ref: "#/components/schemas/CompanyInfo"
        TelematicsPipelineStatus:
          $ref: "#/components/schemas/TelematicsPipelineStatus"
        TSPConnectionHandleId:
          type: string
          example: "*********"
    TelematicsReminderEmailInfo:
      type: object
      properties:
        preference:
          $ref: "#/components/schemas/EmailPreferenceInfo"
        status:
          $ref: "#/components/schemas/EmailStatusInfo"
        nextReminder:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
        emailsSent:
          type: integer
          format: int32
          example: 3
        lastReminder:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
    EmailPreferenceInfo:
      type: object
      properties:
        preference:
          $ref: "#/components/schemas/EmailPreference"
        updatedAt:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
    EmailStatusInfo:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/EmailStatus"
        updatedAt:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
    EmailPreference:
      type: string
      enum:
        [
          Active,
          Stopped,
        ]
    EmailStatus:
      type: string
      enum:
        [
          Unscheduled,
          Completed,
          CancelledAutomatically,
          CancelledByUser,
          Active
        ]
    CompanyInfo:
      type: object
      required:
        - producerName
        - producerEmail
        - companyName
      properties:
        producerName:
          type: string
        producerEmail:
          type: string
        companyName:
          type: string
        phoneNumber:
          type: string
    TelematicsPipelineStatus:
      type: object
      required:
        - tsp
        - details
      properties:
        tsp:
          type: string
        processingDate:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
    ApplicationReviewTab:
      type: string
      enum:
        [
          ApplicationReviewTabAll,
          ApplicationReviewTabIncomplete,
          ApplicationReviewTabReadyForReview,
          ApplicationReviewTabApproved,
          ApplicationReviewTabReadyToBind,
          ApplicationReviewTabDeclined,
          ApplicationReviewTabStale,
          ApplicationReviewTabClosed,
          ApplicationReviewTabInternal,
          ApplicationReviewTabReferral,
          ApplicationReviewTabPending,
          ApplicationReviewTabPreTelematicsExperiment
        ]
    PaginatedListSortBy:
      type: string
      enum:
        [
          EffectiveDate,
          DaysConnected
        ]
    PaginatedListSortDirection:
      type: string
      enum:
        [
          Ascending,
          Descending
        ]
    ApplicationReviewTabCount:
      type: object
      properties:
        tab:
          $ref: "#/components/schemas/ApplicationReviewTab"
        count:
          type: integer
          format: int32
      required:
        - tab
        - count
    ApplicationReviewListResponseV2:
      type: object
      required:
        - appReviews
      properties:
        appReviews:
          type: array
          items:
            $ref: '#/components/schemas/AppReviewForList'
        cursor:
          type: string
    ApplicationReviewListResponse:
      type: object
      required:
        - appReviews
      properties:
        appReviews:
          type: array
          items:
            $ref: '#/components/schemas/ApplicationReviewSummary'
        cursor:
          type: string
    ApplicationReviewListCountResponse:
      type: object
      required:
        - needsAttentionCount
        - counts
      properties:
        needsAttentionCount:
          type: integer
          format: int32
        counts:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewTabCount"
    ApplicationReviewClaimHistory:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewWidgetBase"
        - type: object
          properties:
            claimHistoryTable:
              $ref: "#/components/schemas/ApplicationReviewClaimHistoryTable"
    ApplicationReviewClaimHistoryTable:
      type: object
      properties:
        claim:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewClaimHistoryItemRecord"
    ApplicationReviewClaimHistoryItemRecord:
      type: object
      properties:
        documentId:
          type: string
          example: a81bc81b-dead-4e5d-abff-90865d1e13b1
        policySn:
          type: integer
          format: int32
          example: 1
        claimSn:
          type: integer
          format: int32
          example: 2
        insurer:
          type: string
          example: James
        policyNo:
          type: string
          example: 12345
        claimId:
          type: string
          example: 1234
        dateOfLoss:
          type: string
          format: date-time
          example: 2021-03-06T23:51:04.290898-05:00
        vin:
          type: string
          example: 1234
        otherVehicle:
          type: string
          example: "{'vehicle': 'abcd'}"
        driverName:
          type: string
          example: Jerry
        causeOfLossSummary:
          type: string
          example: Collision
    ApplicationReviewClaimHistoryDescription:
      type: object
      properties:
        documentId:
          type: string
          example: a81bc81b-dead-4e5d-abff-90865d1e13b1
        policySn:
          type: integer
          format: int32
          example: 1
        claimSn:
          type: integer
          format: int32
          example: 2
        insurer:
          type: string
          example: James
        insured:
          type: string
          example: Tim
        agent:
          type: string
          example: Tom
        policyNo:
          type: string
          example: 12345
        effectiveDate:
          type: string
          format: date-time
          example: 2020-07-10T23:51:04.290898-05:00
        expiryDate:
          type: string
          format: date-time
          example: 2021-07-10T23:51:04.290898-05:00
        reportGenerationDate:
          type: string
          format: date-time
          example: 2021-03-10T23:51:04.290898-05:00
        cancelDate:
          type: string
          format: date-time
          example: 2021-03-08T23:51:04.290898-05:00
        numberOfLosses:
          type: integer
          format: int32
          example: 4
        otherPolicyData:
          type: string
          example: "{'someKey': 'someValue'}"
        totalPaid:
          type: number
          format: double
          example: 10000
        totalReserve:
          type: number
          format: double
          example: 10000
        totalRecovered:
          type: number
          format: double
          example: 10000
        totalIncurred:
          type: number
          format: double
          example: 10000
        claimId:
          type: string
          example: 1234
        dateOfLoss:
          type: string
          format: date-time
          example: 2021-03-06T23:51:04.290898-05:00
        dateReported:
          type: string
          format: date-time
          example: 2021-03-05T23:51:04.290898-05:00
        timeOfLoss:
          type: string
          format: date-time
          example: 2021-03-04T23:51:04.290898-05:00
        causeOfLossSummary:
          type: string
          example: Collision
        causeOfLossDescription:
          type: string
          example: Collision
        vin:
          type: string
          example: 1234
        vehicleType:
          type: string
          example: truck
        otherVehicle:
          type: string
          example: "{'vehicle': 'abcd'}"
        driverId:
          type: string
          example: 12345
        driverName:
          type: string
          example: Jerry
        driverAge:
          type: integer
          format: int32
          example: 32
        driverGender:
          type: string
          example: male
        driverDob:
          type: string
          format: date-time
          example: 2021-03-03T23:51:04.290898-05:00
        driverHiringDate:
          type: string
          format: date-time
          example: 2021-03-02T23:51:04.290898-05:00
        LossLocationStreet:
          type: string
          example: ABC Street
        LossLocationCityCounty:
          type: string
          example: Los Santos
        LossLocationState:
          type: string
          example: Texas
        LossLocationZipCode:
          type: integer
          format: int32
          example: 1234
    GetSaferResponse:
      type: object
      properties:
        dotRating:
          type: string
        effectiveDate:
          type: string
    ApplicationReviewWidgetRepull:
      type: object
      properties:
        widgetEnums:
          type: array
          items:
            $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetEnum"
    ApplicationReviewWidgetBackFillForm:
      type: object
      required:
        - widgetEnums
        - backFillAll
        - applicationReviewIDs
      properties:
        applicationReviewIDs:
          type: array
          items:
            $ref: "#/components/schemas/applicationReviewIDs"
        widgetEnums:
          type: array
          items:
            $ref: "../common/spec.yaml#/components/schemas/ApplicationReviewWidgetEnum"
        backFillAll:
          type: boolean
          example: false

    # TODO: shift to common directory
    JobRunId:
      type: object
      required:
        - job_id
        - run_id
      properties:
        job_id:
          type: string
          example: "1234"
        run_id:
          type: integer
          example: 1234

    ApplicationReviewRepullWidgetResponse:
      $ref: "#/components/schemas/JobRunId"
    ApplicationReviewWidgetBackfillResponse:
      $ref: "#/components/schemas/JobRunId"
    ParsedLossState:
      type: string
      enum: [ "Not Validated", "Match", "Mismatch", "No Agent File", "Check with Agent" ]
    ParsedLossStateV2:
      type: string
      enum: [ "Processing", "Validated" ]
    LossRunValueSource:
      type: string
      enum: [ "Agent", "Parsed" ]
    ApplicationReviewLossSummaryParsedLossRunStatusInfo:
      type: object
      properties:
        processingStartDate:
          type: string
          format: date
          example: 2016-11-25
        processingEndDate:
          type: string
          format: date
          example: 2017-06-16
        areAllDocumentsProcessed:
          type: boolean
          example: false
        documentList:
          type: array
          items:
            $ref: "#/components/schemas/LossRunFileMetadata"
      required:
        - areAllDocumentsProcessed
    LossRunFileMetadata:
      type: object
      required:
        - fileHandle
        - fileName
        - parsedStatus
      properties:
        fileHandle:
          type: string
          example: a586e7f9-f856-4e2e-94e1-e2d8edaae63f
        fileName:
          type: string
          example: loss_run_example.pdf
        parsedStatus:
          $ref: "#/components/schemas/LossRunFileParsedStatus"
        errorCode:
          $ref: "#/components/schemas/LossRunFileErrorCode"
    LossRunFileParsedStatus:
      type: string
      enum: [ "Processed", "In Progress", "Failed" ]
    LossRunFileErrorCode:
      type: string
      enum: [ "Invalid File", "Unparsable File", "No Rows Present", "Incomplete File" ]
    Recommendations:
      type: array
      items:
        $ref: "#/components/schemas/RecommendationInfo"
    RecommendationInfo:
      type: object
      properties:
        experimentId:
          type: string
          example: a81bc81b-dead-4e5d-abff-90865d1e13b1
        name:
          type: string
          example: Quote more in Ohio
        description:
          type: string
          example: Quote more in Ohio because XYZ reason
        panelTypes:
          type: array
          items:
            $ref: "#/components/schemas/RecommendationInfoPanelType"
        recommendedAction:
          $ref: "#/components/schemas/ApplicationReviewRecommendationRecommendedAction"
        conclusion:
          $ref: "#/components/schemas/ApplicationReviewRecommendationConclusion"
        conclusionReason:
          $ref: "#/components/schemas/ApplicationReviewRecommendationConclusionReason"
        state:
          type: string
          enum: [ "Active", "Stale" ]
      required:
        - experimentId
        - name
        - panelTypes
        - recommendedAction
        - state
    RecommendationInfoPanelType:
      type: string
      enum: [ "Operations", "Equipments", "Drivers", "Safety", "Financials", "Losses", "Packages", "Overview" ]
    ApplicationReviewRecommendationRecommendedAction:
      type: object
      properties:
        primaryAction:
          type: string
          enum: [ "Quote More", "Quote Less", "Bind More", "Bind Less", "Don't Quote", "Increase Margin", "Decrease Margin", "Apply Subsidy", "Delight Agent", "Inspect Financial Statements" ]
        additionalAction:
          type: integer
          format: int32
          example: -2
      required:
        - primaryAction
    ApplicationReviewRecommendationConclusion:
      type: string
      enum: [ "Accept", "Partially Accept", "Decline" ]
    ApplicationReviewRecommendationConclusionReason:
      type: string
      example: Decline, because does not match XYZ criteria
    ApplicationReviewRecommendationForm:
      type: object
      properties:
        conclusion:
          $ref: "#/components/schemas/ApplicationReviewRecommendationConclusion"
        conclusionReason:
          $ref: "#/components/schemas/ApplicationReviewRecommendationConclusionReason"
    ApplicationReviewOverviewRecommendedActionDetails:
      type: object
      properties:
        recommendedAction:
          $ref: "#/components/schemas/ApplicationReviewOverviewRecommendedAction"
        telematicsRiskScore:
          $ref: "#/components/schemas/ApplicationReviewOverviewTelematicsRiskScore"
        traditionalRiskFactors:
          $ref: "#/components/schemas/ApplicationReviewOverviewRiskFactors"
        isTspException:
          type: boolean
          example: false
        isRenewalApp:
          type: boolean
          example: false
        vinVisibility:
          $ref: "#/components/schemas/ApplicationReviewVinVisibility"
        appetiteScore:
          $ref: "#/components/schemas/AppetiteScoreEnum"
        updatedAt:
          type: string
          format: date
          example: 2021-06-01
    ApplicationReviewOverviewRecommendedAction:
      type: object
      properties:
        action:
          $ref: "#/components/schemas/RecommendedAction"
        reason:
          $ref: "#/components/schemas/RecommendedActionReason"
        refreshDate:
          type: string
          format: date
          example: 2021-06-01
        refreshReason:
          $ref: "#/components/schemas/RefreshReason"
      required:
        - action
        - refreshDate
    RefreshReason:
      type: array
      items:
        $ref: "#/components/schemas/RefreshReasonItem"
    RefreshReasonItem:
      type: string
      example: operations
    ApplicationReviewOverviewTelematicsRiskScore:
      type: object
      properties:
        actualScore:
          type: integer
          format: int32
          example: 86
        maxScore:
          type: integer
          format: int32
          example: 100
        windowStartDate:
          type: string
          format: date
          example: 2021-06-01
        windowEndDate:
          type: string
          format: date
          example: 2021-06-01
        category:
          type: string
          example: Target Market
        vinCount:
          type: integer
          format: int32
          example: 45
        scoreType:
          type: string
          example: TRS
        isShortHaul:
          type: boolean
          example: false
      required:
        - scoreType
    ApplicationReviewOverviewRiskFactors:
      type: array
      items:
        $ref: "#/components/schemas/TraditionalRiskFactors"
    TraditionalRiskFactors:
      type: object
      properties:
        appetiteFlag:
          type: string
          enum: [ "High Risk", "Moderate Risk", "Low Risk", "Unknown" ]
        riskFactors:
          type: array
          items:
            $ref: "#/components/schemas/TraditionalRiskFactor"
      required:
        - appetiteFlag
    TraditionalRiskFactor:
      type: object
      properties:
        name:
          type: string
          example: Years In Business
        description:
          type: string
          example: 1 year 3 months
        previousDescription:
          type: string
          example: 0 year 7 months
      required:
        - name
    ApplicationReviewRecommendedActionTrail:
      type: object
      properties:
        trailItems:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewRecommendedActionTrailItem"
      required:
        - trailItems
    ApplicationReviewRecommendedActionTrailItem:
      type: object
      properties:
        date:
          type: string
          format: date
          example: 2021-06-01
        recommendation:
          $ref: "#/components/schemas/TrailItemRecommendation"
        updated:
          $ref: "#/components/schemas/TrailItemUpdate"
      required:
        - date
        - recommendation
    AppetiteScoreEnum:
      type: string
      enum: [ "AppetiteScoreEnumMarginal", "AppetiteScoreEnumAcceptable",
              "AppetiteScoreEnumPreferred", "AppetiteScoreEnumHighRisk" ]
    RecommendedAction:
      type: string
      enum: [ "Strongly Quote", "Quote", "Neutral", "Decline", "Pending", "Not Applicable For Renewal", "Not Applicable For Non Fleet" ]
    RecommendedActionReason:
      type: string
      enum: [ "Recent Conditional DOT Rating" ,
              "VIN Visibility is less than 60%",
              "VIN Visibility is less than 60% after UW Action",
              "High hazard zone exposure detected",
              "50-100 unit account with a non-premier TSP",
              "100+ unit account with a non-premier TSP",
              "Safety Score is unavailable from Data Science",
              "Losses Burn Rate is greater than $20K",
              "Years in business is less than 2 years",
              "TRS Market Category is Decline",
              "Hazard Zone Distance Percentage is greater than threshold",
              "Hazard Zone Duration Percentage is greater than threshold",
              "Half-yearly Utilization is greater than threshold",
              "Quarterly Utilization is greater than threshold",
              "Driver Turnover is greater than threshold",
              "Unsupported TSP",
              "Driver count is less than threshold",
              "Vehicle count is less than threshold",
              "Hazard Zone Distance Percentage (for New Jersey) is greater than threshold" ]
    TrailItemRecommendation:
      type: object
      properties:
        isFirstRecommendation:
          type: boolean
        hasRecommendationChanged:
          type: boolean
        previousRecommendation:
          $ref: "#/components/schemas/RecommendedAction"
      required:
        - isFirstRecommendation
        - hasRecommendationChanged
    TrailItemUpdate:
      type: object
      properties:
        hasTRSUpdated:
          type: boolean
        riskFactors:
          type: array
          items:
            $ref: "#/components/schemas/TraditionalRiskFactor"
        hasVinVisibilityUpdated:
          type: boolean
      required:
        - hasTRSUpdated
    RecommendedActionNotificationInfo:
      type: object
      properties:
        isNotificationAvailable:
          type: boolean
          example: true
        notification:
          $ref: "#/components/schemas/RecommendedActionNotification"
      required:
        - isNotificationAvailable
    RecommendedActionNotification:
      type: object
      properties:
        notificationId:
          type: string
          example: a586e7f9-f856-4e2e-94e1-e2d8edaae63f
        hasTRSUpdated:
          type: boolean
          example: false
        numOfFactorsUpdated:
          type: integer
          format: int32
          example: 5
      required:
        - notificationId
        - hasTRSUpdated
        - numOfFactorsUpdated
    ApplicationReviewConfirmBindableQuoteForm:
      type: object
      properties:
        recommendedActionFeedback:
          type: string
          example: good account, strongly recommended
        depositAmount:
          $ref: "#/components/schemas/ApplicationReviewDepositAmount"
    ApplicationQuoteArtifact:
      type: object
      required:
        - artifactID
        - applicationID
        - submissionID
        - indicationOptionID
        - premium
        - s3Link
        - packageType
        - createdAt
      properties:
        artifactID:
          type: integer
          format: int64
          example: 10439f23-e471-426e-9760-b10907c47d94
        applicationID:
          type: string
          format: uuid
          example: 10439f23-e471-426e-9760-b10907c47d94
        submissionID:
          type: string
          format: uuid
          example: 10439f23-e471-426e-9760-b10907c47d94
        indicationOptionID:
          type: string
          format: uuid
          example: 10439f23-e471-426e-9760-b10907c47d94
        premium:
          type: integer
          format: int32
          example: 1299407
        s3Link:
          type: string
          format: uri
          example: https://nirvana-rateml-artifacts.s3.us-east-2.amazonaws.com/c04a1493-f2b2-4209-b89b-0420886e5f89/2024-02-26/UW_SUBMISSION-Basic-194ca426-02c4-4b5b-a208-a3884024ac2d-2024-02-26T18%3A13%3A27Z.json?response-content-disposition=inline&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEPb%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLXdlc3QtMSJHMEUCIQDCIjugTErTEwV9CU86V%2Fwj1%2BlNlzjUvFRc9QtcDcYATwIgDJnIvtipe%2B%2FXKPmgyApUHjTz7tfHpLdN1QOZ4%2Bt7A68qiAMI3%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARACGgw2Njc2NTYwMzg3MTgiDB2y%2B0G%2FEJQ%2BKAkOAyrcAgE1AvPPPE1xvGKDtglxGshAX%2F6e5%2BzOpV7hEyPFF7yB7ZoB0PHCCNWOzF52ADAtUJ2IB7e%2FbAWiuSRvdXMDTvEBUUlRf7GZQL%2FrKnA4KZhxTtiSrROzbWz6i99s2yjM93iOukTTBy7Sc1IoAbJuRIMGzNnWn2uky5FXsaGbT6RnZxe2MZYLofJhOS9vcxNTGzINc%2FvioSP7iW3nsVs0rhbhfOpLLJ8Ni9Z5RDT1sHk4qNXwHNXDAUr5WFUTnPy1%2FPyRA6RVWM%2FimA58bn6Slu8tSmZk2rnRe0VGU%2BY9S%2Fj4tTD3aLZp63eTqirAFT7mugldgw%2BIENPxq6Wz58hmIVbd0um3QajfQAfSoCgTeCsBHnerENx%2FvGxtZ3FuDwZbY%2B0NGm%2FTJRUn%2FdJDf7GwNoQ%2BqhcrS16q0AY%2Fi3wGJe7YI6Nsx57%2FGAi7UihrHvWGZu%2BOz2edASLAJPaRDjCbtPmuBjqzAs3278gYKBjQDizJxVUKMSBepfs4q0dar5REUiVNMo1J2TjZLq97txPhnXt1XB4IQWV0lwItrN4h%2BguSBpbU5r5yb0pd0ZqvwDTQ6MOcP%2Fp8%2Baf7RJdjn4y%2BEriR2DvFe0L9DjFwhN0OsB%2Fv9OvvJW9w06xgoBYmC4bedKGPMCryVh2yihZKXnZZedK88Ax3KGeWgjp02aSZJGkGbKKzwHfPpvLjrNiSqp2GBXxuu9riCs7uwJs%2BMsmuhp2ZIt7fTZp8%2BYYl5vxp4kN067aJlbPf%2Fm%2BiL%2BePvUPTGzNuulDWmUjmkPUlb9n%2BbtlauYSCHmNcvXV%2FgSAzOoYTKYiR74o3GVAw6FbEmEB9WovaFjQ8hYseH3ehpKfLWsdDRl3iHTUtVjybh0v8bnAsREPscA6TXXQ%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240227T232838Z&X-Amz-SignedHeaders=host&X-Amz-Expires=600&X-Amz-Credential=ASIAZW43GVE7CH7BM2H7%2F20240227%2Fus-east-2%2Fs3%2Faws4_request&X-Amz-Signature=0b6a0a18f7613c67746d546268927665cd91158a9bce8c9b220442fe32c7807b
        packageType:
          type: string
          enum:
            [
              BasicPackage,
              StandardPackage,
              CompletePackage,
              CustomPackage,
            ]
        createdAt:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
    ApplicationReviewActionDisableReasonCategory:
      type: string
      enum: [ Authority, Recommendations, UnreviewedProblems, CameraDetails, CreditLimitBreach, ClosedApplication,
              Miscellaneous, VinVisibility, MstReferral ]
    ApplicationReviewActionDisableReasonDataType:
      type: string
      enum: [ ApplicationReviewActionDisableReasonDataTypeMarkdown ]
    ApplicationReviewActionDisableReasonMarkdownData:
      type: object
      required:
        - markdown
      properties:
        markdown:
          type: string
          example: "##Camera information pending\nUpdate vehicle camera count and subsidy amount before approval."
    ApplicationReviewActionDisableReasonData:
      type: object
      properties:
        markdownData:
          $ref: "#/components/schemas/ApplicationReviewActionDisableReasonMarkdownData"
    ApplicationReviewActionDisableReason:
      type: object
      required:
        - category
        - dataType
        - data
      properties:
        category:
          $ref: "#/components/schemas/ApplicationReviewActionDisableReasonCategory"
        dataType:
          $ref: "#/components/schemas/ApplicationReviewActionDisableReasonDataType"
        data:
          $ref: "#/components/schemas/ApplicationReviewActionDisableReasonData"
    ApplicationReviewAction:
      type: object
      required:
        - actionType
        - isEnabled
      properties:
        actionType:
          $ref: "#/components/schemas/ApplicationUserActionType"
        isEnabled:
          type: boolean
          example: false
        disableReasons:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewActionDisableReason"
    ApplicationReviewGetActionsResponse:
      type: object
      required:
        - visibleActions
        - assignedUwActions
      properties:
        visibleActions:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewAction"
        assignedUwActions:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewAction"

    RecalculateTelematicsConnectionStateResponse:
      type: object
      required:
        - applicationReviewId
        - currentReviewReadinessState
        - recalculatedReviewReadinessState
        - currentTelematicsConnectionState
        - recalculatedTelematicsConnectionState
      properties:
        applicationReviewId:
          type: string
        currentReviewReadinessState:
          type: string
          description: Current review readiness state stored in DB
        recalculatedReviewReadinessState:
          type: string
          description: Recalculated review readiness state
        currentTelematicsConnectionState:
          type: string
          description: Current telematics connection state stored in DB
        recalculatedTelematicsConnectionState:
          type: string
          description: Recalculated telematics connection state

    ClearedApplicationResponse:
      type: object
      required:
        - applicationID
        - companyName
        - applicationShortID
        - producerName
        - agencyName
      properties:
        applicationID:
          type: string
        companyName:
          type: string
          example: "Company Name"
        applicationShortID:
          type: string
          minLength: 7
          maxLength: 7
        producerName:
          type: string
          example: "Producer Name"
        agencyName:
          type: string
          example: "Agency Name"
    ApplicationReviewSafetyScoreSelectedForRating:
      type: object
      required: [ windowStart, windowEnd, windowType, timestamp, scoreType, scoreVersion ]
      properties:
        score:
          type: number
          format: float
        windowStart:
          type: string
          format: date-time
        windowEnd:
          type: string
          format: date-time
        windowType:
          $ref: '../common/spec.yaml#/components/schemas/WindowType'
        vinCount:
          type: number
          format: float
        timestamp:
          type: string
          format: date-time
        scoreType:
          $ref: '../common/spec.yaml#/components/schemas/ScoreType'
        scoreVersion:
          $ref: '../common/spec.yaml#/components/schemas/Version'

    ApplicationReviewVinVisibility:
      type: object
      required: [ visibleAndOverlappingVinCountPercentage, visibleAndOverlappingVinCount, equipmentListVinCount ,
                  visibleVinsNotInAgentSubmittedVinCount, visibleVinsNotInAgentSubmittedList,nonVisibleAgentSubmittedVinListCount,
                  nonVisibleAgentSubmittedVinList ]
      properties:
        visibleAndOverlappingVinCountPercentage:
          type: number
          format: float
        visibleAndOverlappingVinCount:
          type: integer
          format: int32
        equipmentListVinCount:
          type: integer
          format: int32
        visibleVinsNotInAgentSubmittedVinCount:
          type: integer
          format: int32
        visibleVinsNotInAgentSubmittedList:
          type: array
          items:
            type: string
          example: [ "1HGBH41JXMN109186", "1HGBH41JXMN109187" ]
        nonVisibleAgentSubmittedVinListCount:
          type: integer
          format: int32
        nonVisibleAgentSubmittedVinList:
          type: array
          items:
            type: string
          example: [ "1HGBH41JXMN109186", "1HGBH41JXMN109187" ]

    ApplicationReviewVinVisibilityChecklistPut:
      type: object
      required: [ tasks ]
      properties:
        tasks:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewVinVisibilityCheckListItem"
    ApplicationReviewVinVisibilityCheckListResponse:
      allOf:
        - $ref: "#/components/schemas/ApplicationReviewVinVisibilityCheckList"
        - type: object
          properties:
            previousReviewChecklist:
              $ref: "#/components/schemas/ApplicationReviewVinVisibilityCheckList"

    ApplicationReviewVinVisibilityCheckList:
      type: object
      required: [ status,tasks ]
      properties:
        status:
          $ref: "#/components/schemas/ApplicationReviewVinVisibilityCheckListStatus"
        tasks:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewVinVisibilityCheckListItem"

    ApplicationReviewVinVisibilityCheckListStatus:
      type: object
      required: [ totalTasks, completedTasks ]
      properties:
        totalTasks:
          type: integer
          format: int32
        completedTasks:
          type: integer
          format: int32

    ApplicationReviewVinVisibilityCheckListItem:
      type: object
      required: [ id,description,value,isCompleted ]
      properties:
        id:
          type: integer
          format: int32
        description:
          type: string
          example: "I have checked for mistyped VINs"
        value:
          $ref: "#/components/schemas/OptionalValue"
        isCompleted:
          type: boolean
          example: true

    OptionalValue:
      type: object
      required:
        - valueType
        - value
      properties:
        value:
          type: object
          properties:
            boolValue:
              type: boolean
            stringValue:
              type: string
            integerValue:
              type: integer
        valueType:
          type: string
          enum: [ "bool", "string", "integer" ]

    ApplicationReviewTargetPriceRange:
      type: object
      properties:
        min:
          type: integer
          format: int32
          example: 100000
        max:
          type: integer
          format: int32
          example: 100000
      required:
        - min
        - max
    ApplicationReviewTargetPrice:
      type: object
      properties:
        range:
          $ref: "#/components/schemas/ApplicationReviewTargetPriceRange"
        ratePerHundredMiles:
          type: number
          format: float
          example: 10.5
        percentOfTIV: # applicable only for APD
          type: number
          format: float
          example: 1.75
    ApplicationReviewTargetPriceOverride:
      type: object
      properties:
        isTargetPriceAvailable:
          type: boolean
          example: true
        totalTargetPrice:
          $ref: "#/components/schemas/ApplicationReviewTargetPrice"
        alTargetPrice:
          $ref: "#/components/schemas/ApplicationReviewTargetPrice"
        apdTargetPrice:
          $ref: "#/components/schemas/ApplicationReviewTargetPrice"
        mtcTargetPrice:
          $ref: "#/components/schemas/ApplicationReviewTargetPrice"
    ApplicationReviewDataCompletionTab:
      type: string
      enum:
        [
          ApplicationReviewDataCompletionTabOpen,
          ApplicationReviewDataCompletionTabReadyForReview,
          ApplicationReviewDataCompletionTabReviewComplete,
          ApplicationReviewDataCompletionTabInternal
        ]
    GroupOfApplicationReviewsForDataCompletion:
      type: array
      items:
        $ref: '#/components/schemas/ApplicationReviewForDataCompletion'
    ApplicationReviewListForDataCompletionResponse:
      type: object
      required:
        - orderedAppReviewGroups
      properties:
        orderedAppReviewGroups:
          type: array
          items:
            $ref: '#/components/schemas/GroupOfApplicationReviewsForDataCompletion'
        cursor:
          type: string

    ApplicationReviewForDataCompletion:
      type: object
      required:
        - appReviewID
        - companyName
        - producer
        - agency
        - effectiveDate
        - totalReviewReadinessTasks
        - completedReviewReadinessTasks
        - assignee
        - dotNumber
        - state
      properties:
        appReviewID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        companyName:
          type: string
          example: All Trucking Inc
        producer:
          type: string
          example: Andrew Adams
        agency:
          type: string
          example: Cottingham & Butler
        tspName:
          type: string
          example: Samsara
        telematicsStatus:
          $ref: "#/components/schemas/TelematicsStatus"
        telematicsConnectedDays:
          type: integer
          format: int32
          example: 5
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        totalReviewReadinessTasks:
          type: integer
          format: int32
          example: 5
        completedReviewReadinessTasks:
          type: integer
          format: int32
          example: 3
        clearanceStatus:
          $ref: "../common/spec.yaml#/components/schemas/ApplicationClearanceStatus"
        assignee:
          $ref: "#/components/schemas/ApplicationReviewUser"
        dotNumber:
          type: integer
          format: int64
          example: 1002125
        state:
          $ref: "#/components/schemas/ApplicationReviewState"

    TelematicsStatus:
      type: string
      enum: [ Connected, NotConnected, Errored ]

    ReviewReadinessTaskName:
      type: string
      enum: [ "TaskSuccessfulTelematicsConnection", "TaskEquipment", "TaskVINVisibility" ]
    ReviewReadinessTaskStatus:
      type: string
      enum: [ "TaskStatusPending", "TaskStatusDone", "TaskStatusSkipped" ]
    ReviewReadinessTaskCompletionMethod:
      type: string
      enum: [ "TaskCompletionMethodManual", "TaskCompletionMethodAutomatic" ]
    ReviewReadinessTask:
      type: object
      required:
        - name
        - status
        - assignee
        - completionMethod
        - isEnabled
      properties:
        name:
          $ref: "#/components/schemas/ReviewReadinessTaskName"
        status:
          $ref: "#/components/schemas/ReviewReadinessTaskStatus"
        assignee:
          $ref: "#/components/schemas/ApplicationReviewUser"
        notes:
          type: string
          example: "3 drivers have violations"
        completionMethod:
          $ref: "#/components/schemas/ReviewReadinessTaskCompletionMethod"
        isEnabled:
          type: boolean
          example: true
        disableReason:
          type: string
          description: Reason if task is disabled, reason should always be present if isEnabled is false
          example: "Task not available until telematics is successfully connected"
    ReviewReadinessTaskListResponse:
      type: object
      required:
        - tasks
        - totalTasks
        - completedTasks
        - appReviewState
      properties:
        totalTasks:
          type: integer
          format: int32
          example: 5
        completedTasks:
          type: integer
          format: int32
          example: 3
        appReviewState:
          $ref: "#/components/schemas/ApplicationReviewState"
        tasks:
          type: array
          items:
            $ref: "#/components/schemas/ReviewReadinessTask"
    UpdateReviewReadinessTaskRequest:
      type: object
      required:
        - name
      properties:
        name:
          $ref: "#/components/schemas/ReviewReadinessTaskName"
        status:
          $ref: "#/components/schemas/ReviewReadinessTaskStatus"
        assigneeID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        notes:
          type: string
          example: "3 drivers have violations"
    BulkUpdateReviewReadinessTaskRequest:
      type: object
      properties:
        tasks:
          type: array
          items:
            $ref: "#/components/schemas/UpdateReviewReadinessTaskRequest"

    GetApplicationReviewMstReferral:
      type: object
      required:
        - isReferralRequired
        - referralRules
        - isReviewed
      properties:
        isReferralRequired:
          type: boolean
          example: true
        referralRules:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewMstReferralRule"
        updatedAt:
          type: string
          format: date-time
          example: 2021-06-01
        isReferred:
          type: boolean
          example: true
        isReviewed:
          type: boolean
          example: true

    PatchApplicationReviewMstReferralRule:
      type: object
      properties:
        isReferred:
          type: boolean
          example: true
        rule:
          $ref: "#/components/schemas/ApplicationReviewMstReferralRule"

    ApplicationReviewMstReferralRule:
      type: object
      required:
        - id
        - description
        - ruleType
      properties:
        id:
          type: integer
          example: 1
        description:
          type: string
          example: "Referral rule description"
        isReferralRequired:
          type: boolean
          example: true
        isFurtherReviewRequired:
          type: boolean
          example: true
        isReviewed:
          type: boolean
          example: true
        ruleType:
          $ref: "#/components/schemas/MstReferralRuleType"
        widgetEnum:
          type: string
          enum: [ "LossSummary","CrashRecords" ]

    MstReferralRuleType:
      type: string
      enum: [ "Automatic", "Manual" ]

    ApplicationReviewMstReferralReview:
      type: object
      required:
        - referralRules
        - referralStatus
        - shouldCollapseWidget
      properties:
        referralRules:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewMstReferralReviewRule"
        referralStatus:
          $ref: "#/components/schemas/ApplicationReviewMstReferralReviewStatus"
        summary:
          type: string
          example: "Referral summary"
        summaryPoints:
          type: string
          example: "Referral summary points"
        referralPacketUrl:
          type: string
          example: "https://support.nirvanatech.com/reports/generate"
        markAsSentDate:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
        shouldCollapseWidget:
          type: boolean
          example: true

    ApplicationReviewMstReferralReviewForm:
      type: object
      required:
        - data
      properties:
        data:
          $ref: "#/components/schemas/ApplicationReviewMstReferralReviewFormData"

    ApplicationReviewMstReferralReviewFormData:
      type: object
      required:
        - referralStatus
      properties:
        referralRules:
          type: array
          items:
            $ref: "#/components/schemas/ApplicationReviewMstReferralReviewRule"
        referralStatus:
          $ref: "#/components/schemas/ApplicationReviewMstReferralReviewStatus"

    ApplicationReviewMstReferralReviewRule:
      type: object
      required:
        - id
        - category
        - description
        - ruleType
        - decision
        - shouldPrefill
        - isPrimary
      properties:
        id:
          type: string
          example: "AnySymbol1Or61"
        category:
          type: string
          example: "category"
        description:
          type: string
          example: "Referral rule description"
        decision: # i.e., system decision
          $ref: "#/components/schemas/ApplicationReviewMstReferralReviewRuleDecision"
        decisionOverride:
          $ref: "#/components/schemas/ApplicationReviewMstReferralReviewRuleDecision"
        ruleType:
          $ref: "#/components/schemas/ApplicationReviewMstReferralReviewRuleType"
        shouldPrefill:
          type: boolean
          example: true
        isPrimary:
          type: boolean
          example: true

    ApplicationReviewMstReferralReviewRuleType:
      type: string
      enum: [ "Automatic", "SemiAutomatic" ]

    ApplicationReviewMstReferralReviewRuleDecision:
      type: string
      enum: [ "Active", "Inactive" ]

    ApplicationReviewMstReferralReviewStatus:
      type: string
      enum: [ "NotEligible", "NotEligibleByUW", "Pending", "DecisionSubmitted", "ReferralEmailSent" ]


    ApplicationReviewCurrentStatusRequest:
      type: object
      required:
        - status
      properties:
        status:
          $ref: "#/components/schemas/ApplicationReviewCurrentStatus"

    ApplicationReviewCurrentStatus:
        type: string
        enum: [ "NotStarted", "InReview", "AwaitingAgent", "AwaitingManagerApproval", "BlockedMST", "BORRequested" ]

    ApplicationReviewCurrentStatusForm:
      type: object
      required:
        - data
      properties:
        data:
          $ref: "#/components/schemas/ApplicationReviewCurrentStatusFormData"

    ApplicationReviewCurrentStatusFormData:
      type: object
      required:
        - status
      properties:
        status:
          $ref: "#/components/schemas/ApplicationReviewCurrentStatus"

    RiskFactors:
      type: object
      required:
        - factors
      properties:
        factors:
          type: array
          items:
            $ref: "#/components/schemas/RiskFactor"
    RiskFactor:
      type: object
      required:
        - id
        - name
        - description
        - category
        - pricing_type
        - version
        - state
        - created_at
        - updated_at
        - is_system_generated
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        category:
          $ref: '#/components/schemas/Category'
        pricing_type:
          $ref: '#/components/schemas/PricingType'
        version:
          type: integer
          format: int32
        state:
          $ref: '#/components/schemas/RiskFactorState'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        created_by:
          type: string
        last_updated_by:
          type: string
        is_system_generated:
          type: boolean
          description: Indicates if the risk factor is system generated or user created
          example: true
        composite_name:
          type: string
          description: Name of the composite risk factor if applicable
          example: "BASIC Scores Adjustment"
    WorksheetResponse:
      type: object
      required:
        - id
        - review_id
        - state
        - created_at
        - updated_at
        - worksheet_risk_factors
        - pricing_details
        - version
      properties:
        id:
          type: string
        review_id:
          type: string
        worksheet_risk_factors:
          type: array
          items:
            $ref: '#/components/schemas/WorksheetRiskFactor'
        pricing_details:
          type: array
          items:
            $ref: '#/components/schemas/CategoryPricingDetails'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        last_updated_by:
          type: string
        state:
          $ref: '#/components/schemas/WorksheetState'
        notes:
          type: string
        version:
          type: integer
          format: int32
    UpdateWorksheetRequest:
      type: object
      properties:
        notes:
          type: string
    WorksheetRiskFactorUpdate:
      type: object
      required:
        - risk_factor_id
        - value
      properties:
        risk_factor_id:
          type: string
        value:
          type: string
        notes:
          type: string
    WorksheetRiskFactor:
      type: object
      required:
        - id
        - risk_factor
        - created_at
        - sentiment
        - updated_at
        - created_by
        - last_updated_by
      properties:
        id:
          type: string
        risk_factor:
          $ref: '#/components/schemas/RiskFactor'
        value:
          type: string
        notes:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        created_by:
          type: string
        last_updated_by:
          type: string
        sentiment:
          $ref: '#/components/schemas/Sentiment'
        version:
          type: integer
          format: int32
        pricing_details:
          type: array
          items:
            $ref: '#/components/schemas/CoveragePricingDetail'
    CategoryPricingDetails:
      type: object
      required:
        - category
        - coverage_pricing_detail
        - isEditable
      properties:
        category:
          $ref: '#/components/schemas/Category'
        coverage_pricing_detail:
          type: array
          items:
            $ref: '#/components/schemas/CoveragePricingDetail'
        isEditable:
          type: boolean
    CoveragePricingDetail:
      type: object
      required:
        - coverage
        - credit
      properties:
        coverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        credit:
          type: integer
          format: int32
    Category:
      type: string
      enum:
        - UNSPECIFIED_CATEGORY
        - OPERATIONS
        - EQUIPMENTS
        - DRIVERS
        - SAFETY
        - FINANCIALS
        - LOSSES
    PricingType:
      type: string
      enum:
        - UNSPECIFIED_PRICING_TYPE
        - ALREADY_PRICED
        - MANUALLY_PRICED
    RiskFactorState:
      type: string
      enum:
        - UNSPECIFIED_RISK_FACTOR_STATE
        - ACTIVE
        - INACTIVE
        - DRAFT
    WorksheetState:
      type: string
      enum:
        - UNSPECIFIED_WORKSHEET_STATE
        - APPROVED
        - STALE
        - IN_PROGRESS
    Sentiment:
      type: string
      enum:
        - UNSPECIFIED_SENTIMENT
        - POSITIVE
        - NEGATIVE
        - NEUTRAL

    WorksheetRiskFactorAddRequest:
      type: object
      required:
        - risk_factor_id
        - value
        - sentiment
      properties:
        risk_factor_id:
          type: string
        value:
          type: string
        notes:
          type: string
        sentiment:
          $ref: '#/components/schemas/Sentiment'

    WorksheetRiskFactorUpdateRequest:
        type: object
        properties:
          value:
            type: string
          sentiment:
            $ref: '#/components/schemas/Sentiment'
          notes:
            type: string

    UpdatePricingRequest:
        type: object
        required:
          - category
          - coverage_pricing_detail
        properties:
          riskFactorId:
            type: string
          category:
            $ref: '#/components/schemas/Category'
          coverage_pricing_detail:
            type: array
            items:
              $ref: '#/components/schemas/CoveragePricingDetail'

    SuggestedRiskFactor:
        type: object
        required:
          - name
          - notes
          - category
          - value
          - sentiment
        properties:
          name:
            type: string
          notes:
            type: string
          category:
            $ref: '#/components/schemas/Category'
          value:
            type: string
          comment:
            type: string
          sentiment:
            $ref: '#/components/schemas/Sentiment'

    ApplicationReviewReportForm:
      type: object
      required:
        - data
      properties:
        data:
          $ref: "#/components/schemas/ApplicationReviewReportData"

    ApplicationReviewReportData:
      type: object
      required:
        - reportType
      properties:
        reportType:
          $ref: "#/components/schemas/ApplicationReviewReportType"

    ApplicationReviewReportType:
      type: string
      enum: [ "MSTReferral" ]

    FactorRankingsResponse: 
      type: array
      items:
        $ref: '#/components/schemas/FactorRanking'
    
    FactorRanking:
      type: object
      required:
        - raterType
        - rankedFactors
      properties:
        raterType:
          type: string
        rankedFactors:
          type: array
          items:
            $ref: '#/components/schemas/FactorRankingItem'

    FactorRankingItem:
      type: object
      required:
        - factorName
        - description
        - weightedAverage
        - baseline
        - aboveBaseline
      properties:
        factorName:
          type: string
        description:
          type: string
        weightedAverage:
          type: number
          format: float
        baseline:
          $ref: '#/components/schemas/BaselineFactor'
        aboveBaseline:
          type: number
          format: float
          nullable: true
    
    BaselineFactor:
      type: object
      description: Represents the baseline value of a specific factor. It includes the sample size used in its calculation.
      required:
        - value
        - sampleSize
      properties:
        value:
          type: number
          format: float
        sampleSize:
          type: integer
          format: int32

    ApplicationReviewPanelNotes:
      type: object
      required:
        - notes
      properties:
        notes:
          $ref: '#/components/schemas/ApplicationReviewPanelNotesData'

    ApplicationReviewPanelNotesData:
      type: object
      required:
        - drivers
      properties:
        drivers:
          type: string
          description: Driver panel notes for an application review.
          example: Here goes the driver notes...
