package mvr

import (
	"context"
	"fmt"
	"strings"
	"time"

	"nirvanatech.com/nirvana/common-go/us_states"

	"github.com/cockroachdb/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/rating/data_fetching/mvr_fetching"
)

func getApplicableViolations(
	effectiveDate time.Time,
	report *data_fetching.MVRReportV1,
	programType policy_enums.ProgramType,
	state us_states.USState,
) DriverViolationRecord {
	var retval []ViolationData
	var err error

	if report == nil {
		return DriverViolationRecord{Violations: nil, ErrorVal: errors.New("No MVR report found")}
	}

	for _, violation := range report.Violations {
		var violationPointsExpiryTime, violationExpiryTime time.Time
		var nonfleetViolation NonfleetMVRViolation
		violationCountData, exist := ProgressiveMVRViolationDataMap[violation.AssignedViolationCode]
		if exist && violationCountData.ExcludedStates[state] {
			continue
		}
		nonfleetViolation, err = AdmittedGetMVRViolation(violation.AssignedViolationCode)
		if err == nil {
			violationPointsExpiryTime = effectiveDate.Add(-nonfleetViolation.GetViolationPointsDuration())
			violationExpiryTime = effectiveDate.Add(-nonfleetViolation.GetExtraValidityDuration())
		} else {
			return DriverViolationRecord{
				ProgramType: programType,
				Violations:  nil,
				ErrorVal: errors.Errorf(
					"Unknown violation code %s", violation.AssignedViolationCode),
			}
		}

		// pointsExpired tracks whether the violation points are still applicable or not.
		// Although violations aging from 3-5 years may be expired and hold zero points,
		// but may still hold underwriting value and should be returned from this function.
		// Such old violations are also used in the count of company violations.
		pointsExpired := false
		if violation.ViolationDate.AsTime().Before(violationPointsExpiryTime) {
			pointsExpired = true
		}
		if violation.ViolationDate.AsTime().Before(violationExpiryTime) {
			continue
		}
		retval = append(retval, ViolationData{
			NFViolationData:      nonfleetViolation,
			GenericViolationData: violation,
			PointsExpired:        pointsExpired,
		})
	}

	// Filter out duplicate violations, if ViolationCode and ViolationDate are the same from retval
	uniqueViolations := removeDuplicateViolations(retval)

	var di, de *time.Time
	if report.DateIssued != nil {
		di = pointer_utils.ToPointer(report.DateIssued.AsTime())
	}
	if report.DateExpires != nil {
		de = pointer_utils.ToPointer(report.DateExpires.AsTime())
	}
	return DriverViolationRecord{Violations: uniqueViolations, ErrorVal: nil, DateIssued: di, DateExpiry: de}
}

func fetchMVRReports(
	ctx context.Context,
	drivers []admitted_app.DriverDetails,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) (retval []*data_fetching.MVRReportV1, errs []error) {
	appIdPrefix := random_utils.GenerateRandomString(10)
	var requests []*data_fetching.MVRReportRequestV1
	for i, d := range drivers {
		m := data_fetching.MVRReportRequestV1{
			DlNumber:      d.LicenseNumber,
			UsState:       d.LicenseState,
			ApplicationID: fmt.Sprintf("%s:%d", appIdPrefix, i),
		}
		if d.FirstName != "" {
			m.FirstName = d.FirstName
		}
		if d.LastName != "" {
			m.LastName = d.LastName
		}
		m.Dob = timestamppb.New(d.DateOfBirth)
		requests = append(requests, &m)
	}

	// For now, we are not using the store interceptor for fetching or processing data for Non-Fleet
	// (this method is only used by NF).
	fetcher, closer, err := fetcherClientFactory()
	if err != nil {
		return nil, []error{errors.Wrap(err, "unable to create data fetcher")}
	}
	defer func() { _ = closer() }()

	resp, _, err := mvr_fetching.GetMVRReportsV1(ctx, fetcher, requests, pointer_utils.Duration(mvrTimeout))
	if err != nil {
		for range drivers {
			errs = append(errs, err)
		}
		return
	}
	for _, status := range resp.GetStatus() {
		if status.Status != "success" {
			errs = append(errs, errors.New("Failed to pull the MVR report"))
		} else {
			errs = append(errs, nil)
		}
	}

	var reports []*data_fetching.MVRReportV1
	for _, d := range drivers {
		var reportToUse *data_fetching.MVRReportV1
		for _, report := range resp.GetReports() {
			reportToUse = pointer_utils.ToPointer(data_fetching.MVRReportV1{
				DlNumber: d.LicenseNumber,
			})
			if strings.EqualFold(report.DlNumber, d.LicenseNumber) {
				reportToUse = report
				break
			}
		}
		reports = append(reports, reportToUse)
	}

	return reports, errs
}

func getPGRCompanyViolations(
	effectiveDate time.Time,
	report *data_fetching.MVRReportV1,
) (retval []AdmittedViolationData) {
	for _, violation := range report.Violations {
		var cutOffTime time.Time
		nonfleetViolation, err := AdmittedGetMVRViolation(violation.AssignedViolationCode)
		if err != nil {
			continue
		}
		admittedViolation := nonfleetViolation.(*AdmittedViolationData)
		cutOffTime = effectiveDate.Add(
			-admittedViolation.GetExtraValidityDuration())

		if violation.ViolationDate.AsTime().Before(cutOffTime) {
			continue
		}
		retval = append(retval, *admittedViolation)
	}
	return
}

func removeDuplicateViolations(violations []ViolationData) []ViolationData {
	seen := make(map[string]struct{})
	unique := make([]ViolationData, 0, len(violations))

	for _, v := range violations {
		dateStr := v.GenericViolationData.ViolationDate.AsTime().Format(time.DateOnly)
		key := v.GenericViolationData.AssignedViolationCode + "|" + dateStr

		if _, exists := seen[key]; !exists {
			seen[key] = struct{}{}
			unique = append(unique, v)
		}
	}
	return unique
}
