load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mvr",
    srcs = [
        "admitted_impl.go",
        "fleet_violation_codes.go",
        "interface.go",
        "nrb_codes.go",
        "pgr_codes.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/mvr",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/random_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/rating/data_fetching/mvr_fetching",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
