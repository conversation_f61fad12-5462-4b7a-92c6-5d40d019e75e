package common

import (
	"context"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/jinzhu/copier"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/outputs_quote"
	"nirvanatech.com/nirvana/rating/artifacts"
	"nirvanatech.com/nirvana/rating/rateml/program"
)

type entityPointer[T any] interface {
	*T
	common.Entity
}

func NewOutputCreatorFn() common.OutputCreatorFn[creator_functions.Input] {
	return func(
		_ context.Context,
		pc *common.ProgramContext,
		prog *program.Program,
		input *creator_functions.Input,
	) (any, error) {
		var err error

		e := pc.GetEntity(common.EntityTypeOutputsQuote, outputs_quote.Id)
		if e == nil {
			return nil, errors.New("failed to find outputs quote entity")
		}

		outputsQuote := e.(*entities.OutputsQuote)
		var retval ModelOutput
		if err = copier.Copy(&retval, outputsQuote); err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to unmarshal %+v quote output base bindings %+v",
				outputsQuote,
				retval,
			)
		}

		retval.VehicleEntities, err = getEntitiesFromProgramContext[entities.Vehicle](
			pc,
			common.EntityTypeVehicle,
		)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get vehicles")
		}

		retval.AdditionalInsuredsRegular, err = getEntitiesFromProgramContext[entities.SpecifiedRegularAI](
			pc,
			common.EntityTypeSpecifiedRegularAI,
		)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get specified regular AIs")
		}

		retval.AdditionalInsuredsPNC, err = getEntitiesFromProgramContext[entities.SpecifiedPNCAI](
			pc,
			common.EntityTypeSpecifiedPNCAI,
		)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get specified PNC AIs")
		}

		retval.ThirdPartiesWithWOS, err = getEntitiesFromProgramContext[entities.SpecifiedWOS](
			pc,
			common.EntityTypeSpecifiedWOS,
		)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get specified third parties with WOS")
		}

		if outputsQuote.CreditCode != "" {
			retval.CreditScore = strings.ToUpper(outputsQuote.CreditCode)
		}

		dotNumber, err := input.GetDOTNumber()
		if err != nil {
			return nil, err
		}

		retval.RateMLArtifact = artifacts.NewRateMLArtifact(
			pc.ModelKey,
			prog,
			dotNumber,
		)

		retval.Input = input

		return &retval, nil
	}
}

func getEntitiesFromProgramContext[T any, PT entityPointer[T]](
	pc *common.ProgramContext,
	entityType common.EntityType,
) ([]*T, error) {
	interfaceValues := pc.GetEntitiesByType(entityType)

	concreteEntities, err := slice_utils.MapErr(interfaceValues, func(interfaceValue common.Entity) (*T, error) {
		concreteEntity, ok := interfaceValue.(PT)
		if !ok {
			noop := new(T)
			return noop, errors.Newf(
				"could not cast entity with id %s into type %T",
				interfaceValue.RatemlId(),
				noop,
			)
		}
		return concreteEntity, nil
	})
	if err != nil {
		return nil, err
	}

	return concreteEntities, nil
}
