package common

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestGetProgramSpecificMetadata_CreditScore(t *testing.T) {
	// With empty credit score (we need a valid US DOT score and policy name,
	// otherwise the method returns an error)
	mo := ModelOutput{
		UsdotScoreGrp: "A01",
	}

	m, err := mo.GetProgramSpecificMetadata()
	metadata := m.(*ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata)
	require.NoError(t, err)
	require.Nil(t, metadata.NonFleetChunkOutputMetadata.CreditScore)

	// With invalid credit score
	mo.CreditScore = "invalid"
	m, err = mo.GetProgramSpecificMetadata()
	require.Error(t, err)
	require.Regexp(t, "unknown credit score", err.Error())
	require.Nil(t, m)

	// With valid credit score
	mo.CreditScore = "A1"
	m, err = mo.GetProgramSpecificMetadata()
	metadata = m.(*ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata)
	require.NoError(t, err)
	require.NotNil(t, metadata)
	require.Equal(
		t,
		pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_A1),
		metadata.NonFleetChunkOutputMetadata.CreditScore,
	)
}

func TestGetProgramSpecificMetadata_USDOTScore(t *testing.T) {
	// With empty US DOT score
	mo := ModelOutput{}
	m, err := mo.GetProgramSpecificMetadata()
	require.Error(t, err)
	require.Regexp(t, "unknown US DOT score", err.Error())
	require.Nil(t, m)

	// With invalid US DOT score
	mo = ModelOutput{UsdotScoreGrp: "invalid"}
	m, err = mo.GetProgramSpecificMetadata()
	require.Error(t, err)
	require.Regexp(t, "unknown US DOT score", err.Error())
	require.Nil(t, m)

	// With valid US DOT score
	// (we need a valid policy name, otherwise the method returns an error)
	mo = ModelOutput{
		UsdotScoreGrp: "a01",
	}
	m, err = mo.GetProgramSpecificMetadata()
	metadata := m.(*ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata)
	require.NoError(t, err)
	require.NotNil(t, metadata)
	require.Equal(t, model.USDOTScore_US_DOT_SCORE_A01, metadata.NonFleetChunkOutputMetadata.UsDOTScore)
}

func TestGetProgramSpecificMetadata_PUCount(t *testing.T) {
	// With empty PU count (we need a valid US DOT score and policy name,
	// otherwise the method returns an error)
	mo := ModelOutput{
		UsdotScoreGrp: "A01",
	}
	m, err := mo.GetProgramSpecificMetadata()
	metadata := m.(*ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata)
	require.NoError(t, err)
	require.NotNil(t, metadata)
	require.Equal(t, int32(0), metadata.NonFleetChunkOutputMetadata.PuCount)

	// With valid PU count
	mo.FleetPUCount = 10
	m, err = mo.GetProgramSpecificMetadata()
	metadata = m.(*ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata)
	require.NoError(t, err)
	require.NotNil(t, metadata)
	require.Equal(t, int32(10), metadata.NonFleetChunkOutputMetadata.PuCount)
}

func TestGetProgramSpecificMetadata_VehiclesMetadata(t *testing.T) {
	// With empty vehicles (we need a valid US DOT score and policy name,
	// otherwise the method returns an error)
	mo := ModelOutput{
		UsdotScoreGrp: "A01",
	}
	m, err := mo.GetProgramSpecificMetadata()
	metadata := m.(*ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata)
	require.NoError(t, err)
	require.NotNil(t, metadata)
	require.Len(t, metadata.NonFleetChunkOutputMetadata.VehiclesMetadata, 0)

	// With valid vehicles (some with non-zero stated value
	// and others with zero stated value)
	mo.VehicleEntities = []*entities.Vehicle{
		{
			Id: "vin1",
		},
		{
			Id:          "vin2",
			StatedValue: 100,
		},
		{
			Id:             "vin3",
			StatedValue:    200,
			StatedValueTIV: 201,
		},
		{
			Id:             "vin4",
			StatedValue:    300,
			StatedValueTIV: 301,
		},
	}
	m, err = mo.GetProgramSpecificMetadata()
	metadata = m.(*ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata)
	require.NoError(t, err)
	require.NotNil(t, metadata)
	expectedVehiclesMetadata := []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
		{
			Vin:         "vin1",
			StatedValue: 0,
		},
		{
			Vin:         "vin2",
			StatedValue: 0,
		},
		{
			Vin:         "vin3",
			StatedValue: 201,
		},
		{
			Vin:         "vin4",
			StatedValue: 301,
		},
	}
	require.Equal(t, expectedVehiclesMetadata, metadata.NonFleetChunkOutputMetadata.VehiclesMetadata)
}
