package common

import (
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func (m *ModelOutput) GetProgramSpecificMetadata() (ptypes.ChunkOutputProgramSpecificMetadataI, error) {
	metadata := &ptypes.NonFleet_ChunkOutputMetadata{
		PuCount: int32(m.FleetPUCount),
	}

	usDOTScore, err := m.convertUSDOTScore(m.UsdotScoreGrp)
	if err != nil {
		return nil, err
	}
	metadata.UsDOTScore = *usDOTScore

	if m.CreditScore != "" {
		cs, err := m.convertCreditScore(m.CreditScore)
		if err != nil {
			return nil, err
		}
		metadata.CreditScore = cs
	}

	metadata.VehiclesMetadata = getVehiclesMetadata(m.VehicleEntities)

	return &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
		NonFleetChunkOutputMetadata: metadata,
	}, nil
}

func (m *ModelOutput) convertCreditScore(creditScore string) (*model.CreditScore, error) {
	switch creditScore {
	case "A1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_A1), nil
	case "A2":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_A2), nil
	case "A3":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_A3), nil
	case "B0":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_B0), nil
	case "B1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_B1), nil
	case "B2":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_B2), nil
	case "B3":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_B3), nil
	case "C0":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_C0), nil
	case "C1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_C1), nil
	case "C2":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_C2), nil
	case "C3":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_C3), nil
	case "D0":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_D0), nil
	case "D1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_D1), nil
	case "D2":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_D2), nil
	case "E0":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_E0), nil
	case "E1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_E1), nil
	case "E2":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_E2), nil
	case "I1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_I1), nil
	case "NA":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_NA), nil
	case "O1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_O1), nil
	case "P1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_P1), nil
	case "Q1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_Q1), nil
	case "T1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_T1), nil
	case "T3":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_T3), nil
	case "T4":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_T4), nil
	case "T5":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_T5), nil
	case "X1":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_X1), nil
	case "X3":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_X3), nil
	case "X4":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_X4), nil
	case "X5":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_X5), nil
	case "XX":
		return pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_XX), nil
	default:
		return nil, errors.Newf("unknown credit score %s", creditScore)
	}
}

func (m *ModelOutput) convertUSDOTScore(score string) (*model.USDOTScore, error) {
	score = strings.ToUpper(score)
	switch score {
	case "A01":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A01), nil
	case "A02":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A02), nil
	case "A03":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A03), nil
	case "A04":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A04), nil
	case "A05":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A05), nil
	case "A06":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A06), nil
	case "A07":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A07), nil
	case "A08":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A08), nil
	case "A09":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A09), nil
	case "A10":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A10), nil
	case "A11":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A11), nil
	case "A12":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A12), nil
	case "A13":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_A13), nil
	case "B01":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B01), nil
	case "B02":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B02), nil
	case "B03":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B03), nil
	case "B04":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B04), nil
	case "B05":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B05), nil
	case "B06":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B06), nil
	case "B07":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B07), nil
	case "B08":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B08), nil
	case "B09":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B09), nil
	case "B10":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B10), nil
	case "B11":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B11), nil
	case "B12":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B12), nil
	case "B13":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_B13), nil
	case "Z92":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_Z92), nil
	case "Z93":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_Z93), nil
	case "Z94":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_Z94), nil
	case "Z95":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_Z95), nil
	case "Z97":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_Z97), nil
	case "Z98":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_Z98), nil
	case "Z99":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_Z99), nil
	case "X":
		return pointer_utils.ToPointer(model.USDOTScore_US_DOT_SCORE_X), nil
	default:
		return nil, errors.Newf("unknown US DOT score %s", score)
	}
}

func getVehiclesMetadata(
	vehicleEntities []*entities.Vehicle,
) []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata {
	vehiclesMetadata := make([]*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata, len(vehicleEntities))
	for i, vehicleEntity := range vehicleEntities {
		vehiclesMetadata[i] = &ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
			Vin:         vehicleEntity.Id,
			StatedValue: vehicleEntity.StatedValueTIV,
		}
	}
	return vehiclesMetadata
}
