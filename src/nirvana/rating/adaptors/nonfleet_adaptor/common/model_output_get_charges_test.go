package common

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestGetCharges_WithNoCharges(t *testing.T) {
	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkDates := &proto.Interval{
		Start: timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2025").ToTime()),
	}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	oc := &ModelOutput{Input: input}

	// All premium amounts are zero by default
	charges, err := oc.GetCharges()
	require.NoError(t, err)
	require.Empty(t, charges)
}

// We want to test the case when there are missing charges, but some present.
// We do this by adding only one non-zero premium amount to MO.
func TestGetCharges_WithSomeCharges(t *testing.T) {
	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkDates := &proto.Interval{
		Start: timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime()),
	}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	premium := 1000.0
	mo := ModelOutput{
		Input:             input,
		CollPolicyPremium: premium,
		VehicleEntities: []*entities.Vehicle{
			{
				Id:             "vin1",
				CollVehPremium: premium,
			},
		},
	}
	charges, err := mo.GetCharges()
	require.NoError(t, err)
	require.Len(t, charges, 1)

	expectedCharge := ptypes.NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("1000", chunkDates.Start).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Collision).
		WithDistribution(
			ptypes.Charge_DistributionType_Vehicle,
			[]*ptypes.Charge_DistributionItem{
				ptypes.NewChargeDistributionItem("vin1", "1"),
			},
		).
		Build()
	require.Equal(t, expectedCharge, charges[0])
}

func TestGetCharges_WithAllCharges(t *testing.T) {
	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkDates := &proto.Interval{
		Start: timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/02/2024").ToTime()),
	}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	premiums := make(map[string]float64)
	premiums["coll"] = 200.0
	premiums["comp"] = 220.0
	premiums["ti"] = 240.0
	premiums["not"] = 260.0
	premiums["towing"] = 280.0
	premiums["rental"] = 300.0
	premiums["cargo"] = 320.0
	premiums["reeferWithoutHumanError"] = 340.0
	premiums["reeferWithHumanError"] = 360.0
	premiums["bi"] = 380.0
	premiums["pd"] = 400.0
	premiums["um"] = 420.0
	premiums["uim"] = 440.0
	premiums["umuim"] = 460.0
	premiums["umpd"] = 480.0
	premiums["medPay"] = 500.0
	premiums["pip"] = 520.0
	premiums["pipWorkLossAndRPLService"] = 540.0
	premiums["pipAttendantCare"] = 560.0
	premiums["ppi"] = 580.0
	premiums["gl"] = 600.0
	premiums["ncrf"] = 620.0
	premiums["mcca"] = 700.0
	premiums["ha"] = 750.0
	premiums["blanketRegularAdditionalInsured"] = 75.0
	premiums["blanketPNCAdditionalInsured"] = 175.0
	premiums["blanketWaiverOfSubrogation"] = 75.0
	premiums["specifiedRegularAdditionalInsured"] = 20.0
	premiums["specifiedPNCAdditionalInsured"] = 75.0
	premiums["specifiedWaiverOfSubrogation"] = 25.0
	premiums["premiumRelatedSurplusTax"] = 1.0
	premiums["premiumRelatedStampingFee"] = 2.0
	premiums["feeRelatedSurplusTax"] = 3.5
	premiums["feeRelatedStampingFee"] = 4.5

	mo := ModelOutput{
		Input:                        input,
		CollPolicyPremium:            premiums["coll"],
		CompPolicyPremium:            premiums["comp"],
		TiPolicyPremium:              premiums["ti"],
		NotPolicyPremium:             premiums["not"],
		TlsPolicyPremium:             premiums["towing"],
		RentalPolicyPremium:          premiums["rental"],
		CargoPolicyPremium:           premiums["cargo"],
		ReeferBreakdownPolicyPremium: premiums["reeferWithoutHumanError"],
		ReeferBreakdownWithHumanErrorPolicyPremium: premiums["reeferWithHumanError"],
		BiPolicyPremium:                       premiums["bi"],
		PdPolicyPremium:                       premiums["pd"],
		UmPolicyPremium:                       premiums["um"],
		UimPolicyPremium:                      premiums["uim"],
		UmUimPolicyPremium:                    premiums["umuim"],
		UmpdPolicyPremium:                     premiums["umpd"],
		MedPayPolicyPremium:                   premiums["medPay"],
		PipPolicyPremium:                      premiums["pip"],
		PipWorkLossAndRplServicePolicyPremium: premiums["pipWorkLossAndRPLService"],
		PipAttendantCarePolicyPremium:         premiums["pipAttendantCare"],
		PpiPolicyPremium:                      premiums["ppi"],
		GlPolicyPremium:                       premiums["gl"],
		NCRFSurchargePremium:                  premiums["ncrf"],
		MCCASurchargePremium:                  premiums["mcca"],
		HiredAutoPolicyPremium:                premiums["ha"],
		BlanketWaiverOfSubrogationBaseCharge:  premiums["blanketWaiverOfSubrogation"],
		BlanketAdditionalInsuredBaseCharge:    premiums["blanketRegularAdditionalInsured"],
		BlanketPNCAdditionalInsuredBaseCharge: premiums["blanketPNCAdditionalInsured"],
		AdditionalInsuredsRegular: []*entities.SpecifiedRegularAI{
			{
				Id:                           "ai1",
				SpecifiedRegularAIBaseCharge: premiums["specifiedRegularAdditionalInsured"],
			},
			{
				Id:                           "ai2",
				SpecifiedRegularAIBaseCharge: premiums["specifiedRegularAdditionalInsured"],
			},
		},
		AdditionalInsuredsPNC: []*entities.SpecifiedPNCAI{
			{
				Id:                       "ai3",
				SpecifiedPNCAIBaseCharge: premiums["specifiedPNCAdditionalInsured"],
			},
			{
				Id:                       "ai4",
				SpecifiedPNCAIBaseCharge: premiums["specifiedPNCAdditionalInsured"],
			},
		},
		ThirdPartiesWithWOS: []*entities.SpecifiedWOS{
			{
				Id:                     "tp1",
				SpecifiedWOSBaseCharge: premiums["specifiedWaiverOfSubrogation"],
			},
			{
				Id:                     "tp2",
				SpecifiedWOSBaseCharge: premiums["specifiedWaiverOfSubrogation"],
			},
		},
		SurplusLinesTax:        pointer_utils.ToPointer(premiums["premiumRelatedSurplusTax"]),
		FlatSurplusLinesTax:    pointer_utils.ToPointer(premiums["feeRelatedSurplusTax"]),
		SurplusStampingFee:     pointer_utils.ToPointer(premiums["premiumRelatedStampingFee"]),
		FlatSurplusStampingFee: pointer_utils.ToPointer(premiums["feeRelatedStampingFee"]),
		VehicleEntities: []*entities.Vehicle{
			{
				Id:                                 "vin1",
				CollVehPremium:                     100.0,
				CompVehPremium:                     110.0,
				TiVehPremium:                       120.0,
				NotVehPremium:                      130.0,
				TlsVehPremium:                      140.0,
				RentalVehPremium:                   150.0,
				CargoVehPremium:                    160.0,
				ReeferVehPremium:                   170.0,
				ReeferWithHumanVehPremium:          180.0,
				BiVehPremium:                       190.0,
				PdVehPremium:                       200.0,
				UmVehPremium:                       210.0,
				UimVehPremium:                      220.0,
				UmuimVehPremium:                    230.0,
				UmpdVehPremium:                     240.0,
				MedPayVehPremium:                   250.0,
				PipVehPremium:                      260.0,
				PipWorkLossAndRplServiceVehPremium: 270.0,
				PipAttendantCareVehPremium:         280.0,
				PpiVehPremium:                      290.0,
				GlVehPremium:                       300.0,
			},
			{
				Id:                                 "vin2",
				CollVehPremium:                     100.0,
				CompVehPremium:                     110.0,
				TiVehPremium:                       120.0,
				NotVehPremium:                      130.0,
				TlsVehPremium:                      140.0,
				RentalVehPremium:                   150.0,
				CargoVehPremium:                    160.0,
				ReeferVehPremium:                   170.0,
				ReeferWithHumanVehPremium:          180.0,
				BiVehPremium:                       190.0,
				PdVehPremium:                       200.0,
				UmVehPremium:                       210.0,
				UimVehPremium:                      220.0,
				UmuimVehPremium:                    230.0,
				UmpdVehPremium:                     240.0,
				MedPayVehPremium:                   250.0,
				PipVehPremium:                      260.0,
				PipWorkLossAndRplServiceVehPremium: 270.0,
				PipAttendantCareVehPremium:         280.0,
				PpiVehPremium:                      290.0,
				GlVehPremium:                       300.0,
			},
		},
	}
	charges, err := mo.GetCharges()
	require.NoError(t, err)

	expectedCharges := getExpectedCharges(policyNumber, chunkID, chunkDates.Start, premiums)
	require.ElementsMatch(t, expectedCharges, charges)
}

func getExpectedCharges(
	policyNumber string,
	chunkID string,
	chunkStartDate *timestamppb.Timestamp,
	expectedPremiums map[string]float64,
) []*ptypes.Charge {
	return []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "coll"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Collision).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "comp"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Comprehensive).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "ti"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_TrailerInterchange).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "not"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "towing"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Towing).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "rental"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_RentalReimbursement).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "cargo"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Cargo).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "reeferWithoutHumanError"),
				chunkStartDate,
			).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "reeferWithHumanError"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "bi"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "pd"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "um"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "uim"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "umuim"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_UMUIM).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "umpd"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "medPay"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_MedicalPayments).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "pip"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "pipWorkLossAndRPLService"),
				chunkStartDate,
			).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "pipAttendantCare"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "ppi"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "gl"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "ha"), chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_HiredAuto).
			Build(),
		ptypes.NewChargeBuilder().
			WithNCRFSurchargeType().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "ncrf"), chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithMCCASurchargeType().
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "mcca"), chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithSurplusTaxSurchargeType().
			WithChargeablePolicy(policyNumber).
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "premiumRelatedSurplusTax"), chunkStartDate).
			Build(),
		ptypes.NewChargeBuilder().
			WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
			WithChargeablePolicy(policyNumber).
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "feeRelatedSurplusTax"), chunkStartDate).
			Build(),
		ptypes.NewChargeBuilder().
			WithStampingFeeSurchargeType().
			WithChargeablePolicy(policyNumber).
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "premiumRelatedStampingFee"), chunkStartDate).
			Build(),
		ptypes.NewChargeBuilder().
			WithStampingFeeFromFullyEarnedPremiumSurchargeType().
			WithChargeablePolicy(policyNumber).
			WithAmountBasedBillingDetails(premiumToString(expectedPremiums, "feeRelatedStampingFee"), chunkStartDate).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "blanketRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "blanketPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured("ai1").
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "specifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured("ai2").
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "specifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured("ai3").
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "specifiedPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured("ai4").
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "specifiedPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketWaiverOfSubrogation().
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "blanketWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation("tp1").
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "specifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation("tp2").
			WithAmountBasedBillingDetails(
				premiumToString(expectedPremiums, "specifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
	}
}

func premiumToString(expectedPremiums map[string]float64, key string) string {
	return decimal.NewFromFloat(expectedPremiums[key]).String()
}

func TestGetCharges_WithComplexDistribution(t *testing.T) {
	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkStartDate := time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime()
	chunkDates := &proto.Interval{
		Start: timestamppb.New(chunkStartDate),
	}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	// When expected total premium doesn't match the sum of the distributions
	totalCollPremium := 1000.0
	collPremium1 := 700.0
	collPremium2 := 299.99
	mo := ModelOutput{
		Input:             input,
		CollPolicyPremium: totalCollPremium,
		VehicleEntities: []*entities.Vehicle{
			{
				Id:             "vin1",
				CollVehPremium: collPremium1,
			},
			{
				Id:             "vin2",
				CollVehPremium: collPremium2,
			},
		},
	}
	charges, err := mo.GetCharges()
	require.Error(t, err)
	require.Regexp(t, "total premium does not match expected total premium", err)
	require.Nil(t, charges)

	// When rounding is required, and last item gets "the remainder"
	totalCollPremium = 100.0
	collPremium1 = 39.333333333
	collPremium2 = 50.333333334
	collPremium3 := 10.333333333
	mo = ModelOutput{
		Input:             input,
		CollPolicyPremium: totalCollPremium,
		VehicleEntities: []*entities.Vehicle{
			{
				Id:             "vin1",
				CollVehPremium: collPremium1,
			},
			{
				Id:             "vin2",
				CollVehPremium: collPremium2,
			},
			{
				Id:             "vin3",
				CollVehPremium: collPremium3,
			},
		},
	}
	charges, err = mo.GetCharges()
	require.NoError(t, err)
	require.Len(t, charges, 1)
	expectedCharge := ptypes.NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("100", timestamppb.New(chunkStartDate)).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Collision).
		WithDistribution(
			ptypes.Charge_DistributionType_Vehicle,
			[]*ptypes.Charge_DistributionItem{
				ptypes.NewChargeDistributionItem("vin3", "0.10333333"),
				ptypes.NewChargeDistributionItem("vin1", "0.39333333"),
				ptypes.NewChargeDistributionItem("vin2", "0.50333334"),
			},
		).
		Build()
	require.Equal(t, expectedCharge, charges[0])
}
