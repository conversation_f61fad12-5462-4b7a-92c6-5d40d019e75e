package common

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/rating/artifacts"
)

func Test_GetPluginsData_WithSuccess(t *testing.T) {
	artifact := &artifacts.RateMLArtifact{DOTNumber: 123}

	mo := &ModelOutput{
		RateMLArtifact: artifact,
	}

	output, err := mo.GetPluginsData()
	require.NoError(t, err)

	require.Equal(t, artifact, output.RateMLArtifactUploadV1Data.RateMLArtifact)
}
