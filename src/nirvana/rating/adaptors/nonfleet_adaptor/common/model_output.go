package common

import (
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/artifacts"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/common"
)

type ModelOutput struct {
	// Base charges premiums
	// APD related
	CollPolicyPremium   float64
	CompPolicyPremium   float64
	TiPolicyPremium     float64
	NotPolicyPremium    float64
	TlsPolicyPremium    float64
	RentalPolicyPremium float64
	// MTC related
	CargoPolicyPremium                         float64
	ReeferBreakdownPolicyPremium               float64
	ReeferBreakdownWithHumanErrorPolicyPremium float64
	// AL related
	BiPolicyPremium                       float64
	PdPolicyPremium                       float64
	UmPolicyPremium                       float64
	UimPolicyPremium                      float64
	UmUimPolicyPremium                    float64
	UmpdPolicyPremium                     float64
	MedPayPolicyPremium                   float64
	PipPolicyPremium                      float64
	PipWorkLossAndRplServicePolicyPremium float64
	PipAttendantCarePolicyPremium         float64
	PpiPolicyPremium                      float64
	HiredAutoPolicyPremium                float64
	// GL related
	GlPolicyPremium float64

	// Surcharges premiums
	// NCRF surcharge (NC)
	NCRFSurchargePremium float64
	// MCCA surcharge (MI)
	MCCASurchargePremium float64

	// Fee charges premiums.
	// We assume these fees correspond to the AL/APD policy.
	//
	// Blanket fees.
	BlanketAdditionalInsuredBaseCharge    float64
	BlanketPNCAdditionalInsuredBaseCharge float64
	BlanketWaiverOfSubrogationBaseCharge  float64
	//
	// Non-blanket (a.k.a Specified) fees.
	// Strings in this sliced correspond to the IDs of the
	// additional insureds or third parties.
	// See doc of ExtraPricingData for more details.
	AdditionalInsuredsRegular []*entities.SpecifiedRegularAI
	AdditionalInsuredsPNC     []*entities.SpecifiedPNCAI
	ThirdPartiesWithWOS       []*entities.SpecifiedWOS

	// Metadata of pricing run
	CreditScore   string
	FleetPUCount  float64
	UsdotScoreGrp string

	// Used to calculate the distribution of certain charges
	VehicleEntities []*entities.Vehicle

	// Used to upload artifacts outside of adaptor
	RateMLArtifact *artifacts.RateMLArtifact

	// APD + MTC + AL + GL
	SubTotalPolicyPremium float64

	SurplusLinesTax        *float64
	FlatSurplusLinesTax    *float64
	SurplusStampingFee     *float64
	FlatSurplusStampingFee *float64

	Input *creator_functions.Input
}

var _ common.ChunkOutputCreatorI = (*ModelOutput)(nil)
