package common

import (
	"nirvanatech.com/nirvana/rating/pricing/api/engine/common"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/rateml_artifact_upload_v1/rateml_artifact_upload_v1_data"
)

func (m *ModelOutput) GetPluginsData() (*common.PluginsData, error) {
	return &common.PluginsData{
		RateMLArtifactUploadV1Data: &rateml_artifact_upload_v1_data.Data{
			RateMLArtifact: m.RateMLArtifact,
		},
	}, nil
}
