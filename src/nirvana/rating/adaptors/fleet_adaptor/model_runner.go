package fleet_adaptor

import (
	"context"
	"reflect"

	"github.com/cockroachdb/errors"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/fleet/model"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	model_output "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/artifacts"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/rtypes"
)

type ModelRunnerRunInputs struct {
	Input           *creator_functions.Input
	ModelKey        rtypes.ModelKey
	ArtifactFileKey string
	OptionalPlugins []ptypes.PluginID
}

type modelRunnerDeps struct {
	fx.In

	FleetModelKeyToRateMLExecutorCreatorFnMap ModelKeyToRateMLExecutorCreatorFnMap

	ArtifactWriter artifacts.ArtifactWriter
	PluginsHelper  plugins.HelperI
}

func newModelRunner(deps modelRunnerDeps) *ModelRunner {
	return &ModelRunner{deps: &deps}
}

// ModelRunner should be replaced by the new API.
type ModelRunner struct {
	deps *modelRunnerDeps
}

func (m *ModelRunner) Run(
	ctx context.Context,
	inputs *ModelRunnerRunInputs,
) (*model_output.ModelOutput, error) {
	if inputs == nil {
		return nil, errors.New("inputs are nil")
	}

	mk := inputs.ModelKey
	executorCreatorFn, ok := m.deps.FleetModelKeyToRateMLExecutorCreatorFnMap[mk]
	if !ok {
		return nil, errors.Errorf("Couldn't find fleet model for model key %v", mk)
	}

	executor, err := executorCreatorFn()
	if err != nil {
		log.Error(ctx,
			"failed to fetch model executor",
			log.Any("version", mk.Version()),
			log.Any("state", mk.State()),
			log.Err(err),
		)
		return nil, errors.Wrap(err, "failed to fetch model executor")
	}

	input := inputs.Input
	if input == nil {
		return nil, errors.New("input is nil")
	}

	mi := input.ModelInput
	if mi == nil {
		return nil, errors.New("model input is nil")
	}

	// TODO: implement validations to the new Validator object once moving this logic to engine
	err = m.validateLossPeriods(mi)
	if err != nil {
		return nil, errors.Wrap(err, "error validating loss periods")
	}

	err = m.validateRadiusOfOperationRecords(input)
	if err != nil {
		return nil, errors.Wrap(err, "error validating radius of operation records")
	}

	err = m.validateOptionalPlugins(mk, inputs.OptionalPlugins)
	if err != nil {
		return nil, errors.Wrap(err, "error validating optional plugins")
	}

	// TODO: wait until consumers fix this on their end before re-enabling this validation
	// err = m.validateCombinedAPDAndMTCDeductible(mi)
	// if err != nil {
	// 	return nil, errors.Wrap(err, "error validating combined APD and MTC deductible")
	// }

	// This is a workaround for the fact that we don't have a pricing engine
	// in Fleet. For now, we'll just add the logic here.
	hasLossHistoryExperiment := slice_utils.Contains(
		inputs.OptionalPlugins,
		ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
	)

	if !hasLossHistoryExperiment {
		err = m.trimLossPeriods(mi)
		if err != nil {
			return nil, errors.Wrap(err, "error trimming loss periods")
		}
	}

	output, err := executor.Execute(
		ctx,
		input,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "error executing RateML")
	}

	mo, ok := output.(*model_output.ModelOutput)
	if !ok {
		return nil, errors.New("output is not a model_output.ModelOutput")
	}

	writeMetadata := artifacts.NewWriteMetadata(inputs.ArtifactFileKey)

	artifact := mo.GetRateMLArtifact()
	if artifact == nil {
		return nil, errors.New("artifact was nil")
	}

	err = m.deps.ArtifactWriter.WriteArtifact(ctx, artifact, writeMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "could not write artifact")
	}

	return mo, nil
}

func (m *ModelRunner) validateLossPeriods(mi *application.ModelInput) error {
	if mi == nil {
		return errors.New("model input is nil")
	}

	lossInfo := mi.LossInfo
	if lossInfo == nil {
		return errors.New("loss info is nil")
	}

	required := map[app_enums.LossRunSummaryPeriod]struct{}{ // nolint:exhaustive
		app_enums.LossRunSummaryPeriodFirstPriorYear:  {},
		app_enums.LossRunSummaryPeriodSecondPriorYear: {},
		app_enums.LossRunSummaryPeriodThirdPriorYear:  {},
	}

	valid := map[app_enums.LossRunSummaryPeriod]struct{}{ // nolint:exhaustive
		app_enums.LossRunSummaryPeriodCurrent:         {},
		app_enums.LossRunSummaryPeriodFirstPriorYear:  {},
		app_enums.LossRunSummaryPeriodSecondPriorYear: {},
		app_enums.LossRunSummaryPeriodThirdPriorYear:  {},
		app_enums.LossRunSummaryPeriodFourthPriorYear: {},
		app_enums.LossRunSummaryPeriodFifthPriorYear:  {},
	}

	for _, summary := range mi.LossInfo.LossRunSummary {
		counts := make(map[app_enums.LossRunSummaryPeriod]int)

		for _, rec := range summary.Summary {
			if _, ok := valid[rec.PeriodTag]; !ok {
				return errors.Newf("invalid period tag %s for coverage %s", rec.PeriodTag, summary.CoverageType)
			}
			counts[rec.PeriodTag]++
			if counts[rec.PeriodTag] > 1 {
				return errors.Newf("duplicate period tag %s for coverage %s", rec.PeriodTag, summary.CoverageType)
			}
		}

		for tag := range required {
			if counts[tag] == 0 {
				return errors.Newf("missing required period tag %s for coverage %s", tag, summary.CoverageType)
			}
		}
	}

	return nil
}

// trimLossPeriods removes the current and fourth prior year periods from the loss run summaries
func (m *ModelRunner) trimLossPeriods(mi *application.ModelInput) error {
	if mi == nil {
		return errors.New("model input is nil")
	}

	lossInfo := mi.LossInfo
	if lossInfo == nil {
		return errors.New("loss info is nil")
	}

	lossRunSummaries := lossInfo.LossRunSummary

	for i, lossRunSummary := range lossRunSummaries {
		records := lossRunSummary.Summary
		// Trim outer periods
		lossRunSummaries[i].Summary = slice_utils.Filter(
			records,
			func(record application.LossRunSummaryRecord) bool {
				return record.PeriodTag == app_enums.LossRunSummaryPeriodFirstPriorYear ||
					record.PeriodTag == app_enums.LossRunSummaryPeriodSecondPriorYear ||
					record.PeriodTag == app_enums.LossRunSummaryPeriodThirdPriorYear
			},
		)
	}
	return nil
}

func (m *ModelRunner) validateRadiusOfOperationRecords(input *creator_functions.Input) error {
	if input == nil {
		return errors.New("input is nil")
	}

	company, err := input.GetCompany()
	if err != nil {
		return err
	}

	seen := make(map[model.RadiusOfOperationRange]struct{})
	for _, radiusRecord := range company.RadiusOfOperationRecords {
		if radiusRecord == nil {
			return errors.New("radius of operation record is nil")
		}

		operationRange := radiusRecord.RadiusOfOperationRange
		if _, ok := seen[operationRange]; ok {
			return errors.Newf("duplicate radius of operation range %s", operationRange)
		}

		seen[operationRange] = struct{}{}
	}

	return nil
}

//nolint:unused
func (m *ModelRunner) validateCombinedAPDAndMTCDeductible(mi *application.ModelInput) error {
	if mi == nil {
		return errors.New("model input is nil")
	}

	coverageInfo := mi.CoverageInfo
	if coverageInfo == nil {
		return nil
	}

	covsWithCombinedDeductibles := coverageInfo.CoveragesWithCombinedDeductibles
	if covsWithCombinedDeductibles == nil {
		return nil
	}

	found := false
	for _, combinedCovs := range covsWithCombinedDeductibles.CombinedCoveragesList {
		if reflect.DeepEqual(map[app_enums.Coverage]bool(combinedCovs), app_logic.MTCAndAPD) {
			found = true
			break
		}
	}

	if found {
		apdCoverage := coverageInfo.GetCoverage(app_enums.CoverageAutoPhysicalDamage)
		if apdCoverage == nil {
			return errors.New("APD coverage is missing but combined deductible is set")
		}
		if apdCoverage.Deductible == nil {
			return errors.New("APD coverage deductible is missing but combined deductible is set")
		}

		mtcCoverage := coverageInfo.GetCoverage(app_enums.CoverageMotorTruckCargo)
		if mtcCoverage == nil {
			return errors.New("MTC coverage is missing but combined deductible is set")
		}
		if mtcCoverage.Deductible == nil {
			return errors.New("MTC coverage deductible is missing but combined deductible is set")
		}
	}

	return nil
}

func (m *ModelRunner) validateOptionalPlugins(modelKey rtypes.ModelKey, optionalPlugins []ptypes.PluginID) error {
	modelOptionalPlugins := m.deps.PluginsHelper.GetOptionalPlugins(modelKey)

	for _, plugin := range optionalPlugins {
		if !slice_utils.Contains(modelOptionalPlugins, plugin) {
			return errors.Newf("optional plugin %s is not supported for model %s", plugin, modelKey)
		}
	}

	return nil
}
