package common

import (
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/artifacts"
)

/*
TODO:

As part of Fleet's migration to IB and the development of the new Pricing Fleet API,
we will have to implement certain methods on the Model Output struct, particularly
one that creates charges from the output fields that are bound from RateML.

The challenge with this is that current RateML models are package-aware and policy-aware,
and we want them to be package-agnostic and policy-agnostic.

To circumvent this, we will encapsulate the logic of "which charges to create for which
package and policies" inside this Model Output struct. In the ideal world, this logic
should live outside Pricing. Pricing consumers should pass boolean flags indicating
what sub-coverages they want to price for, and with what limits and deductibles. This
information can be derived from package selection, but Pricing and RateML should not
know about this. It should only know about sub-coverages.

When we create the first set of package-agnostic and policy-agnostic models, we will
have to create a new Model Output struct that implements the method to create charges.
This method will not base it's behavior on the package being priced, nor in the policy
being priced, but rather on the sub-coverages that are passed as input and thus priced
by the RateML model. Eventually, when all active policies that were bound with "old"
models expire, we should be able to remove this Model Output struct.

Additional considerations:
- This Model Output struct receives the package and the policy being priced. That won't
be the case for the new Model Output struct.
- Ask @oganyan1 to provide the full list of what charges should be created for what
policies and packages.
- There are some charges that depend on the specific model version. One case is the
reefer charge, which in pre-CW models should only be created if the package type is
Complete. Whereas in post-CW models, it should be created for all packages, as long
as the premiums is greater than 0.
*/

type ModelOutput struct {
	// FleetPowerUnitCount holds the number of power units for the fleet.
	FleetPowerUnitCount float64
	// Tiv refers to the Total Insured Value, or in other words, the sum of the
	// stated value of all the equipment units uploaded.
	Tiv float64

	TotalSurchargePremium      float64
	ByPuSurchargePremium       *float64
	TotalSurchargePremiumMi    *float64
	TotalByCovSurchargePremium *float64

	NegotiatedRateFlag       bool
	Rule15ExemptionFlag      bool
	Rule15ExemptionThreshold float64
	Rule15ExemptionPremium   float64

	NewModelOutputBasicPackage
	NewModelOutputStandardPackage
	NewModelOutputCompletePackage

	PackageType app_enums.IndicationOptionTag

	// This field is injected in adaptors, so that it
	// can be later used to create charges that need
	// to prorate a premium amount into a $/mile rate.
	ProjectedMiles int64

	RateMLArtifact *artifacts.RateMLArtifact

	// This field is injected in adaptors, so that the
	// model output can have access to certain policy
	// fields, like the policy dates, which are used
	// to calculate the rates.
	Input *creator_functions.Input

	TotalPolicyPremiumCargoAtTerminal  float64
	PolicyPremiumMtcTrailerInterchange float64
}

type NewModelOutputBasicPackage struct {
	TotalBasicPolicyPremium                  float64
	LiabBasicPolicyPremium                   float64
	LiabBasicPolicyPremiumPpu                float64
	PhysBasicPolicyPremium                   float64
	PhysBasicPolicyPremiumPtiv               float64
	GlBasicPolicyPremium                     float64
	MtcBasicPolicyPremium                    float64
	MtcBasicPolicyPremiumPpu                 float64
	FlatBasicPolicyPremium                   float64
	NonFlatLiabBasicPolicyPremium            float64
	NonFlatLiabBasicPolicyPremiumPpu         float64
	NonFlatPhysBasicPolicyPremium            float64
	NonFlatPhysBasicPolicyPremiumPtiv        float64
	NegotiatedNonFlatLiabBasicPolicyPremium  float64
	NegotiatedNonFlatPhysBasicPolicyPremium  float64
	TraditionalNonFlatLiabBasicPolicyPremium float64
	TraditionalNonFlatPhysBasicPolicyPremium float64
	CoverageFinalModPremiumCollBasic         *float64
	CoverageFinalModPremiumCompBasic         *float64
	TotalBasicSurchargePremium               *float64
	TotalBasicSurchargePremiumKy             *float64
	TotalBasicSurpTaxandStampFee             *float64
	TotalBasicInsurancePremSurcharge         *float64
	AutoBasicInsurancePremSurcharge          *float64
	GlBasicInsurancePremSurcharge            *float64
	MtcBasicInsurancePremSurcharge           *float64
	AutoBasicLGPTSurcharge                   *float64
	GlBasicLGPTSurcharge                     *float64
	MtcBasicLGPTSurcharge                    *float64
	LiabBasicPolicySurplusLinesTax           *float64
	LiabBasicPolicyStampingFee               *float64
	PhysBasicPolicySurplusLinesTax           *float64
	PhysBasicPolicyStampingFee               *float64
	GlBasicPolicySurplusLinesTax             *float64
	GlBasicPolicyStampingFee                 *float64
	MtcBasicPolicySurplusLinesTax            *float64
	MtcBasicPolicyStampingFee                *float64
	TotalBasicPolicyPremiumUnmodified        *float64
	LiabBasicPolicyPremiumUnmodified         *float64
	PhysBasicPolicyPremiumUnmodified         *float64
	GlBasicPolicyPremiumUnmodified           *float64
	MtcBasicPolicyPremiumUnmodified          *float64
}

type NewModelOutputStandardPackage struct {
	TotalStandardPolicyPremium                  float64
	LiabStandardPolicyPremium                   float64
	LiabStandardPolicyPremiumPpu                float64
	PhysStandardPolicyPremium                   float64
	PhysStandardPolicyPremiumPtiv               float64
	GlStandardPolicyPremium                     float64
	MtcStandardPolicyPremium                    float64
	MtcStandardPolicyPremiumPpu                 float64
	FlatStandardPolicyPremium                   float64
	NonFlatLiabStandardPolicyPremium            float64
	NonFlatLiabStandardPolicyPremiumPpu         float64
	NonFlatPhysStandardPolicyPremium            float64
	NonFlatPhysStandardPolicyPremiumPtiv        float64
	NegotiatedNonFlatLiabStandardPolicyPremium  float64
	NegotiatedNonFlatPhysStandardPolicyPremium  float64
	TraditionalNonFlatLiabStandardPolicyPremium float64
	TraditionalNonFlatPhysStandardPolicyPremium float64
	CoverageFinalModPremiumCollStandard         *float64
	CoverageFinalModPremiumCompStandard         *float64
	TotalStandardSurchargePremium               *float64
	TotalStandardSurchargePremiumKy             *float64
	TotalStandardSurpTaxandStampFee             *float64
	TotalStandardInsurancePremSurcharge         *float64
	AutoStandardInsurancePremSurcharge          *float64
	GlStandardInsurancePremSurcharge            *float64
	MtcStandardInsurancePremSurcharge           *float64
	AutoStandardLGPTSurcharge                   *float64
	GlStandardLGPTSurcharge                     *float64
	MtcStandardLGPTSurcharge                    *float64
	LiabStandardPolicySurplusLinesTax           *float64
	LiabStandardPolicyStampingFee               *float64
	PhysStandardPolicySurplusLinesTax           *float64
	PhysStandardPolicyStampingFee               *float64
	GlStandardPolicySurplusLinesTax             *float64
	GlStandardPolicyStampingFee                 *float64
	MtcStandardPolicySurplusLinesTax            *float64
	MtcStandardPolicyStampingFee                *float64
	TotalStandardPolicyPremiumUnmodified        *float64
	LiabStandardPolicyPremiumUnmodified         *float64
	PhysStandardPolicyPremiumUnmodified         *float64
	GlStandardPolicyPremiumUnmodified           *float64
	MtcStandardPolicyPremiumUnmodified          *float64
}

type NewModelOutputCompletePackage struct {
	TotalCompletePolicyPremium                  float64
	LiabCompletePolicyPremium                   float64
	LiabCompletePolicyPremiumPpu                float64
	PhysCompletePolicyPremium                   float64
	PhysCompletePolicyPremiumPtiv               float64
	GlCompletePolicyPremium                     float64
	MtcCompletePolicyPremium                    float64
	MtcCompletePolicyPremiumPpu                 float64
	FlatCompletePolicyPremium                   float64
	NonFlatLiabCompletePolicyPremium            float64
	NonFlatLiabCompletePolicyPremiumPpu         float64
	NonFlatPhysCompletePolicyPremium            float64
	NonFlatPhysCompletePolicyPremiumPtiv        float64
	NegotiatedNonFlatLiabCompletePolicyPremium  float64
	NegotiatedNonFlatPhysCompletePolicyPremium  float64
	TraditionalNonFlatLiabCompletePolicyPremium float64
	TraditionalNonFlatPhysCompletePolicyPremium float64
	CoverageFinalModPremiumCollComplete         *float64
	CoverageFinalModPremiumCompComplete         *float64
	TotalCompleteSurchargePremium               *float64
	TotalCompleteSurchargePremiumKy             *float64
	TotalCompleteSurpTaxandStampFee             *float64
	TotalCompleteInsurancePremSurcharge         *float64
	AutoCompleteInsurancePremSurcharge          *float64
	GlCompleteInsurancePremSurcharge            *float64
	MtcCompleteInsurancePremSurcharge           *float64
	AutoCompleteLGPTSurcharge                   *float64
	GlCompleteLGPTSurcharge                     *float64
	MtcCompleteLGPTSurcharge                    *float64
	LiabCompletePolicySurplusLinesTax           *float64
	LiabCompletePolicyStampingFee               *float64
	PhysCompletePolicySurplusLinesTax           *float64
	PhysCompletePolicyStampingFee               *float64
	GlCompletePolicySurplusLinesTax             *float64
	GlCompletePolicyStampingFee                 *float64
	MtcCompletePolicySurplusLinesTax            *float64
	MtcCompletePolicyStampingFee                *float64
	TotalCompletePolicyPremiumUnmodified        *float64
	LiabCompletePolicyPremiumUnmodified         *float64
	PhysCompletePolicyPremiumUnmodified         *float64
	GlCompletePolicyPremiumUnmodified           *float64
	MtcCompletePolicyPremiumUnmodified          *float64
}

func (m *ModelOutput) GetFleetPowerUnitCount() float64 {
	return m.FleetPowerUnitCount
}

func (m *ModelOutput) GetTIV() float64 {
	return m.Tiv
}

func (m *ModelOutput) GetByPuSurchargePremium() *float64 {
	return m.ByPuSurchargePremium
}

func (m *ModelOutput) GetTotalSurchargePremiumMi() *float64 {
	return m.TotalSurchargePremiumMi
}

func (m *ModelOutput) GetTotalByCovSurchargePremium() *float64 {
	return m.TotalByCovSurchargePremium
}

func (m *ModelOutput) GetNegotiatedRateFlag() bool {
	return m.NegotiatedRateFlag
}

func (m *ModelOutput) GetRule15ExemptionFlag() bool {
	return m.Rule15ExemptionFlag
}

func (m *ModelOutput) GetRule15ExemptionThreshold() float64 {
	return m.Rule15ExemptionThreshold
}

func (m *ModelOutput) GetRule15ExemptionPremium() float64 {
	return m.Rule15ExemptionPremium
}

func (m *ModelOutput) GetTotalPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TotalBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.TotalStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.TotalCompletePolicyPremium
	default:
		return m.TotalStandardPolicyPremium
	}
}

func (m *ModelOutput) GetLiabPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.LiabBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.LiabStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.LiabCompletePolicyPremium
	default:
		return m.LiabStandardPolicyPremium
	}
}

func (m *ModelOutput) GetLiabPolicyPremiumPpu() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.LiabBasicPolicyPremiumPpu
	case app_enums.IndicationOptionTagStandard:
		return m.LiabStandardPolicyPremiumPpu
	case app_enums.IndicationOptionTagComplete:
		return m.LiabCompletePolicyPremiumPpu
	default:
		return m.LiabStandardPolicyPremiumPpu
	}
}

func (m *ModelOutput) GetPhysPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.PhysBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.PhysStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.PhysCompletePolicyPremium
	default:
		return m.PhysStandardPolicyPremium
	}
}

func (m *ModelOutput) GetPhysPolicyPremiumPtiv() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.PhysBasicPolicyPremiumPtiv
	case app_enums.IndicationOptionTagStandard:
		return m.PhysStandardPolicyPremiumPtiv
	case app_enums.IndicationOptionTagComplete:
		return m.PhysCompletePolicyPremiumPtiv
	default:
		return m.PhysStandardPolicyPremiumPtiv
	}
}

func (m *ModelOutput) GetGlPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.GlBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.GlStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.GlCompletePolicyPremium
	default:
		return m.GlStandardPolicyPremium
	}
}

func (m *ModelOutput) GetMtcPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.MtcBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.MtcStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.MtcCompletePolicyPremium
	default:
		return m.MtcStandardPolicyPremium
	}
}

func (m *ModelOutput) GetMtcPolicyPremiumPpu() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.MtcBasicPolicyPremiumPpu
	case app_enums.IndicationOptionTagStandard:
		return m.MtcStandardPolicyPremiumPpu
	case app_enums.IndicationOptionTagComplete:
		return m.MtcCompletePolicyPremiumPpu
	default:
		return m.MtcStandardPolicyPremiumPpu
	}
}

func (m *ModelOutput) GetFlatPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.FlatBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.FlatStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.FlatCompletePolicyPremium
	default:
		return m.FlatStandardPolicyPremium
	}
}

func (m *ModelOutput) GetNonFlatLiabPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.NonFlatLiabBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.NonFlatLiabStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.NonFlatLiabCompletePolicyPremium
	default:
		return m.NonFlatLiabStandardPolicyPremium
	}
}

func (m *ModelOutput) GetNonFlatLiabPolicyPremiumPpu() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.NonFlatLiabBasicPolicyPremiumPpu
	case app_enums.IndicationOptionTagStandard:
		return m.NonFlatLiabStandardPolicyPremiumPpu
	case app_enums.IndicationOptionTagComplete:
		return m.NonFlatLiabCompletePolicyPremiumPpu
	default:
		return m.NonFlatLiabStandardPolicyPremiumPpu
	}
}

func (m *ModelOutput) GetNonFlatPhysPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.NonFlatPhysBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.NonFlatPhysStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.NonFlatPhysCompletePolicyPremium
	default:
		return m.NonFlatPhysStandardPolicyPremium
	}
}

func (m *ModelOutput) GetNonFlatPhysPolicyPremiumPtiv() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.NonFlatPhysBasicPolicyPremiumPtiv
	case app_enums.IndicationOptionTagStandard:
		return m.NonFlatPhysStandardPolicyPremiumPtiv
	case app_enums.IndicationOptionTagComplete:
		return m.NonFlatPhysCompletePolicyPremiumPtiv
	default:
		return m.NonFlatPhysStandardPolicyPremiumPtiv
	}
}

func (m *ModelOutput) GetNegotiatedNonFlatLiabPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.NegotiatedNonFlatLiabBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.NegotiatedNonFlatLiabStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.NegotiatedNonFlatLiabCompletePolicyPremium
	default:
		return m.NegotiatedNonFlatLiabStandardPolicyPremium
	}
}

func (m *ModelOutput) GetNegotiatedNonFlatPhysPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.NegotiatedNonFlatPhysBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.NegotiatedNonFlatPhysStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.NegotiatedNonFlatPhysCompletePolicyPremium
	default:
		return m.NegotiatedNonFlatPhysStandardPolicyPremium
	}
}

func (m *ModelOutput) GetTraditionalNonFlatLiabPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TraditionalNonFlatLiabBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.TraditionalNonFlatLiabStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.TraditionalNonFlatLiabCompletePolicyPremium
	default:
		return m.TraditionalNonFlatLiabStandardPolicyPremium
	}
}

func (m *ModelOutput) GetTraditionalNonFlatPhysPolicyPremium() float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TraditionalNonFlatPhysBasicPolicyPremium
	case app_enums.IndicationOptionTagStandard:
		return m.TraditionalNonFlatPhysStandardPolicyPremium
	case app_enums.IndicationOptionTagComplete:
		return m.TraditionalNonFlatPhysCompletePolicyPremium
	default:
		return m.TraditionalNonFlatPhysStandardPolicyPremium
	}
}

func (m *ModelOutput) GetCollisionPremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.CoverageFinalModPremiumCollBasic
	case app_enums.IndicationOptionTagStandard:
		return m.CoverageFinalModPremiumCollStandard
	case app_enums.IndicationOptionTagComplete:
		return m.CoverageFinalModPremiumCollComplete
	default:
		return m.CoverageFinalModPremiumCollStandard
	}
}

func (m *ModelOutput) GetComprehensivePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.CoverageFinalModPremiumCompBasic
	case app_enums.IndicationOptionTagStandard:
		return m.CoverageFinalModPremiumCompStandard
	case app_enums.IndicationOptionTagComplete:
		return m.CoverageFinalModPremiumCompComplete
	default:
		return m.CoverageFinalModPremiumCompStandard
	}
}

func (m *ModelOutput) GetTotalSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TotalBasicSurchargePremium
	case app_enums.IndicationOptionTagStandard:
		return m.TotalStandardSurchargePremium
	case app_enums.IndicationOptionTagComplete:
		return m.TotalCompleteSurchargePremium
	default:
		return m.TotalStandardSurchargePremium
	}
}

func (m *ModelOutput) GetTotalSurpTaxandStampFee() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TotalBasicSurpTaxandStampFee
	case app_enums.IndicationOptionTagStandard:
		return m.TotalStandardSurpTaxandStampFee
	case app_enums.IndicationOptionTagComplete:
		return m.TotalCompleteSurpTaxandStampFee
	default:
		return m.TotalStandardSurpTaxandStampFee
	}
}

func (m *ModelOutput) GetTotalSurchargePremiumKy() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TotalBasicSurchargePremiumKy
	case app_enums.IndicationOptionTagStandard:
		return m.TotalStandardSurchargePremiumKy
	case app_enums.IndicationOptionTagComplete:
		return m.TotalCompleteSurchargePremiumKy
	default:
		return m.TotalStandardSurchargePremiumKy
	}
}

func (m *ModelOutput) GetTotalInsuranceSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TotalBasicInsurancePremSurcharge
	case app_enums.IndicationOptionTagStandard:
		return m.TotalStandardInsurancePremSurcharge
	case app_enums.IndicationOptionTagComplete:
		return m.TotalCompleteInsurancePremSurcharge
	default:
		return m.TotalStandardInsurancePremSurcharge
	}
}

func (m *ModelOutput) GetAutoInsuranceSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.AutoBasicInsurancePremSurcharge
	case app_enums.IndicationOptionTagStandard:
		return m.AutoStandardInsurancePremSurcharge
	case app_enums.IndicationOptionTagComplete:
		return m.AutoCompleteInsurancePremSurcharge
	default:
		return m.AutoStandardInsurancePremSurcharge
	}
}

func (m *ModelOutput) GetGLInsuranceSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.GlBasicInsurancePremSurcharge
	case app_enums.IndicationOptionTagStandard:
		return m.GlStandardInsurancePremSurcharge
	case app_enums.IndicationOptionTagComplete:
		return m.GlCompleteInsurancePremSurcharge
	default:
		return m.GlStandardInsurancePremSurcharge
	}
}

func (m *ModelOutput) GetMTCInsuranceSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.MtcBasicInsurancePremSurcharge
	case app_enums.IndicationOptionTagStandard:
		return m.MtcStandardInsurancePremSurcharge
	case app_enums.IndicationOptionTagComplete:
		return m.MtcCompleteInsurancePremSurcharge
	default:
		return m.MtcStandardInsurancePremSurcharge
	}
}

func (m *ModelOutput) GetAutoLGPTSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.AutoBasicLGPTSurcharge
	case app_enums.IndicationOptionTagStandard:
		return m.AutoStandardLGPTSurcharge
	case app_enums.IndicationOptionTagComplete:
		return m.AutoCompleteLGPTSurcharge
	default:
		return m.AutoStandardLGPTSurcharge
	}
}

func (m *ModelOutput) GetGLLGPTSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.GlBasicLGPTSurcharge
	case app_enums.IndicationOptionTagStandard:
		return m.GlStandardLGPTSurcharge
	case app_enums.IndicationOptionTagComplete:
		return m.GlCompleteLGPTSurcharge
	default:
		return m.GlStandardLGPTSurcharge
	}
}

func (m *ModelOutput) GetMTCLGPTSurchargePremium() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.MtcBasicLGPTSurcharge
	case app_enums.IndicationOptionTagStandard:
		return m.MtcStandardLGPTSurcharge
	case app_enums.IndicationOptionTagComplete:
		return m.MtcCompleteLGPTSurcharge
	default:
		return m.MtcStandardLGPTSurcharge
	}
}

func (m *ModelOutput) GetALPolicySurplusLinesTax() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.LiabBasicPolicySurplusLinesTax
	case app_enums.IndicationOptionTagStandard:
		return m.LiabStandardPolicySurplusLinesTax
	case app_enums.IndicationOptionTagComplete:
		return m.LiabCompletePolicySurplusLinesTax
	default:
		return m.LiabStandardPolicySurplusLinesTax
	}
}

func (m *ModelOutput) GetALPolicyStampingFee() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.LiabBasicPolicyStampingFee
	case app_enums.IndicationOptionTagStandard:
		return m.LiabStandardPolicyStampingFee
	case app_enums.IndicationOptionTagComplete:
		return m.LiabCompletePolicyStampingFee
	default:
		return m.LiabStandardPolicyStampingFee
	}
}

func (m *ModelOutput) GetAPDPolicySurplusLinesTax() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.PhysBasicPolicySurplusLinesTax
	case app_enums.IndicationOptionTagStandard:
		return m.PhysStandardPolicySurplusLinesTax
	case app_enums.IndicationOptionTagComplete:
		return m.PhysCompletePolicySurplusLinesTax
	default:
		return m.PhysStandardPolicySurplusLinesTax
	}
}

func (m *ModelOutput) GetAPDPolicyStampingFee() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.PhysBasicPolicyStampingFee
	case app_enums.IndicationOptionTagStandard:
		return m.PhysStandardPolicyStampingFee
	case app_enums.IndicationOptionTagComplete:
		return m.PhysCompletePolicyStampingFee
	default:
		return m.PhysStandardPolicyStampingFee
	}
}

func (m *ModelOutput) GetGLPolicySurplusLinesTax() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.GlBasicPolicySurplusLinesTax
	case app_enums.IndicationOptionTagStandard:
		return m.GlStandardPolicySurplusLinesTax
	case app_enums.IndicationOptionTagComplete:
		return m.GlCompletePolicySurplusLinesTax
	default:
		return m.GlStandardPolicySurplusLinesTax
	}
}

func (m *ModelOutput) GetGLPolicyStampingFee() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.GlBasicPolicyStampingFee
	case app_enums.IndicationOptionTagStandard:
		return m.GlStandardPolicyStampingFee
	case app_enums.IndicationOptionTagComplete:
		return m.GlCompletePolicyStampingFee
	default:
		return m.GlStandardPolicyStampingFee
	}
}

func (m *ModelOutput) GetMTCPolicySurplusLinesTax() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.MtcBasicPolicySurplusLinesTax
	case app_enums.IndicationOptionTagStandard:
		return m.MtcStandardPolicySurplusLinesTax
	case app_enums.IndicationOptionTagComplete:
		return m.MtcCompletePolicySurplusLinesTax
	default:
		return m.MtcStandardPolicySurplusLinesTax
	}
}

func (m *ModelOutput) GetMTCPolicyStampingFee() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.MtcBasicPolicyStampingFee
	case app_enums.IndicationOptionTagStandard:
		return m.MtcStandardPolicyStampingFee
	case app_enums.IndicationOptionTagComplete:
		return m.MtcCompletePolicyStampingFee
	default:
		return m.MtcStandardPolicyStampingFee
	}
}

func (m *ModelOutput) GetTotalPolicyPremiumUnmodified() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.TotalBasicPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagStandard:
		return m.TotalStandardPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagComplete:
		return m.TotalCompletePolicyPremiumUnmodified
	default:
		return m.TotalStandardPolicyPremiumUnmodified
	}
}

func (m *ModelOutput) GetLiabilityPolicyPremiumUnmodified() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.LiabBasicPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagStandard:
		return m.LiabStandardPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagComplete:
		return m.LiabCompletePolicyPremiumUnmodified
	default:
		return m.LiabStandardPolicyPremiumUnmodified
	}
}

func (m *ModelOutput) GetPhysicalPolicyPremiumUnmodified() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.PhysBasicPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagStandard:
		return m.PhysStandardPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagComplete:
		return m.PhysCompletePolicyPremiumUnmodified
	default:
		return m.PhysStandardPolicyPremiumUnmodified
	}
}

func (m *ModelOutput) GetGLPolicyPremiumUnmodified() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.GlBasicPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagStandard:
		return m.GlStandardPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagComplete:
		return m.GlCompletePolicyPremiumUnmodified
	default:
		return m.GlStandardPolicyPremiumUnmodified
	}
}

func (m *ModelOutput) GetMtcPolicyPremiumUnmodified() *float64 {
	switch m.PackageType { //nolint:exhaustive
	case app_enums.IndicationOptionTagBasic:
		return m.MtcBasicPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagStandard:
		return m.MtcStandardPolicyPremiumUnmodified
	case app_enums.IndicationOptionTagComplete:
		return m.MtcCompletePolicyPremiumUnmodified
	default:
		return m.MtcStandardPolicyPremiumUnmodified
	}
}

func (m *ModelOutput) GetRateMLArtifact() *artifacts.RateMLArtifact {
	return m.RateMLArtifact
}

// Basic package

func (m *NewModelOutputBasicPackage) GetTotalPolicyPremium() float64 {
	return m.TotalBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetLiabPolicyPremium() float64 {
	return m.LiabBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetLiabPolicyPremiumPpu() float64 {
	return m.LiabBasicPolicyPremiumPpu
}

func (m *NewModelOutputBasicPackage) GetPhysPolicyPremium() float64 {
	return m.PhysBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetPhysPolicyPremiumPtiv() float64 {
	return m.PhysBasicPolicyPremiumPtiv
}

func (m *NewModelOutputBasicPackage) GetGlPolicyPremium() float64 {
	return m.GlBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetMtcPolicyPremium() float64 {
	return m.MtcBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetMtcPolicyPremiumPpu() float64 {
	return m.MtcBasicPolicyPremiumPpu
}

func (m *NewModelOutputBasicPackage) GetFlatPolicyPremium() float64 {
	return m.FlatBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetNonFlatLiabPolicyPremium() float64 {
	return m.NonFlatLiabBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetNonFlatLiabPolicyPremiumPpu() float64 {
	return m.NonFlatLiabBasicPolicyPremiumPpu
}

func (m *NewModelOutputBasicPackage) GetNonFlatPhysPolicyPremium() float64 {
	return m.NonFlatPhysBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetNonFlatPhysPolicyPremiumPtiv() float64 {
	return m.NonFlatPhysBasicPolicyPremiumPtiv
}

func (m *NewModelOutputBasicPackage) GetNegotiatedNonFlatLiabPolicyPremium() float64 {
	return m.NegotiatedNonFlatLiabBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetNegotiatedNonFlatPhysPolicyPremium() float64 {
	return m.NegotiatedNonFlatPhysBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetTraditionalNonFlatLiabPolicyPremium() float64 {
	return m.TraditionalNonFlatLiabBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetTraditionalNonFlatPhysPolicyPremium() float64 {
	return m.TraditionalNonFlatPhysBasicPolicyPremium
}

func (m *NewModelOutputBasicPackage) GetCollisionPremium() *float64 {
	return m.CoverageFinalModPremiumCollBasic
}

func (m *NewModelOutputBasicPackage) GetComprehensivePremium() *float64 {
	return m.CoverageFinalModPremiumCompBasic
}

func (m *NewModelOutputBasicPackage) GetTotalSurchargePremium() *float64 {
	return m.TotalBasicSurchargePremium
}

func (m *NewModelOutputBasicPackage) GetTotalSurpTaxandStampFee() *float64 {
	return m.TotalBasicSurpTaxandStampFee
}

func (m *NewModelOutputBasicPackage) GetTotalSurchargePremiumKy() *float64 {
	return m.TotalBasicSurchargePremiumKy
}

func (m *NewModelOutputBasicPackage) GetTotalInsuranceSurchargePremium() *float64 {
	return m.TotalBasicInsurancePremSurcharge
}

func (m *NewModelOutputBasicPackage) GetAutoInsuranceSurchargePremium() *float64 {
	return m.AutoBasicInsurancePremSurcharge
}

func (m *NewModelOutputBasicPackage) GetGLInsuranceSurchargePremium() *float64 {
	return m.GlBasicInsurancePremSurcharge
}

func (m *NewModelOutputBasicPackage) GetMTCInsuranceSurchargePremium() *float64 {
	return m.MtcBasicInsurancePremSurcharge
}

func (m *NewModelOutputBasicPackage) GetAutoLGPTSurchargePremium() *float64 {
	return m.AutoBasicLGPTSurcharge
}

func (m *NewModelOutputBasicPackage) GetGLLGPTSurchargePremium() *float64 {
	return m.GlBasicLGPTSurcharge
}

func (m *NewModelOutputBasicPackage) GetMTCLGPTSurchargePremium() *float64 {
	return m.MtcBasicLGPTSurcharge
}

func (m *NewModelOutputBasicPackage) GetALPolicySurplusLinesTax() *float64 {
	return m.LiabBasicPolicySurplusLinesTax
}

func (m *NewModelOutputBasicPackage) GetALPolicyStampingFee() *float64 {
	return m.LiabBasicPolicyStampingFee
}

func (m *NewModelOutputBasicPackage) GetAPDPolicySurplusLinesTax() *float64 {
	return m.PhysBasicPolicySurplusLinesTax
}

func (m *NewModelOutputBasicPackage) GetAPDPolicyStampingFee() *float64 {
	return m.PhysBasicPolicyStampingFee
}

func (m *NewModelOutputBasicPackage) GetGLPolicySurplusLinesTax() *float64 {
	return m.GlBasicPolicySurplusLinesTax
}

func (m *NewModelOutputBasicPackage) GetGLPolicyStampingFee() *float64 {
	return m.GlBasicPolicyStampingFee
}

func (m *NewModelOutputBasicPackage) GetMTCPolicySurplusLinesTax() *float64 {
	return m.MtcBasicPolicySurplusLinesTax
}

func (m *NewModelOutputBasicPackage) GetMTCPolicyStampingFee() *float64 {
	return m.MtcBasicPolicyStampingFee
}

// Standard package

func (m *NewModelOutputStandardPackage) GetTotalPolicyPremium() float64 {
	return m.TotalStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetLiabPolicyPremium() float64 {
	return m.LiabStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetLiabPolicyPremiumPpu() float64 {
	return m.LiabStandardPolicyPremiumPpu
}

func (m *NewModelOutputStandardPackage) GetPhysPolicyPremium() float64 {
	return m.PhysStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetPhysPolicyPremiumPtiv() float64 {
	return m.PhysStandardPolicyPremiumPtiv
}

func (m *NewModelOutputStandardPackage) GetGlPolicyPremium() float64 {
	return m.GlStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetMtcPolicyPremium() float64 {
	return m.MtcStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetMtcPolicyPremiumPpu() float64 {
	return m.MtcStandardPolicyPremiumPpu
}

func (m *NewModelOutputStandardPackage) GetFlatPolicyPremium() float64 {
	return m.FlatStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetNonFlatLiabPolicyPremium() float64 {
	return m.NonFlatLiabStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetNonFlatLiabPolicyPremiumPpu() float64 {
	return m.NonFlatLiabStandardPolicyPremiumPpu
}

func (m *NewModelOutputStandardPackage) GetNonFlatPhysPolicyPremium() float64 {
	return m.NonFlatPhysStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetNonFlatPhysPolicyPremiumPtiv() float64 {
	return m.NonFlatPhysStandardPolicyPremiumPtiv
}

func (m *NewModelOutputStandardPackage) GetNegotiatedNonFlatLiabPolicyPremium() float64 {
	return m.NegotiatedNonFlatLiabStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetNegotiatedNonFlatPhysPolicyPremium() float64 {
	return m.NegotiatedNonFlatPhysStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetTraditionalNonFlatLiabPolicyPremium() float64 {
	return m.TraditionalNonFlatLiabStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetTraditionalNonFlatPhysPolicyPremium() float64 {
	return m.TraditionalNonFlatPhysStandardPolicyPremium
}

func (m *NewModelOutputStandardPackage) GetCollisionPremium() *float64 {
	return m.CoverageFinalModPremiumCollStandard
}

func (m *NewModelOutputStandardPackage) GetComprehensivePremium() *float64 {
	return m.CoverageFinalModPremiumCompStandard
}

func (m *NewModelOutputStandardPackage) GetTotalSurchargePremium() *float64 {
	return m.TotalStandardSurchargePremium
}

func (m *NewModelOutputStandardPackage) GetTotalSurpTaxandStampFee() *float64 {
	return m.TotalStandardSurpTaxandStampFee
}

func (m *NewModelOutputStandardPackage) GetTotalSurchargePremiumKy() *float64 {
	return m.TotalStandardSurchargePremiumKy
}

func (m *NewModelOutputStandardPackage) GetTotalInsuranceSurchargePremium() *float64 {
	return m.TotalStandardInsurancePremSurcharge
}

func (m *NewModelOutputStandardPackage) GetAutoInsuranceSurchargePremium() *float64 {
	return m.AutoStandardInsurancePremSurcharge
}

func (m *NewModelOutputStandardPackage) GetGLInsuranceSurchargePremium() *float64 {
	return m.GlStandardInsurancePremSurcharge
}

func (m *NewModelOutputStandardPackage) GetMTCInsuranceSurchargePremium() *float64 {
	return m.MtcStandardInsurancePremSurcharge
}

func (m *NewModelOutputStandardPackage) GetAutoLGPTSurchargePremium() *float64 {
	return m.AutoStandardLGPTSurcharge
}

func (m *NewModelOutputStandardPackage) GetGLLGPTSurchargePremium() *float64 {
	return m.GlStandardLGPTSurcharge
}

func (m *NewModelOutputStandardPackage) GetMTCLGPTSurchargePremium() *float64 {
	return m.MtcStandardLGPTSurcharge
}

func (m *NewModelOutputStandardPackage) GetALPolicySurplusLinesTax() *float64 {
	return m.LiabStandardPolicySurplusLinesTax
}

func (m *NewModelOutputStandardPackage) GetALPolicyStampingFee() *float64 {
	return m.LiabStandardPolicyStampingFee
}

func (m *NewModelOutputStandardPackage) GetAPDPolicySurplusLinesTax() *float64 {
	return m.PhysStandardPolicySurplusLinesTax
}

func (m *NewModelOutputStandardPackage) GetAPDPolicyStampingFee() *float64 {
	return m.PhysStandardPolicyStampingFee
}

func (m *NewModelOutputStandardPackage) GetGLPolicySurplusLinesTax() *float64 {
	return m.GlStandardPolicySurplusLinesTax
}

func (m *NewModelOutputStandardPackage) GetGLPolicyStampingFee() *float64 {
	return m.GlStandardPolicyStampingFee
}

func (m *NewModelOutputStandardPackage) GetMTCPolicySurplusLinesTax() *float64 {
	return m.MtcStandardPolicySurplusLinesTax
}

func (m *NewModelOutputStandardPackage) GetMTCPolicyStampingFee() *float64 {
	return m.MtcStandardPolicyStampingFee
}

// Complete Package

func (m *NewModelOutputCompletePackage) GetTotalPolicyPremium() float64 {
	return m.TotalCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetLiabPolicyPremium() float64 {
	return m.LiabCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetLiabPolicyPremiumPpu() float64 {
	return m.LiabCompletePolicyPremiumPpu
}

func (m *NewModelOutputCompletePackage) GetPhysPolicyPremium() float64 {
	return m.PhysCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetPhysPolicyPremiumPtiv() float64 {
	return m.PhysCompletePolicyPremiumPtiv
}

func (m *NewModelOutputCompletePackage) GetGlPolicyPremium() float64 {
	return m.GlCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetMtcPolicyPremium() float64 {
	return m.MtcCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetMtcPolicyPremiumPpu() float64 {
	return m.MtcCompletePolicyPremiumPpu
}

func (m *NewModelOutputCompletePackage) GetFlatPolicyPremium() float64 {
	return m.FlatCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetNonFlatLiabPolicyPremium() float64 {
	return m.NonFlatLiabCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetNonFlatLiabPolicyPremiumPpu() float64 {
	return m.NonFlatLiabCompletePolicyPremiumPpu
}

func (m *NewModelOutputCompletePackage) GetNonFlatPhysPolicyPremium() float64 {
	return m.NonFlatPhysCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetNonFlatPhysPolicyPremiumPtiv() float64 {
	return m.NonFlatPhysCompletePolicyPremiumPtiv
}

func (m *NewModelOutputCompletePackage) GetNegotiatedNonFlatLiabPolicyPremium() float64 {
	return m.NegotiatedNonFlatLiabCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetNegotiatedNonFlatPhysPolicyPremium() float64 {
	return m.NegotiatedNonFlatPhysCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetTraditionalNonFlatLiabPolicyPremium() float64 {
	return m.TraditionalNonFlatLiabCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetTraditionalNonFlatPhysPolicyPremium() float64 {
	return m.TraditionalNonFlatPhysCompletePolicyPremium
}

func (m *NewModelOutputCompletePackage) GetCollisionPremium() *float64 {
	return m.CoverageFinalModPremiumCollComplete
}

func (m *NewModelOutputCompletePackage) GetComprehensivePremium() *float64 {
	return m.CoverageFinalModPremiumCompComplete
}

func (m *NewModelOutputCompletePackage) GetTotalSurchargePremium() *float64 {
	return m.TotalCompleteSurchargePremium
}

func (m *NewModelOutputCompletePackage) GetTotalSurpTaxandStampFee() *float64 {
	return m.TotalCompleteSurpTaxandStampFee
}

func (m *NewModelOutputCompletePackage) GetTotalSurchargePremiumKy() *float64 {
	return m.TotalCompleteSurchargePremiumKy
}

func (m *NewModelOutputCompletePackage) GetTotalInsuranceSurchargePremium() *float64 {
	return m.TotalCompleteInsurancePremSurcharge
}

func (m *NewModelOutputCompletePackage) GetAutoInsuranceSurchargePremium() *float64 {
	return m.AutoCompleteInsurancePremSurcharge
}

func (m *NewModelOutputCompletePackage) GetGLInsuranceSurchargePremium() *float64 {
	return m.GlCompleteInsurancePremSurcharge
}

func (m *NewModelOutputCompletePackage) GetMTCInsuranceSurchargePremium() *float64 {
	return m.MtcCompleteInsurancePremSurcharge
}

func (m *NewModelOutputCompletePackage) GetAutoLGPTSurchargePremium() *float64 {
	return m.AutoCompleteLGPTSurcharge
}

func (m *NewModelOutputCompletePackage) GetGLLGPTSurchargePremium() *float64 {
	return m.GlCompleteLGPTSurcharge
}

func (m *NewModelOutputCompletePackage) GetMTCLGPTSurchargePremium() *float64 {
	return m.MtcCompleteLGPTSurcharge
}

func (m *NewModelOutputCompletePackage) GetALPolicySurplusLinesTax() *float64 {
	return m.LiabCompletePolicySurplusLinesTax
}

func (m *NewModelOutputCompletePackage) GetALPolicyStampingFee() *float64 {
	return m.LiabCompletePolicyStampingFee
}

func (m *NewModelOutputCompletePackage) GetAPDPolicySurplusLinesTax() *float64 {
	return m.PhysCompletePolicySurplusLinesTax
}

func (m *NewModelOutputCompletePackage) GetAPDPolicyStampingFee() *float64 {
	return m.PhysCompletePolicyStampingFee
}

func (m *NewModelOutputCompletePackage) GetGLPolicySurplusLinesTax() *float64 {
	return m.GlCompletePolicySurplusLinesTax
}

func (m *NewModelOutputCompletePackage) GetGLPolicyStampingFee() *float64 {
	return m.GlCompletePolicyStampingFee
}

func (m *NewModelOutputCompletePackage) GetMTCPolicySurplusLinesTax() *float64 {
	return m.MtcCompletePolicySurplusLinesTax
}

func (m *NewModelOutputCompletePackage) GetMTCPolicyStampingFee() *float64 {
	return m.MtcCompletePolicyStampingFee
}
