load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "common",
    srcs = [
        "fx.go",
        "model_output.go",
        "model_output_get_charges.go",
        "model_output_get_plugins_data.go",
        "model_output_get_program_specific_metadata.go",
        "output_creator.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/common",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/adaptors/fleet_adaptor/entities",
        "//nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions",
        "//nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/outputs_quote",
        "//nirvana/rating/artifacts",
        "//nirvana/rating/pricing/api/engine/common",
        "//nirvana/rating/pricing/api/engine/plugins/impls/common/rateml_artifact_upload_v1/rateml_artifact_upload_v1_data",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/rateml/program",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_jinzhu_copier//:copier",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "common_test",
    srcs = [
        "model_output_get_charges_test.go",
        "model_output_get_plugins_data_test.go",
        "model_output_get_program_specific_metadata_test.go",
    ],
    embed = [":common"],
    deps = [
        "//nirvana/rating/artifacts",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_stretchr_testify//require",
    ],
)
