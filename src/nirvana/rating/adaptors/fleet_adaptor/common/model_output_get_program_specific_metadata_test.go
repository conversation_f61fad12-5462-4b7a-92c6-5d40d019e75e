package common

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestGetProgramSpecificMetadata(t *testing.T) {
	mo := ModelOutput{}

	m, err := mo.GetProgramSpecificMetadata()
	metadata := m.(*ptypes.ChunkOutput_Metadata_FleetChunkOutputMetadata)
	require.NoError(t, err)
	require.Equal(t, &ptypes.Fleet_ChunkOutputMetadata{}, metadata.FleetChunkOutputMetadata)
}
