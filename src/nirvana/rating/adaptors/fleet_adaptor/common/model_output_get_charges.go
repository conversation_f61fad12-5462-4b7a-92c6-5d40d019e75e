package common

import (
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// The GetCharges method is needed so the model output implements the engine.ChunkOutputCreatorI.
//
// It's important to be aware that a new ModelOutput implementations should be added if
// the logic to create charges changes in a non-backward compatible way. For instance:
//   - OK: add charges that would never be created for old model versions.
//   - NOT OK: change the way in which an existing charge's premium is calculated.
func (m *ModelOutput) GetCharges() ([]*ptypes.Charge, error) {
	charges := make([]*ptypes.Charge, 0)
	return charges, nil
}
