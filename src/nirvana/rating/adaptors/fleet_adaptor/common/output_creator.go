package common

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jinzhu/copier"

	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/outputs_quote"
	"nirvanatech.com/nirvana/rating/artifacts"
	"nirvanatech.com/nirvana/rating/rateml/program"
)

func NewOutputCreatorFn() common.OutputCreatorFn[creator_functions.Input] {
	return func(
		_ context.Context,
		pc *common.ProgramContext,
		prog *program.Program,
		input *creator_functions.Input,
	) (any, error) {
		var retval ModelOutput

		e := pc.GetEntity(common.EntityTypeOutputsQuote, outputs_quote.Id)
		outputsQuote := e.(*entities.OutputsQuote)

		// Base fields
		if err := copier.Copy(&retval, outputsQuote); err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to copy outputs quote (%+v) into new model output impl (%+v)",
				outputsQuote,
				retval,
			)
		}

		if input == nil {
			return nil, errors.Newf("input is nil")
		}

		mi := input.ModelInput
		if mi == nil {
			return nil, errors.Newf("model input is nil")
		}

		extraPricingInfo := mi.ExtraPricingInfo
		if extraPricingInfo == nil {
			return nil, errors.Newf("extraPricingInfo is nil")
		}

		retval.PackageType = extraPricingInfo.PackageType

		retval.RateMLArtifact = artifacts.NewRateMLArtifact(
			pc.ModelKey,
			prog,
			mi.CompanyInfo.DOTNumber,
		)

		retval.ProjectedMiles = int64(mi.CompanyInfo.ProjectedMileage)

		retval.Input = input

		return &retval, nil
	}
}
